/**
 * <PERSON><PERSON>hamApp Data Converter - Node.js Version
 * Simple Excel to JSON converter
 */

const XLSX = require('xlsx');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

function convertArhamData(filePath) {
    try {
        console.log('🔄 Reading Excel file...');
        
        // Read Excel file
        const workbook = XLSX.readFile(filePath);
        const sheetNames = workbook.SheetNames;
        
        console.log(`📋 Found ${sheetNames.length} sheets: ${sheetNames.join(', ')}`);
        
        const allData = {};
        const hierarchy = {
            arhamApp: {
                navigation: {},
                content: {},
                metadata: {
                    totalSheets: sheetNames.length,
                    sheetNames: sheetNames,
                    createdAt: Date.now()
                }
            }
        };
        
        // Process each sheet
        sheetNames.forEach((sheetName, index) => {
            console.log(`🔄 Processing Sheet ${index + 1}: ${sheetName}`);
            
            const worksheet = workbook.Sheets[sheetName];
            const jsonData = XLSX.utils.sheet_to_json(worksheet);
            
            console.log(`   Found ${jsonData.length} rows`);
            
            // Store raw data
            allData[sheetName] = jsonData;
            
            // Process based on sheet index (hierarchy level)
            processSheetData(hierarchy, jsonData, sheetName, index);
        });
        
        // Save multiple formats
        saveResults(hierarchy, allData);
        
        console.log('🎉 Conversion completed successfully!');
        return true;
        
    } catch (error) {
        console.error('❌ Error:', error.message);
        return false;
    }
}

function processSheetData(hierarchy, data, sheetName, level) {
    const levelTypes = ['exploreScreen', 'section', 'category', 'author', 'title'];
    const levelType = levelTypes[level] || `level_${level}`;
    
    data.forEach(row => {
        const keys = Object.keys(row);
        if (keys.length === 0) return;
        
        const mainKey = keys[0];
        const name = String(row[mainKey] || '').trim();
        
        if (!name) return;
        
        const item = {
            id: generateId(name),
            name: name,
            level: level,
            type: levelType,
            metadata: {}
        };
        
        // Add all other fields as metadata
        keys.slice(1).forEach(key => {
            if (row[key] !== undefined && row[key] !== null && String(row[key]).trim()) {
                const fieldName = key.toLowerCase().replace(/\s+/g, '_');
                item.metadata[fieldName] = String(row[key]).trim();
            }
        });
        
        item.metadata.createdAt = Date.now();
        
        // Add to appropriate level in hierarchy
        addToHierarchy(hierarchy, item, level);
    });
}

function addToHierarchy(hierarchy, item, level) {
    const nav = hierarchy.arhamApp.navigation;
    
    switch (level) {
        case 0: // Explore Screen
            if (!nav[item.name]) {
                nav[item.name] = {
                    ...item,
                    sections: {}
                };
            }
            break;
            
        case 1: // Section
            const exploreScreens = Object.keys(nav);
            if (exploreScreens.length > 0) {
                const parentScreen = exploreScreens[0];
                if (!nav[parentScreen].sections[item.name]) {
                    nav[parentScreen].sections[item.name] = {
                        ...item,
                        categories: {}
                    };
                }
            }
            break;
            
        case 2: // Category
            for (const screenName in nav) {
                const sections = Object.keys(nav[screenName].sections);
                if (sections.length > 0) {
                    const parentSection = sections[0];
                    if (!nav[screenName].sections[parentSection].categories) {
                        nav[screenName].sections[parentSection].categories = {};
                    }
                    if (!nav[screenName].sections[parentSection].categories[item.name]) {
                        nav[screenName].sections[parentSection].categories[item.name] = {
                            ...item,
                            authors: {}
                        };
                    }
                    break;
                }
            }
            break;
            
        case 3: // Author
            for (const screenName in nav) {
                for (const sectionName in nav[screenName].sections) {
                    const categories = Object.keys(nav[screenName].sections[sectionName].categories || {});
                    if (categories.length > 0) {
                        const parentCategory = categories[0];
                        const categoryObj = nav[screenName].sections[sectionName].categories[parentCategory];
                        if (!categoryObj.authors) categoryObj.authors = {};
                        if (!categoryObj.authors[item.name]) {
                            categoryObj.authors[item.name] = {
                                ...item,
                                titles: {}
                            };
                        }
                        return;
                    }
                }
            }
            break;
            
        case 4: // Title (Content)
            // Add to content collection
            hierarchy.arhamApp.content[item.id] = {
                ...item,
                isLeaf: true
            };
            
            // Also add to hierarchy
            for (const screenName in nav) {
                for (const sectionName in nav[screenName].sections) {
                    for (const categoryName in nav[screenName].sections[sectionName].categories || {}) {
                        const authors = nav[screenName].sections[sectionName].categories[categoryName].authors || {};
                        const authorNames = Object.keys(authors);
                        if (authorNames.length > 0) {
                            const parentAuthor = authorNames[0];
                            if (!authors[parentAuthor].titles) authors[parentAuthor].titles = {};
                            authors[parentAuthor].titles[item.name] = item;
                            return;
                        }
                    }
                }
            }
            break;
    }
}

function generateId(name) {
    return name.toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/[-\s]+/g, '_') + '_' + uuidv4().substring(0, 8);
}

function saveResults(hierarchy, allData) {
    // 1. Full hierarchy
    fs.writeFileSync('arham_hierarchy.json', JSON.stringify(hierarchy, null, 2), 'utf8');
    console.log('✅ Saved: arham_hierarchy.json');
    
    // 2. Navigation only
    const navOnly = { navigation: hierarchy.arhamApp.navigation };
    fs.writeFileSync('arham_navigation.json', JSON.stringify(navOnly, null, 2), 'utf8');
    console.log('✅ Saved: arham_navigation.json');
    
    // 3. Content only
    const contentOnly = { content: hierarchy.arhamApp.content };
    fs.writeFileSync('arham_content.json', JSON.stringify(contentOnly, null, 2), 'utf8');
    console.log('✅ Saved: arham_content.json');
    
    // 4. Flat for Firestore
    const flatData = Object.values(hierarchy.arhamApp.content).map(item => ({
        id: item.id,
        title: item.name,
        content: JSON.stringify(item.metadata),
        level: item.level,
        type: item.type,
        createdAt: Date.now(),
        updatedAt: Date.now()
    }));
    
    fs.writeFileSync('arham_firestore.json', JSON.stringify(flatData, null, 2), 'utf8');
    console.log('✅ Saved: arham_firestore.json');
    
    // 5. Raw data
    fs.writeFileSync('arham_raw_data.json', JSON.stringify(allData, null, 2), 'utf8');
    console.log('✅ Saved: arham_raw_data.json');
}

// Command line usage
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('Usage: node arham_converter_nodejs.js <excel_file>');
        console.log('Example: node arham_converter_nodejs.js data.xlsx');
        process.exit(1);
    }
    
    const filePath = args[0];
    
    if (!fs.existsSync(filePath)) {
        console.error(`❌ File not found: ${filePath}`);
        process.exit(1);
    }
    
    console.log('🔄 ArhamApp Data Converter (Node.js)');
    console.log('='.repeat(50));
    
    convertArhamData(filePath);
}

module.exports = { convertArhamData };
