package com.example.arham.ui.components

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Brightness2
import androidx.compose.material.icons.filled.WbSunny
import androidx.compose.material3.Icon
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.unit.dp

/**
 * A customizable theme toggle button that switches between dark and light modes.
 * It displays a sun icon for light mode and a moon icon for dark mode, with a rotation animation
 * during the transition.
 *
 * @param isDarkMode A boolean state indicating whether the dark theme is currently active.
 * @param onToggle A lambda function that is invoked when the button is clicked,
 *                 triggering the theme change.
 */
@Composable
fun ThemeToggleButton(
    isDarkMode: Boolean,
    onToggle: () -> Unit
) {
    // Animate the rotation of the icon based on the current theme mode.
    // The icon rotates 180 degrees when switching to dark mode and back to 0 for light mode.
    val rotation by animateFloatAsState(
        targetValue = if (isDarkMode) 180f else 0f,
        animationSpec = tween(durationMillis = 500) // Animation duration of 500 milliseconds.
    )

    // The main container for the toggle button.
    Box(
        modifier = Modifier
            .size(48.dp) // Sets the size of the button.
            .clip(CircleShape) // Clips the button to a circular shape.
            // Sets the background color based on the theme mode.
            .background(if (isDarkMode) Color.DarkGray.copy(alpha = 0.4f) else Color.Blue.copy(alpha = 0.1f))
            // Makes the Box clickable and handles the theme toggle.
            // `indication = null` removes the visual ripple effect on click.
            // `MutableInteractionSource()` is used to manage interaction states.
            .clickable(
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            ) { onToggle() },
        contentAlignment = Alignment.Center // Centers the content (icon) within the Box.
    ) {
        // Displays the appropriate icon (moon for dark, sun for light) based on the theme mode.
        Icon(
            imageVector = if (isDarkMode) Icons.Default.Brightness2 else Icons.Default.WbSunny,
            contentDescription = "Toggle Theme", // Accessibility description for the icon.
            tint = if (isDarkMode) Color.White else Color.Black, // Sets the icon color based on the theme.
            modifier = Modifier.graphicsLayer {
                rotationZ = rotation // Applies the animated rotation to the icon.
            }
        )
    }
}