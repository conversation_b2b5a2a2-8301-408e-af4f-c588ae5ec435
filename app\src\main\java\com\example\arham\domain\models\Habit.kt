package com.example.arham.domain.models

/**
 * Domain model for habit tracking
 * Clean architecture - Domain layer entity
 */
data class Habit(
    val id: String,
    val name: String,
    val description: String,
    val category: HabitCategory,
    val targetCount: Int = 1,
    val unit: String = "times",
    val color: String = "#FF6B35",
    val icon: String = "🙏",
    val isActive: Boolean = true,
    val createdAt: Long = System.currentTimeMillis(),
    val streak: Int = 0,
    val bestStreak: Int = 0,
    val totalCompletions: Int = 0
)

data class HabitCompletion(
    val id: String,
    val habitId: String,
    val date: String, // Format: "yyyy-MM-dd"
    val count: Int = 1,
    val completedAt: Long = System.currentTimeMillis(),
    val notes: String = ""
)

enum class HabitCategory {
    PRAYER, MEDITATION, READING, CHANTING, SERVICE, STUDY, FASTING, OTHER
}

/**
 * UI State for habit tracking
 */
data class HabitWithCompletions(
    val habit: Habit,
    val completions: List<HabitCompletion> = emptyList(),
    val todayCompletion: HabitCompletion? = null,
    val currentStreak: Int = 0
)
