/**
 * Excel to JSON Converter for ArhamApp
 * Node.js script to convert Excel/CSV to Firestore-ready JSON
 */

const XLSX = require('xlsx');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

function excelToJson(filePath, outputPath) {
    try {
        console.log('🔄 Reading Excel file...');
        
        // Read Excel file
        const workbook = XLSX.readFile(filePath);
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        
        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        
        console.log(`✅ Found ${jsonData.length} rows`);
        
        // Transform for Firestore
        const firestoreData = jsonData.map(row => {
            const doc = {
                id: uuidv4(),
                createdAt: Date.now(),
                updatedAt: Date.now()
            };
            
            // Add all fields
            Object.keys(row).forEach(key => {
                const fieldName = key.trim().replace(/\s+/g, '_').toLowerCase();
                let value = row[key];
                
                // Clean value
                if (value === null || value === undefined) {
                    value = '';
                } else if (typeof value === 'string') {
                    value = value.trim();
                }
                
                doc[fieldName] = value;
            });
            
            return doc;
        });
        
        // Save JSON
        const output = outputPath || 'firestore_data.json';
        fs.writeFileSync(output, JSON.stringify(firestoreData, null, 2), 'utf8');
        
        console.log(`🎉 JSON file created: ${output}`);
        console.log(`📊 Total documents: ${firestoreData.length}`);
        
        // Show sample
        if (firestoreData.length > 0) {
            console.log('\n📋 Sample document:');
            console.log(JSON.stringify(firestoreData[0], null, 2));
        }
        
        return output;
        
    } catch (error) {
        console.error('❌ Error:', error.message);
        return null;
    }
}

function createSpiritualContentJson(filePath) {
    try {
        const workbook = XLSX.readFile(filePath);
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        
        const spiritualContent = jsonData.map(row => {
            const content = {
                id: uuidv4(),
                title: (row.title || '').toString().trim(),
                content: (row.content || '').toString().trim(),
                author: (row.author || '').toString().trim(),
                category: (row.category || 'GENERAL').toString().toUpperCase(),
                type: (row.type || 'TEACHING').toString().toUpperCase(),
                language: (row.language || 'HINDI').toString().toUpperCase(),
                tags: row.tags ? row.tags.toString().split(',').map(t => t.trim()) : [],
                searchKeywords: [],
                isPopular: Boolean(row.isPopular),
                viewCount: parseInt(row.viewCount) || 0,
                createdAt: Date.now(),
                updatedAt: Date.now()
            };
            
            // Generate search keywords
            const keywords = new Set();
            content.title.toLowerCase().split(' ').forEach(word => keywords.add(word));
            content.author.toLowerCase().split(' ').forEach(word => keywords.add(word));
            content.tags.forEach(tag => keywords.add(tag.toLowerCase()));
            content.searchKeywords = Array.from(keywords).filter(k => k.length > 2);
            
            return content;
        });
        
        const output = 'spiritual_content_firestore.json';
        fs.writeFileSync(output, JSON.stringify(spiritualContent, null, 2), 'utf8');
        
        console.log(`🎉 Spiritual Content JSON created: ${output}`);
        console.log(`📊 Total content: ${spiritualContent.length}`);
        
        return output;
        
    } catch (error) {
        console.error('❌ Error:', error.message);
        return null;
    }
}

// Command line usage
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('Usage: node excel-to-json.js <excel_file_path> [output_path]');
        console.log('Example: node excel-to-json.js data.xlsx');
        process.exit(1);
    }
    
    const filePath = args[0];
    const outputPath = args[1];
    
    if (!fs.existsSync(filePath)) {
        console.error(`❌ File not found: ${filePath}`);
        process.exit(1);
    }
    
    console.log('🔄 Excel to JSON Converter for ArhamApp');
    console.log('='.repeat(50));
    
    // Ask for conversion type
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    
    console.log('\nSelect conversion type:');
    console.log('1. General Excel to JSON');
    console.log('2. Spiritual Content format');
    
    rl.question('Enter choice (1 or 2): ', (choice) => {
        if (choice === '1') {
            excelToJson(filePath, outputPath);
        } else if (choice === '2') {
            createSpiritualContentJson(filePath);
        } else {
            console.log('❌ Invalid choice');
        }
        rl.close();
    });
}

module.exports = { excelToJson, createSpiritualContentJson };
