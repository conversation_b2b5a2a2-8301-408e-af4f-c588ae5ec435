<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArhamApp Firestore Upload</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
            display: none;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 ArhamApp Firestore Upload</h1>
        <p>Upload your JSON data to Firebase Firestore</p>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 Drag and drop your JSON file here or click to select</p>
            <input type="file" id="fileInput" accept=".json" style="display: none;">
        </div>
        
        <div>
            <label for="collectionName">Collection Name:</label>
            <input type="text" id="collectionName" value="spiritual_content" style="margin-left: 10px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
        </div>
        
        <div style="margin: 20px 0;">
            <label for="batchSize">Batch Size:</label>
            <input type="number" id="batchSize" value="500" min="1" max="500" style="margin-left: 10px; padding: 8px; border: 1px solid #ccc; border-radius: 4px; width: 80px;">
        </div>
        
        <button id="uploadBtn" disabled>Upload to Firestore</button>
        <button id="previewBtn" disabled>Preview Data</button>
        
        <div class="progress" id="progressContainer">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore, collection, doc, setDoc, writeBatch } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Firebase configuration - REPLACE WITH YOUR CONFIG
        const firebaseConfig = {
            apiKey: "your-api-key",
            authDomain: "your-project.firebaseapp.com",
            projectId: "your-project-id",
            storageBucket: "your-project.appspot.com",
            messagingSenderId: "123456789",
            appId: "your-app-id"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        let jsonData = null;

        // DOM elements
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const uploadBtn = document.getElementById('uploadBtn');
        const previewBtn = document.getElementById('previewBtn');
        const log = document.getElementById('log');
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');
        const collectionNameInput = document.getElementById('collectionName');
        const batchSizeInput = document.getElementById('batchSize');

        // File upload handling
        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        function handleFile(file) {
            if (!file.name.endsWith('.json')) {
                logMessage('❌ Please select a JSON file', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    jsonData = JSON.parse(e.target.result);
                    logMessage(`✅ File loaded: ${file.name}`, 'success');
                    logMessage(`📊 Found ${jsonData.length} items`, 'info');
                    uploadBtn.disabled = false;
                    previewBtn.disabled = false;
                } catch (error) {
                    logMessage(`❌ Invalid JSON file: ${error.message}`, 'error');
                }
            };
            reader.readAsText(file);
        }

        // Preview data
        previewBtn.addEventListener('click', () => {
            if (jsonData && jsonData.length > 0) {
                logMessage('📋 Preview of first item:', 'info');
                logMessage(JSON.stringify(jsonData[0], null, 2), 'info');
            }
        });

        // Upload to Firestore
        uploadBtn.addEventListener('click', async () => {
            if (!jsonData) {
                logMessage('❌ No data to upload', 'error');
                return;
            }

            const collectionName = collectionNameInput.value.trim() || 'spiritual_content';
            const batchSize = parseInt(batchSizeInput.value) || 500;

            uploadBtn.disabled = true;
            progressContainer.style.display = 'block';
            
            try {
                await uploadData(jsonData, collectionName, batchSize);
                logMessage('🎉 Upload completed successfully!', 'success');
            } catch (error) {
                logMessage(`❌ Upload failed: ${error.message}`, 'error');
            } finally {
                uploadBtn.disabled = false;
                progressContainer.style.display = 'none';
            }
        });

        async function uploadData(data, collectionName, batchSize) {
            const totalItems = data.length;
            let uploadedItems = 0;

            for (let i = 0; i < totalItems; i += batchSize) {
                const batch = writeBatch(db);
                const batchData = data.slice(i, i + batchSize);

                logMessage(`🔄 Uploading batch ${Math.floor(i/batchSize) + 1} (${batchData.length} items)...`, 'info');

                for (const item of batchData) {
                    const docRef = doc(collection(db, collectionName), item.id);
                    batch.set(docRef, item);
                }

                await batch.commit();
                uploadedItems += batchData.length;

                // Update progress
                const progress = (uploadedItems / totalItems) * 100;
                progressBar.style.width = `${progress}%`;
                
                logMessage(`✅ Uploaded ${uploadedItems}/${totalItems} items`, 'success');
            }
        }

        function logMessage(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.textContent = `[${timestamp}] ${message}`;
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }

        // Initial log message
        logMessage('🔥 ArhamApp Firestore Upload Tool Ready', 'info');
        logMessage('⚠️ Make sure to update Firebase configuration in the code', 'error');
    </script>
</body>
</html>
