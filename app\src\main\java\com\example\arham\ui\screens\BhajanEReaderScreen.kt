package com.example.arham.ui.screens

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.unit.offset
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.view.WindowCompat
import androidx.navigation.NavController
import android.app.Activity
import com.example.arham.ui.theme.eczarFamily
import com.example.arham.data.BhajanContent
import kotlinx.coroutines.tasks.await

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BhajanEReaderScreen(
    navController: NavController,
    title: String,
    isDarkMode: Boolean = false
) {
    val clipboardManager = LocalClipboardManager.current
    val listState = rememberLazyListState()
    val view = LocalView.current
    val context = LocalContext.current

    // Start with clean screen - hidden back button bar
    var showTopBar by remember { mutableStateOf(false) }

    // Smooth scroll-based animations
    val firstVisibleItemIndex = listState.firstVisibleItemIndex
    val firstVisibleItemScrollOffset = listState.firstVisibleItemScrollOffset

    // Calculate synchronized fade progress - slower, more controlled
    val headerFadeProgress = remember(firstVisibleItemIndex, firstVisibleItemScrollOffset) {
        when {
            firstVisibleItemIndex > 0 -> 1f
            firstVisibleItemIndex == 0 -> (firstVisibleItemScrollOffset / 400f).coerceIn(0f, 1f)
            else -> 0f
        }
    }

    // Slower, more synchronized animations
    val headerAlpha by animateFloatAsState(
        targetValue = 1f - headerFadeProgress,
        animationSpec = tween(durationMillis = 600, easing = LinearEasing),
        label = "headerAlpha"
    )

    // Top bar title fades in perfectly synchronized with content fade out
    val topBarTitleAlpha by animateFloatAsState(
        targetValue = if (showTopBar) headerFadeProgress else 0f,
        animationSpec = tween(durationMillis = 600, easing = LinearEasing),
        label = "topBarTitleAlpha"
    )

    // Animated top padding for smooth content transition
    val contentTopPadding by animateDpAsState(
        targetValue = if (showTopBar) 70.dp else 0.dp,
        animationSpec = tween(durationMillis = 300, easing = FastOutSlowInEasing),
        label = "contentTopPadding"
    )

    // Top bar stays visible until manually tapped again - no auto-hide
    
    var isBookmarked by remember { mutableStateOf(false) }
    var showOptionsMenu by remember { mutableStateOf(false) }
    var showFontOptions by remember { mutableStateOf(false) }

    // Font and theme settings
    var fontSize by remember { mutableStateOf(26.sp) }
    var selectedTheme by remember { mutableStateOf(2) } // Default to dark theme (index 2)
    
    // Content loading with Firebase fallback
    var content by remember { mutableStateOf("") }
    var author by remember { mutableStateOf("") }
    var category by remember { mutableStateOf("") }
    var displayTitle by remember { mutableStateOf(title) } // Use this for display
    var isLoading by remember { mutableStateOf(true) }

    // Load content from static data or Firebase
    LaunchedEffect(title) {
        try {
            // First try static content
            val staticContent = BhajanContent.getBhajanContent(title)
            if (staticContent.isNotEmpty()) {
                content = staticContent
                author = BhajanContent.getBhajanAuthor(title)
                category = BhajanContent.getBhajanCategory(title)
                displayTitle = title // Keep original title for static content
            } else {
                // If title starts with "content_", it's from Firebase
                if (title.startsWith("content_")) {
                    // Load from Firebase using the full document ID
                    val db = com.google.firebase.firestore.FirebaseFirestore.getInstance()
                    val docId = title // Use the full title as document ID
                    android.util.Log.d("ArhamApp", "Loading Firebase content for docId: $docId")
                    val doc = db.collection("spiritual_reading").document(docId).get().await()

                    if (doc.exists()) {
                        content = doc.getString("content") ?: "Content not found"
                        author = doc.getString("author") ?: "Unknown Author"
                        category = "दैनिक स्वाध्याय" // Set proper Hindi category
                        displayTitle = doc.getString("title") ?: "Unknown Title"
                        android.util.Log.d("ArhamApp", "Loaded content: title='$displayTitle', author='$author', content length=${content.length}")
                    } else {
                        content = "Document not found"
                        author = "Unknown Author"
                        category = "Error"
                        displayTitle = "Content Not Found"
                        android.util.Log.d("ArhamApp", "Document not found: $docId")
                    }
                } else {
                    // Fallback for other titles
                    content = "Content not available for: $title"
                    author = "Unknown Author"
                    category = "General"
                    displayTitle = title
                }
            }
        } catch (e: Exception) {
            content = "Error loading content: ${e.message}"
            author = "Error"
            category = "Error"
        } finally {
            isLoading = false
        }
    }

    // Theme colors
    val themeColors = remember {
        listOf(
            Triple(Color.White, Color.Black, "Light"), // Light theme
            Triple(Color(0xFFF5F1E8), Color(0xFF5D4E37), "Sepia"), // Sepia theme
            Triple(Color(0xFF1E1E1E), Color.White, "Dark"), // Dark theme
            Triple(Color.Black, Color.White, "Black") // Black theme
        )
    }

    val currentTheme = themeColors[selectedTheme]

    // Update system bars to match E-Reader theme
    LaunchedEffect(selectedTheme) {
        if (!view.isInEditMode) {
            val window = (context as Activity).window
            val backgroundColor = currentTheme.first.toArgb()
            val isLightTheme = selectedTheme == 0 || selectedTheme == 1 // Light or Sepia

            window.statusBarColor = backgroundColor
            window.navigationBarColor = backgroundColor
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = isLightTheme
            WindowCompat.getInsetsController(window, view).isAppearanceLightNavigationBars = isLightTheme
        }
    }

    // Reset system bars when leaving E-Reader
    DisposableEffect(Unit) {
        onDispose {
            if (!view.isInEditMode) {
                val window = (context as Activity).window
                // Reset to app theme colors
                val appBackgroundColor = if (isDarkMode) Color(0xFF121212).toArgb() else Color.White.toArgb()
                window.statusBarColor = appBackgroundColor
                window.navigationBarColor = appBackgroundColor
                WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = !isDarkMode
                WindowCompat.getInsetsController(window, view).isAppearanceLightNavigationBars = !isDarkMode
            }
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // Ultra-fast tap detection - no expensive operations
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(currentTheme.first)
                .clickable(
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() }
                ) {
                    showTopBar = !showTopBar
                    showOptionsMenu = false // Close menu if open
                    showFontOptions = false // Hide font options when tapping
                }
        ) {
            LazyColumn(
                state = listState,
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(
                    top = contentTopPadding // Smooth animated padding
                )
            ) {
                // Animated header and content
                item {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(24.dp)
                    ) {
                        // Animated Header - fades out on scroll
                        Column(
                            modifier = Modifier.alpha(headerAlpha)
                        ) {
                            Text(
                                text = author,
                                fontSize = 18.sp,
                                color = currentTheme.second.copy(alpha = 0.7f),
                                fontFamily = eczarFamily,
                                fontWeight = FontWeight.Medium
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = displayTitle,
                                fontSize = 28.sp,
                                fontWeight = FontWeight.Bold,
                                color = currentTheme.second,
                                fontFamily = eczarFamily,
                                lineHeight = 32.sp
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = category,
                                fontSize = 16.sp,
                                color = currentTheme.second.copy(alpha = 0.8f),
                                fontFamily = eczarFamily,
                                fontWeight = FontWeight.Medium
                            )
                            Spacer(modifier = Modifier.height(16.dp))

                            // Divider line beneath category
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(1.dp)
                                    .background(currentTheme.second.copy(alpha = 0.3f))
                            )
                        }

                        Spacer(modifier = Modifier.height(32.dp))

                        // Main Content - with loading state
                        if (isLoading) {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(200.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                CircularProgressIndicator(
                                    color = currentTheme.second,
                                    modifier = Modifier.size(40.dp)
                                )
                            }
                        } else {
                            SelectionContainer {
                                Text(
                                    text = content,
                                    fontSize = fontSize,
                                    lineHeight = fontSize * 1.6f,
                                    fontFamily = eczarFamily,
                                    fontWeight = FontWeight.Normal,
                                    color = currentTheme.second,
                                    textAlign = TextAlign.End, // Right justify for Hindi text
                                    modifier = Modifier.fillMaxWidth(),
                                    letterSpacing = 0.5.sp
                                )
                            }
                        }

                        Spacer(modifier = Modifier.height(100.dp))
                    }
                }
            }
        }
        
        // Sliding top bar with smooth animations
        AnimatedVisibility(
            visible = showTopBar,
            enter = slideInVertically(
                initialOffsetY = { -it },
                animationSpec = tween(durationMillis = 300, easing = FastOutSlowInEasing)
            ),
            exit = slideOutVertically(
                targetOffsetY = { -it },
                animationSpec = tween(durationMillis = 250, easing = FastOutSlowInEasing)
            ),
            modifier = Modifier.align(Alignment.TopCenter)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(currentTheme.first) // Use current theme background
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 12.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    // Left side - Back arrow
                    IconButton(
                        onClick = { navController.navigateUp() },
                        modifier = Modifier.size(40.dp)
                    ) {
                        Icon(
                            Icons.Filled.ArrowBack,
                            contentDescription = "Back",
                            modifier = Modifier.size(24.dp),
                            tint = currentTheme.second
                        )
                    }

                    // Center - Animated title emergence
                    Box(
                        modifier = Modifier.weight(1f),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = title,
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Normal,
                            color = currentTheme.second,
                            fontFamily = eczarFamily,
                            maxLines = 1,
                            modifier = Modifier.alpha(topBarTitleAlpha),
                            textAlign = TextAlign.Center
                        )
                    }

                    // Right side - Options
                    Row {
                        // Font size button
                        IconButton(
                            onClick = { showFontOptions = !showFontOptions },
                            modifier = Modifier.size(40.dp)
                        ) {
                            Text(
                                text = "Aa",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                color = currentTheme.second
                            )
                        }



                        // More options
                        IconButton(
                            onClick = { showOptionsMenu = true },
                            modifier = Modifier.size(40.dp)
                        ) {
                            Icon(
                                Icons.Default.MoreVert,
                                contentDescription = "More Options",
                                modifier = Modifier.size(20.dp),
                                tint = currentTheme.second
                            )
                        }
                    }
                }

                // Bottom border of top bar
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(1.dp)
                        .background(currentTheme.second.copy(alpha = 0.2f))
                )
            }
        }

        // Font Options Panel
        if (showFontOptions) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(currentTheme.first.copy(alpha = 0.95f))
                    .padding(16.dp)
                    .offset(y = if (showTopBar) 56.dp else 0.dp)
            ) {
                Column {
                    // Font Size Section - Card Style Controls
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Decrease font button
                        Card(
                            onClick = {
                                if (fontSize.value > 14f) fontSize = (fontSize.value - 2f).sp
                            },
                            modifier = Modifier.size(48.dp),
                            shape = RoundedCornerShape(12.dp),
                            colors = CardDefaults.cardColors(
                                containerColor = currentTheme.first.copy(alpha = 0.1f)
                            ),
                            border = BorderStroke(1.dp, currentTheme.second.copy(alpha = 0.3f))
                        ) {
                            Box(
                                modifier = Modifier.fillMaxSize(),
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    Icons.Default.Remove,
                                    contentDescription = "Decrease Font",
                                    modifier = Modifier.size(24.dp),
                                    tint = currentTheme.second
                                )
                            }
                        }

                        Spacer(modifier = Modifier.width(16.dp))

                        // Font size indicator
                        Card(
                            modifier = Modifier.padding(horizontal = 8.dp),
                            shape = RoundedCornerShape(12.dp),
                            colors = CardDefaults.cardColors(
                                containerColor = currentTheme.first.copy(alpha = 0.1f)
                            ),
                            border = BorderStroke(1.dp, currentTheme.second.copy(alpha = 0.3f))
                        ) {
                            Text(
                                text = "Aa",
                                fontSize = 18.sp,
                                color = currentTheme.second,
                                fontWeight = FontWeight.Medium,
                                modifier = Modifier.padding(horizontal = 20.dp, vertical = 12.dp)
                            )
                        }

                        Spacer(modifier = Modifier.width(16.dp))

                        // Increase font button
                        Card(
                            onClick = {
                                if (fontSize.value < 36f) fontSize = (fontSize.value + 2f).sp
                            },
                            modifier = Modifier.size(48.dp),
                            shape = RoundedCornerShape(12.dp),
                            colors = CardDefaults.cardColors(
                                containerColor = currentTheme.first.copy(alpha = 0.1f)
                            ),
                            border = BorderStroke(1.dp, currentTheme.second.copy(alpha = 0.3f))
                        ) {
                            Box(
                                modifier = Modifier.fillMaxSize(),
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    Icons.Default.Add,
                                    contentDescription = "Increase Font",
                                    modifier = Modifier.size(24.dp),
                                    tint = currentTheme.second
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Theme Colors
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        themeColors.forEachIndexed { index, (bgColor, textColor, name) ->
                            Box(
                                modifier = Modifier
                                    .size(40.dp)
                                    .background(
                                        bgColor,
                                        CircleShape
                                    )
                                    .border(
                                        width = if (selectedTheme == index) 3.dp else 1.dp,
                                        color = if (selectedTheme == index) currentTheme.second else currentTheme.second.copy(alpha = 0.3f),
                                        shape = CircleShape
                                    )
                                    .clickable { selectedTheme = index },
                                contentAlignment = Alignment.Center
                            ) {
                                if (selectedTheme == index) {
                                    Icon(
                                        Icons.Default.Check,
                                        contentDescription = "Selected",
                                        tint = textColor,
                                        modifier = Modifier.size(16.dp)
                                    )
                                }
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    // Reset to default button
                    TextButton(
                        onClick = {
                            fontSize = 26.sp
                            selectedTheme = 2 // Reset to dark theme
                        },
                        modifier = Modifier.align(Alignment.CenterHorizontally)
                    ) {
                        Text(
                            text = "Reset to default",
                            color = currentTheme.second.copy(alpha = 0.8f)
                        )
                    }
                }
            }
        }

        // Removed floating buttons for maximum performance
        // All actions moved to top bar menu
        
        // Ultra-fast options menu
        if (showOptionsMenu) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.3f))
                    .clickable { showOptionsMenu = false }
            ) {
                Box(
                    modifier = Modifier
                        .align(Alignment.TopStart)
                        .padding(16.dp)
                        .width(200.dp)
                        .background(
                            currentTheme.first,
                            RoundedCornerShape(8.dp)
                        )
                        .border(
                            1.dp,
                            currentTheme.second.copy(alpha = 0.2f),
                            RoundedCornerShape(8.dp)
                        )
                ) {
                    Column(
                        modifier = Modifier.padding(8.dp)
                    ) {
                        // Share option
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showOptionsMenu = false
                                    // Handle share
                                }
                                .padding(12.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                Icons.Default.Share,
                                contentDescription = "Share",
                                modifier = Modifier.size(20.dp),
                                tint = currentTheme.second
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Text(
                                text = "Share",
                                fontSize = 16.sp,
                                color = currentTheme.second
                            )
                        }

                        // Bookmark option
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showOptionsMenu = false
                                    isBookmarked = !isBookmarked
                                }
                                .padding(12.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                if (isBookmarked) Icons.Default.Bookmark else Icons.Default.BookmarkBorder,
                                contentDescription = "Bookmark",
                                modifier = Modifier.size(20.dp),
                                tint = currentTheme.second
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Text(
                                text = if (isBookmarked) "Remove Bookmark" else "Add Bookmark",
                                fontSize = 16.sp,
                                color = currentTheme.second
                            )
                        }

                        // Copy option
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    showOptionsMenu = false
                                    clipboardManager.setText(AnnotatedString("$title\n\n$content"))
                                }
                                .padding(12.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                Icons.Default.ContentCopy,
                                contentDescription = "Copy",
                                modifier = Modifier.size(20.dp),
                                tint = currentTheme.second
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Text(
                                text = "Copy Text",
                                fontSize = 16.sp,
                                color = currentTheme.second
                            )
                        }
                    }
                }
            }
        }
    }
}
