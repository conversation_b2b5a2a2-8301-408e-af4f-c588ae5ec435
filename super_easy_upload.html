<!DOCTYPE html>
<html>
<head>
    <title>Super Easy Firebase Upload</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container { 
            max-width: 600px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        h1 { text-align: center; margin-bottom: 30px; }
        .step { 
            background: rgba(255,255,255,0.2); 
            padding: 20px; 
            margin: 15px 0; 
            border-radius: 10px; 
            border-left: 5px solid #00ff88;
        }
        input, textarea { 
            width: 100%; 
            padding: 12px; 
            margin: 10px 0; 
            border: none; 
            border-radius: 8px; 
            font-size: 14px;
        }
        button { 
            width: 100%;
            padding: 15px; 
            background: #00ff88; 
            color: #333; 
            border: none; 
            border-radius: 8px; 
            font-size: 16px;
            font-weight: bold;
            cursor: pointer; 
            margin: 10px 0;
        }
        button:hover { background: #00cc6a; }
        .output { 
            background: #333; 
            color: #00ff88; 
            padding: 15px; 
            border-radius: 8px; 
            font-family: monospace; 
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 Super Easy Firebase Upload</h1>
        
        <div class="step">
            <h3>Step 1: Upload JSON File</h3>
            <input type="file" id="fileInput" accept=".json">
            <div id="fileInfo" class="hidden"></div>
        </div>
        
        <div class="step">
            <h3>Step 2: Firebase Project ID</h3>
            <input type="text" id="projectId" placeholder="Enter your Firebase Project ID (e.g., arham-app-12345)">
            <small>Find this in Firebase Console → Project Settings</small>
        </div>
        
        <div class="step">
            <h3>Step 3: Collection Name</h3>
            <input type="text" id="collectionName" value="spiritual_content" placeholder="Collection name">
        </div>
        
        <button onclick="generateCommands()">Generate Upload Commands</button>
        
        <div class="step hidden" id="outputSection">
            <h3>Step 4: Copy & Run These Commands</h3>
            <p>Copy these commands and run in your terminal:</p>
            <div id="output" class="output"></div>
            <button onclick="copyToClipboard()">Copy Commands</button>
        </div>
    </div>

    <script>
        let jsonData = null;
        
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    jsonData = JSON.parse(e.target.result);
                    document.getElementById('fileInfo').innerHTML = `
                        ✅ File loaded: ${file.name}<br>
                        📊 Items found: ${jsonData.length}
                    `;
                    document.getElementById('fileInfo').classList.remove('hidden');
                } catch (error) {
                    alert('Invalid JSON file: ' + error.message);
                }
            };
            reader.readAsText(file);
        });
        
        function generateCommands() {
            if (!jsonData) {
                alert('Please upload a JSON file first');
                return;
            }
            
            const projectId = document.getElementById('projectId').value.trim();
            const collectionName = document.getElementById('collectionName').value.trim() || 'spiritual_content';
            
            if (!projectId) {
                alert('Please enter your Firebase Project ID');
                return;
            }
            
            // Generate Firebase CLI commands
            let commands = `# Firebase Upload Commands for ${collectionName}\n`;
            commands += `# Total items: ${jsonData.length}\n\n`;
            commands += `# First, install Firebase CLI:\n`;
            commands += `npm install -g firebase-tools\n\n`;
            commands += `# Login to Firebase:\n`;
            commands += `firebase login\n\n`;
            commands += `# Set project:\n`;
            commands += `firebase use ${projectId}\n\n`;
            commands += `# Upload commands (run these one by one):\n\n`;
            
            // Generate individual upload commands for first 10 items
            const itemsToShow = Math.min(jsonData.length, 10);
            for (let i = 0; i < itemsToShow; i++) {
                const item = jsonData[i];
                const jsonString = JSON.stringify(item).replace(/"/g, '\\"');
                commands += `echo '${jsonString}' | firebase firestore:set ${collectionName}/${item.id}\n`;
            }
            
            if (jsonData.length > 10) {
                commands += `\n# ... and ${jsonData.length - 10} more items\n`;
                commands += `# (Use the web interface or script for bulk upload)\n`;
            }
            
            document.getElementById('output').textContent = commands;
            document.getElementById('outputSection').classList.remove('hidden');
        }
        
        function copyToClipboard() {
            const output = document.getElementById('output');
            navigator.clipboard.writeText(output.textContent).then(() => {
                alert('Commands copied to clipboard!');
            });
        }
    </script>
</body>
</html>
