<!DOCTYPE html>
<html>
<head>
    <title>Upload JSO<PERSON> to Firestore</title>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore.js"></script>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .upload-area { border: 2px dashed #ccc; padding: 40px; text-align: center; margin: 20px 0; }
        .progress { background: #f0f0f0; height: 20px; border-radius: 10px; overflow: hidden; margin: 20px 0; }
        .progress-bar { background: #4CAF50; height: 100%; width: 0%; transition: width 0.3s; }
        button { background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .log { background: #f5f5f5; padding: 15px; border-radius: 5px; height: 200px; overflow-y: auto; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Upload JSON to Firestore</h1>
    
    <div>
        <label>Collection Name:</label>
        <input type="text" id="collectionName" value="spiritual_content" style="width: 200px; padding: 5px;">
    </div>
    
    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
        <input type="file" id="fileInput" accept=".json" style="display: none;">
        <p>Click here to select JSON file</p>
        <p id="fileName"></p>
    </div>
    
    <button id="uploadBtn" onclick="uploadJSON()" disabled>Upload to Firestore</button>
    
    <div class="progress" style="display: none;">
        <div class="progress-bar" id="progressBar"></div>
    </div>
    
    <div class="log" id="log"></div>

    <script>
        // Your Firebase config
        const firebaseConfig = {
            apiKey: "YOUR_API_KEY",
            authDomain: "YOUR_PROJECT.firebaseapp.com",
            projectId: "YOUR_PROJECT_ID",
            storageBucket: "YOUR_PROJECT.appspot.com",
            messagingSenderId: "YOUR_SENDER_ID",
            appId: "YOUR_APP_ID"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const db = firebase.firestore();

        let selectedFile = null;

        document.getElementById('fileInput').addEventListener('change', function(e) {
            selectedFile = e.target.files[0];
            if (selectedFile) {
                document.getElementById('fileName').textContent = selectedFile.name;
                document.getElementById('uploadBtn').disabled = false;
            }
        });

        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        async function uploadJSON() {
            if (!selectedFile) return;

            const collectionName = document.getElementById('collectionName').value;
            const uploadBtn = document.getElementById('uploadBtn');
            const progressDiv = document.querySelector('.progress');
            const progressBar = document.getElementById('progressBar');

            uploadBtn.disabled = true;
            progressDiv.style.display = 'block';
            
            try {
                log('Reading JSON file...');
                const text = await selectedFile.text();
                const jsonData = JSON.parse(text);
                
                log('Starting upload to Firestore...');
                
                if (Array.isArray(jsonData)) {
                    await uploadArray(jsonData, collectionName, progressBar);
                } else if (typeof jsonData === 'object') {
                    await uploadObject(jsonData, collectionName, progressBar);
                }
                
                log('✅ Upload completed successfully!');
                
            } catch (error) {
                log('❌ Error: ' + error.message);
            }
            
            uploadBtn.disabled = false;
        }

        async function uploadArray(data, collectionName, progressBar) {
            const batchSize = 500;
            const totalBatches = Math.ceil(data.length / batchSize);
            
            for (let i = 0; i < totalBatches; i++) {
                const batch = db.batch();
                const start = i * batchSize;
                const end = Math.min(start + batchSize, data.length);
                
                for (let j = start; j < end; j++) {
                    const docRef = db.collection(collectionName).doc();
                    batch.set(docRef, data[j]);
                }
                
                await batch.commit();
                const progress = ((i + 1) / totalBatches) * 100;
                progressBar.style.width = progress + '%';
                log(`Uploaded batch ${i + 1}/${totalBatches} (${end - start} documents)`);
            }
        }

        async function uploadObject(data, collectionName, progressBar) {
            const entries = Object.entries(data);
            const batchSize = 500;
            const totalBatches = Math.ceil(entries.length / batchSize);
            
            for (let i = 0; i < totalBatches; i++) {
                const batch = db.batch();
                const start = i * batchSize;
                const end = Math.min(start + batchSize, entries.length);
                
                for (let j = start; j < end; j++) {
                    const [docId, docData] = entries[j];
                    const docRef = db.collection(collectionName).doc(docId);
                    batch.set(docRef, docData);
                }
                
                await batch.commit();
                const progress = ((i + 1) / totalBatches) * 100;
                progressBar.style.width = progress + '%';
                log(`Uploaded batch ${i + 1}/${totalBatches} (${end - start} documents)`);
            }
        }
    </script>
</body>
</html>
