package com.example.arham.presentation.viewmodels

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.arham.domain.models.SpiritualContent
import com.example.arham.domain.models.ContentCategory
import com.example.arham.domain.usecases.GetContentByCategoryUseCase
import com.example.arham.domain.usecases.SearchContentUseCase
import com.example.arham.domain.repository.ContentRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import javax.inject.Inject

/**
 * ViewModel for content screens
 * Clean architecture - Presentation layer
 */
class ContentViewModel @Inject constructor(
    private val getContentByCategoryUseCase: GetContentByCategoryUseCase,
    private val searchContentUseCase: SearchContentUseCase,
    private val contentRepository: ContentRepository
) : ViewModel() {
    
    // UI State
    private val _uiState = MutableStateFlow(ContentUiState())
    val uiState: StateFlow<ContentUiState> = _uiState.asStateFlow()
    
    // Search state
    private val _searchState = MutableStateFlow(SearchUiState())
    val searchState: StateFlow<SearchUiState> = _searchState.asStateFlow()
    
    private var searchJob: Job? = null
    
    fun loadContentByCategory(category: ContentCategory) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                val content = getContentByCategoryUseCase(category)
                _uiState.value = _uiState.value.copy(
                    content = content,
                    isLoading = false,
                    currentCategory = category
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "Unknown error occurred"
                )
            }
        }
    }
    
    fun searchContent(query: String) {
        searchJob?.cancel()
        
        if (query.isBlank()) {
            _searchState.value = SearchUiState()
            return
        }
        
        searchJob = viewModelScope.launch {
            _searchState.value = _searchState.value.copy(isLoading = true)
            
            // Debounce search
            delay(300)
            
            try {
                val results = searchContentUseCase(query)
                _searchState.value = _searchState.value.copy(
                    results = results,
                    isLoading = false,
                    query = query
                )
            } catch (e: Exception) {
                _searchState.value = _searchState.value.copy(
                    isLoading = false,
                    error = e.message ?: "Search failed"
                )
            }
        }
    }
    
    fun incrementViewCount(contentId: String) {
        viewModelScope.launch {
            contentRepository.incrementViewCount(contentId)
        }
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    fun clearSearchError() {
        _searchState.value = _searchState.value.copy(error = null)
    }
}

/**
 * UI State for content screens
 */
data class ContentUiState(
    val content: List<SpiritualContent> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null,
    val currentCategory: ContentCategory? = null
)

/**
 * UI State for search
 */
data class SearchUiState(
    val results: List<SpiritualContent> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null,
    val query: String = ""
)
