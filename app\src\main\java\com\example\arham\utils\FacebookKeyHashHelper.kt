package com.example.arham.utils

import android.content.Context
import android.content.pm.PackageManager
import android.util.Base64
import android.util.Log
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException

object FacebookKeyHashHelper {
    
    /**
     * Generate Facebook Key Hash for debugging
     * Call this once and check the logs for the key hash
     */
    fun printKeyHash(context: Context) {
        try {
            val info = context.packageManager.getPackageInfo(
                context.packageName,
                PackageManager.GET_SIGNATURES
            )
            
            for (signature in info.signatures) {
                val md = MessageDigest.getInstance("SHA")
                md.update(signature.toByteArray())
                val keyHash = Base64.encodeToString(md.digest(), Base64.DEFAULT)
                Log.d("FacebookKeyHash", "Key Hash: $keyHash")
                println("Facebook Key Hash: $keyHash")
            }
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e("FacebookKeyHash", "Package not found", e)
        } catch (e: NoSuchAlgorithmException) {
            Log.e("FacebookKeyHash", "Algorithm not found", e)
        }
    }
}
