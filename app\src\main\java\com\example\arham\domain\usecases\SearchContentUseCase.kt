package com.example.arham.domain.usecases

import com.example.arham.domain.models.SpiritualContent
import com.example.arham.domain.repository.ContentRepository
import javax.inject.Inject

/**
 * Use case for searching content
 * Clean architecture - Domain layer business logic
 */
class SearchContentUseCase @Inject constructor(
    private val repository: ContentRepository
) {
    
    suspend operator fun invoke(query: String): List<SpiritualContent> {
        if (query.isBlank()) return emptyList()
        
        return try {
            val results = repository.searchContent(query.trim())
            
            // Sort by relevance: exact title match > title contains > content contains
            results.sortedWith { content1, content2 ->
                val query1 = query.lowercase()
                val title1 = content1.title.lowercase()
                val title2 = content2.title.lowercase()
                
                when {
                    title1 == query1 && title2 != query1 -> -1
                    title1 != query1 && title2 == query1 -> 1
                    title1.contains(query1) && !title2.contains(query1) -> -1
                    !title1.contains(query1) && title2.contains(query1) -> 1
                    else -> {
                        // Secondary sort by popularity and view count
                        when {
                            content1.isPopular && !content2.isPopular -> -1
                            !content1.isPopular && content2.isPopular -> 1
                            else -> content2.viewCount.compareTo(content1.viewCount)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            emptyList()
        }
    }
}
