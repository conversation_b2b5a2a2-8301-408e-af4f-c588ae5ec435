package com.example.arham.data.repository

import com.example.arham.domain.models.SpiritualContent
import com.example.arham.domain.models.ContentCategory
import com.example.arham.domain.repository.ContentRepository
import com.example.arham.data.datasource.FirestoreDataSource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.catch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Implementation of ContentRepository
 * Clean architecture - Data layer
 */
@Singleton
class ContentRepositoryImpl @Inject constructor(
    private val firestoreDataSource: FirestoreDataSource
) : ContentRepository {
    
    private var cachedContent: List<SpiritualContent> = emptyList()
    private var lastCacheTime: Long = 0
    private val cacheValidityDuration = 5 * 60 * 1000L // 5 minutes
    
    override suspend fun getAllContent(): List<SpiritualContent> {
        return if (isCacheValid()) {
            cachedContent
        } else {
            try {
                val content = firestoreDataSource.getAllContent()
                cachedContent = content
                lastCacheTime = System.currentTimeMillis()
                content
            } catch (e: Exception) {
                cachedContent // Return cached data if network fails
            }
        }
    }
    
    override suspend fun getContentByCategory(category: ContentCategory): List<SpiritualContent> {
        return getAllContent().filter { it.category == category }
    }
    
    override suspend fun getContentById(id: String): SpiritualContent? {
        return getAllContent().find { it.id == id }
            ?: firestoreDataSource.getContentById(id)
    }
    
    override suspend fun searchContent(query: String): List<SpiritualContent> {
        val allContent = getAllContent()
        val queryLower = query.lowercase()
        
        return allContent.filter { content ->
            content.title.lowercase().contains(queryLower) ||
            content.content.lowercase().contains(queryLower) ||
            content.author.lowercase().contains(queryLower) ||
            content.tags.any { it.lowercase().contains(queryLower) } ||
            content.searchKeywords.any { it.lowercase().contains(queryLower) }
        }
    }
    
    override suspend fun getPopularContent(): List<SpiritualContent> {
        return getAllContent()
            .filter { it.isPopular }
            .sortedByDescending { it.viewCount }
    }
    
    override fun getContentFlow(): Flow<List<SpiritualContent>> = flow {
        emit(getAllContent())
    }.catch { emit(cachedContent) }
    
    override fun getContentByCategoryFlow(category: ContentCategory): Flow<List<SpiritualContent>> = flow {
        emit(getContentByCategory(category))
    }.catch { emit(emptyList()) }
    
    override suspend fun addContent(content: SpiritualContent): Result<Unit> {
        return try {
            firestoreDataSource.addContent(content)
            invalidateCache()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun updateContent(content: SpiritualContent): Result<Unit> {
        return try {
            firestoreDataSource.updateContent(content)
            invalidateCache()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun deleteContent(id: String): Result<Unit> {
        return try {
            firestoreDataSource.deleteContent(id)
            invalidateCache()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun incrementViewCount(contentId: String): Result<Unit> {
        return try {
            firestoreDataSource.incrementViewCount(contentId)
            // Update local cache
            cachedContent = cachedContent.map { content ->
                if (content.id == contentId) {
                    content.copy(viewCount = content.viewCount + 1)
                } else {
                    content
                }
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    override suspend fun getContentAnalytics(contentId: String): Map<String, Any> {
        return firestoreDataSource.getContentAnalytics(contentId)
    }
    
    private fun isCacheValid(): Boolean {
        return cachedContent.isNotEmpty() && 
               (System.currentTimeMillis() - lastCacheTime) < cacheValidityDuration
    }
    
    private fun invalidateCache() {
        lastCacheTime = 0
    }
}
