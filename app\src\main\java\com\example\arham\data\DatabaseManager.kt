package com.example.arham.data

import android.util.Log
import com.example.arham.data.models.*
import com.example.arham.data.repository.FirestoreRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

class DatabaseManager private constructor() {
    private val repository = FirestoreRepository()
    
    // State flows for different content types
    private val _bhikshuContent = MutableStateFlow<List<SpiritualContent>>(emptyList())
    val bhikshuContent: StateFlow<List<SpiritualContent>> = _bhikshuContent.asStateFlow()
    
    private val _tulsiContent = MutableStateFlow<List<SpiritualContent>>(emptyList())
    val tulsiContent: StateFlow<List<SpiritualContent>> = _tulsiContent.asStateFlow()
    
    private val _kaluContent = MutableStateFlow<List<SpiritualContent>>(emptyList())
    val kaluContent: StateFlow<List<SpiritualContent>> = _kaluContent.asStateFlow()
    
    private val _mahaprajnaContent = MutableStateFlow<List<SpiritualContent>>(emptyList())
    val mahaprajnaContent: StateFlow<List<SpiritualContent>> = _mahaprajnaContent.asStateFlow()
    
    private val _geetContent = MutableStateFlow<List<SpiritualContent>>(emptyList())
    val geetContent: StateFlow<List<SpiritualContent>> = _geetContent.asStateFlow()
    
    private val _mangalContent = MutableStateFlow<List<SpiritualContent>>(emptyList())
    val mangalContent: StateFlow<List<SpiritualContent>> = _mangalContent.asStateFlow()
    
    private val _allContent = MutableStateFlow<List<SpiritualContent>>(emptyList())
    val allContent: StateFlow<List<SpiritualContent>> = _allContent.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    private val _isInitialized = MutableStateFlow(false)
    val isInitialized: StateFlow<Boolean> = _isInitialized.asStateFlow()
    
    companion object {
        @Volatile
        private var INSTANCE: DatabaseManager? = null
        
        fun getInstance(): DatabaseManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DatabaseManager().also { INSTANCE = it }
            }
        }
    }
    
    suspend fun initializeDatabase() {
        if (_isInitialized.value) return
        
        _isLoading.value = true
        try {
            Log.d("DatabaseManager", "Initializing database...")
            
            // Check if data exists, if not seed it
            val existingContent = repository.getAllContent()
            if (existingContent.isEmpty()) {
                Log.d("DatabaseManager", "No content found, seeding database...")
                seedDatabase()
            }
            
            // Load all content
            loadAllContent()
            _isInitialized.value = true
            
            Log.d("DatabaseManager", "Database initialized successfully")
        } catch (e: Exception) {
            Log.e("DatabaseManager", "Error initializing database", e)
            // Load fallback static content
            loadFallbackContent()
        } finally {
            _isLoading.value = false
        }
    }
    
    private suspend fun seedDatabase() {
        try {
            // Seed Bhikshu content
            seedBhikshuContent()
            
            // Seed other content
            seedTulsiContent()
            seedKaluContent()
            seedMahaprajnaContent()
            seedGeetContent()
            seedMangalContent()
            
            Log.d("DatabaseManager", "Database seeding completed")
        } catch (e: Exception) {
            Log.e("DatabaseManager", "Error seeding database", e)
        }
    }
    
    private suspend fun seedBhikshuContent() {
        val bhikshuBhajans = listOf(
            "भिक्षु म्हारै प्रगट्या जी", "चैत्य पुरुष", "वंदना लो झेलो", "सिरियारी रो संत",
            "ओ म्हांरा गुरुदेव!", "घणा सुहावो माता", "स्वामीजी! थांरी साधना री",
            "रुं रूं में सांवरियो", "स्वामी भीखणजी रो नाम", "बादळियो आंखड़ल्यां में",
            "म्हांनै घणा सुहावै जी", "निहारा तुमको कितनी बार", "हमारे भाग्य बड़े बलवान",
            "मंगल है आज तेरे शासन में", "भीखणजी स्वामी! भारी मर्यादा", "गुरुदेव! थांरी खिण-खिण याद",
            "प्रगट्यो एक नयो उद्योत", "प्रभो! यह तेरापन्थ महान", "देखो मर्यादा की महिमां",
            "देवते! बतलाओ", "स्वामी पंथ दिखाओ जी", "भिक्षु-स्मरण", "म्हारै सांस-सांस में बोलै रे",
            "स्वामीजी म्हानै दर्शन दीन्हाजी", "भिक्षुस्वाम भिक्षुस्वाम रटन लगावां",
            "भिक्षु-भिक्षु-भिक्षु म्हांरी आतमा", "भज मन भिखु स्याम", "आस्था रा अनुपम दीप",
            "स्वामीजी का नाम हमारे", "ओ स्वामीजी रो नाम", "स्वामीजी! थांरै चरणां",
            "आओ स्वामीजी", "धन्य धन्य भीखणजी स्वाम", "स्वामीजी! आओ देखल्यो",
            "भिक्षु-भिक्षु भजन हो", "आओ-आओ भिक्षु स्वामी", "स्वामीजी रै नाम री", "भिक्षु आरती",
            "अब तो पधारो भगवन्!", "स्वामीजी थारै संघ री", "शासन ओ भिक्षु रो",
            "श्री भिक्षु की जय-जयकार", "बाबै ने मनावां", "रूं रुं में सांवरियो",
            "श्री भिक्षु का नाम सुंदर", "भीखण री मूरत भावै", "भिक्षु स्वामी! अंतर्यामी!",
            "प्यारो सांवरिये रो नाम", "सांवरिये रो नाम लियां", "भिक्षु को भुलाएं कैसे",
            "भिक्षु गण री फुलवारी", "सांवरिया थांरै संघ स्यूं", "मीठो सांवरियै रो नाम",
            "तेरस री है रात", "अलख जगावां", "सांवरिया स्वामीजी! आओ", "भिक्षु स्वामीजी",
            "भिक्षु स्वाम ज्योतिर्धाम", "आओ स्वामीजी", "आपां गण रो गौरव गावां",
            "ऊं भिक्षु भिक्षु नित ध्यावां", "स्वामी भीखणजी कद स्यूं", "भिक्षु है इमरत रो झरणों",
            "आवो सांवरिया", "भिक्ष तेरी राह पर", "भिक्षु को अभिवंदना", "स्वामीजी थारै शासन में",
            "स्वामीजी! एकर तो देखो", "गौरव गाथा भिक्षु के बलिदान की", "भिक्षु-भिक्षु ही जबां पर",
            "स्वामीजी थांरो‌‌‍ संघ निरालो", "ऊं भिक्षु! जय भिक्षु!", "गण नंदनवन पा हरसावां",
            "म्हारै हिवड़े रो हार", "प्यारो सांवरिये रो नाम", "कल्पतरु रा बीज फल्या़",
            "समरां बाबै रो नित नाम", "ॐ भिक्षु ॐ भिक्षु जपो सदा", "स्वामीजी रो नाम महासुखकारी",
            "स्वाम भिक्षु रो नाम", "भिक्षु भिक्षु बोल तूं", "आओ म्हारा स्वामी जी",
            "बाबै रो नाम बड़ो", "भिक्षु स्वाम भिक्षु स्वाम", "भिक्षु भिक्षु बोलो",
            "ॐ जय भिक्षु स्वामी", "नमो गुरुदेवाणं", "सिरियारी वाले करोनी",
            "संयम की सांसो से", "पंखीड़ा ओ पंखीड़ा"
        )
        
        bhikshuBhajans.forEachIndexed { index, title ->
            val content = SpiritualContent(
                id = "bhikshu_$index",
                title = title,
                content = getBhajanFullContent(title),
                author = "आचार्य भिक्षु",
                category = ContentCategory.BHIKSHU,
                type = ContentType.BHAJAN,
                language = Language.HINDI,
                tags = listOf("भिक्षु", "भजन", "तेरापंथ", "आध्यात्म"),
                searchKeywords = generateSearchKeywords(title, "आचार्य भिक्षु"),
                isPopular = index < 10,
                viewCount = (100..1000).random().toLong()
            )
            
            repository.addContent(content)
        }
    }
    
    private suspend fun seedTulsiContent() {
        val tulsiItems = listOf(
            "तुलसी के दोहे", "हनुमान चालीसा", "रामायण के श्लोक", "राम नाम की महिमा",
            "सीता राम भजन", "हनुमान स्तुति", "राम रक्षा स्तोत्र", "तुलसी की वाणी",
            "रामचरितमानस के पद", "भक्ति रस", "राम कथा", "हनुमान आरती"
        )
        
        tulsiItems.forEachIndexed { index, title ->
            val content = SpiritualContent(
                id = "tulsi_$index",
                title = title,
                content = getTulsiFullContent(title),
                author = "तुलसीदास",
                category = ContentCategory.TULSI,
                type = if (title.contains("दोहे")) ContentType.TEACHING else ContentType.BHAJAN,
                language = Language.HINDI,
                tags = listOf("तुलसी", "राम", "हनुमान", "भक्ति"),
                searchKeywords = generateSearchKeywords(title, "तुलसीदास"),
                isPopular = index < 5,
                viewCount = (200..1500).random().toLong()
            )
            
            repository.addContent(content)
        }
    }
    
    private suspend fun seedKaluContent() {
        val kaluItems = listOf(
            "कालू की कहानियां", "आध्यात्मिक कथाएं", "संत कालू के उपदेश", "जीवन के सत्य",
            "नैतिक शिक्षा", "आत्मा की खोज", "सच्चाई की राह", "प्रेम और करुणा"
        )
        
        kaluItems.forEachIndexed { index, title ->
            val content = SpiritualContent(
                id = "kalu_$index",
                title = title,
                content = getKaluFullContent(title),
                author = "संत कालू",
                category = ContentCategory.KALU,
                type = ContentType.STORY,
                language = Language.HINDI,
                tags = listOf("कालू", "कहानी", "उपदेश", "नैतिकता"),
                searchKeywords = generateSearchKeywords(title, "संत कालू"),
                isPopular = index < 3,
                viewCount = (150..800).random().toLong()
            )
            
            repository.addContent(content)
        }
    }
    
    private suspend fun seedMahaprajnaContent() {
        val mahaprajnaItems = listOf(
            "प्रेक्षा ध्यान", "आत्म साधना", "जैन दर्शन", "महाप्रज्ञा के सूत्र",
            "आध्यात्मिक ज्ञान", "मन की शुद्धता", "कर्म सिद्धांत", "मोक्ष मार्ग"
        )
        
        mahaprajnaItems.forEachIndexed { index, title ->
            val content = SpiritualContent(
                id = "mahaprajna_$index",
                title = title,
                content = getMahaprajnaFullContent(title),
                author = "आचार्य महाप्रज्ञा",
                category = ContentCategory.MAHAPRAJNA,
                type = ContentType.TEACHING,
                language = Language.HINDI,
                tags = listOf("महाप्रज्ञा", "ध्यान", "दर्शन", "साधना"),
                searchKeywords = generateSearchKeywords(title, "आचार्य महाप्रज्ञा"),
                isPopular = index < 4,
                viewCount = (300..1200).random().toLong()
            )
            
            repository.addContent(content)
        }
    }
    
    private suspend fun seedGeetContent() {
        val geetItems = listOf(
            "आध्यात्मिक गीत", "भक्ति संगीत", "मंगल गान", "प्रार्थना गीत",
            "शांति मंत्र", "ध्यान संगीत", "आरती गीत", "स्तुति गान"
        )
        
        geetItems.forEachIndexed { index, title ->
            val content = SpiritualContent(
                id = "geet_$index",
                title = title,
                content = getGeetFullContent(title),
                author = "विभिन्न संत",
                category = ContentCategory.GENERAL,
                type = ContentType.GEET,
                language = Language.HINDI,
                tags = listOf("गीत", "संगीत", "भक्ति", "आध्यात्म"),
                searchKeywords = generateSearchKeywords(title, "विभिन्न संत"),
                isPopular = index < 3,
                viewCount = (100..600).random().toLong()
            )
            
            repository.addContent(content)
        }
    }
    
    private suspend fun seedMangalContent() {
        val mangalItems = listOf(
            "मंगल आरती", "शुभ मंगल", "कल्याण मंत्र", "सुख शांति प्रार्थना",
            "मंगल कामना", "आशीर्वाद गीत", "शुभकामना मंत्र", "कल्याण पाठ"
        )
        
        mangalItems.forEachIndexed { index, title ->
            val content = SpiritualContent(
                id = "mangal_$index",
                title = title,
                content = getMangalFullContent(title),
                author = "विभिन्न संत",
                category = ContentCategory.MANGAL,
                type = ContentType.PRAYER,
                language = Language.HINDI,
                tags = listOf("मंगल", "आरती", "प्रार्थना", "कल्याण"),
                searchKeywords = generateSearchKeywords(title, "विभिन्न संत"),
                isPopular = index < 2,
                viewCount = (80..400).random().toLong()
            )
            
            repository.addContent(content)
        }
    }
    
    private suspend fun loadAllContent() {
        try {
            // Load content by category
            _bhikshuContent.value = repository.getContentByCategory(ContentCategory.BHIKSHU)
            _tulsiContent.value = repository.getContentByCategory(ContentCategory.TULSI)
            _kaluContent.value = repository.getContentByCategory(ContentCategory.KALU)
            _mahaprajnaContent.value = repository.getContentByCategory(ContentCategory.MAHAPRAJNA)
            _geetContent.value = repository.getContentByCategory(ContentCategory.GENERAL).filter { it.type == ContentType.GEET }
            _mangalContent.value = repository.getContentByCategory(ContentCategory.MANGAL)
            
            // Load all content
            _allContent.value = repository.getAllContent()
            
            Log.d("DatabaseManager", "Loaded ${_allContent.value.size} total content items")
        } catch (e: Exception) {
            Log.e("DatabaseManager", "Error loading content", e)
            loadFallbackContent()
        }
    }
    
    private fun loadFallbackContent() {
        // Load static content as fallback
        Log.d("DatabaseManager", "Loading fallback static content")

        // Create static content for Bhikshu
        val staticBhikshuContent = listOf(
            "भिक्षु म्हारै प्रगट्या जी", "चैत्य पुरुष", "वंदना लो झेलो", "सिरियारी रो संत",
            "ओ म्हांरा गुरुदेव!", "घणा सुहावो माता", "स्वामीजी! थांरी साधना री",
            "रुं रूं में सांवरियो", "स्वामी भीखणजी रो नाम", "बादळियो आंखड़ल्यां में"
            // Add more as needed
        ).mapIndexed { index, title ->
            SpiritualContent(
                id = "static_bhikshu_$index",
                title = title,
                content = "Static content for $title",
                author = "आचार्य भिक्षु",
                category = ContentCategory.BHIKSHU,
                type = ContentType.BHAJAN,
                language = Language.HINDI,
                tags = listOf("भिक्षु", "भजन"),
                searchKeywords = listOf(title, "भिक्षु"),
                isPopular = index < 5,
                viewCount = 0
            )
        }

        _bhikshuContent.value = staticBhikshuContent
        _allContent.value = staticBhikshuContent
    }
    
    // Helper functions
    private fun generateSearchKeywords(title: String, author: String): List<String> {
        val keywords = mutableListOf<String>()
        keywords.addAll(title.split(" ").filter { it.isNotBlank() })
        keywords.addAll(author.split(" ").filter { it.isNotBlank() })
        keywords.addAll(listOf("भजन", "गीत", "प्रार्थना", "आध्यात्म", "भक्ति"))
        return keywords.distinct()
    }
    
    // Content generation functions
    private fun getBhajanFullContent(title: String): String {
        return when (title) {
            "भिक्षु म्हारै प्रगट्या जी" -> """
                भिक्षु म्हारै प्रगट्या जी, धन्य धन्य यो दिन
                सुख शांति रो दाता आयो, हरष्यो म्हारो मन
                
                तेरापंथ रो तारणहार, भिक्षु स्वामी आयो
                अहिंसा रो संदेश लेकर, जग में ज्योति जगायो
                
                सत्य अहिंसा रो पंथ दिखायो
                मानव धर्म सिखायो
                भिक्षु म्हारै प्रगट्या जी, धन्य धन्य यो दिन
                
                करुणा दया रो सागर आयो
                प्रेम भाव सिखायो
                जीव दया रो महत्व बतायो
                पाप मुक्ति रो राह दिखायो
                
                भिक्षु स्वामी रो नाम सुमिरां
                मन में शांति पावां
                तेरापंथ रो गौरव गावां
                आत्मा रो कल्याण करावां
            """.trimIndent()
            
            "चैत्य पुरुष" -> """
                चैत्य पुरुष भिक्षु स्वामी, तुम हो जग के नेता
                सत्य अहिंसा के मार्ग पर, तुमने जग को चेता
                
                तेरापंथ के संस्थापक तुम
                धर्म के रक्षक तुम
                अहिंसा के प्रचारक तुम
                सत्य के उपदेशक तुम
                
                चैत्य पुरुष भिक्षु स्वामी, तुम हो जग के नेता
                
                जीव दया का संदेश दिया
                मानव धर्म सिखाया
                पाप मुक्ति का मार्ग दिखाया
                आत्मा का कल्याण कराया
                
                तुम्हारे चरणों में शीश नवाएं
                तुम्हारा आशीर्वाद पाएं
                भिक्षु स्वामी की जय जयकार
                हो तेरापंथ का उद्धार
            """.trimIndent()
            
            else -> """
                $title
                
                यह एक पवित्र भजन है जो आचार्य भिक्षु की महिमा में गाया जाता है।
                
                भिक्षु स्वामी तेरापंथ के संस्थापक थे और उन्होंने सत्य, अहिंसा और जीव दया का संदेश दिया।
                
                उनकी शिक्षाएं आज भी हमारे जीवन में प्रासंगिक हैं और हमें सही मार्ग दिखाती हैं।
                
                इस भजन के माध्यम से हम उनकी महिमा का गुणगान करते हैं और उनसे आशीर्वाद प्राप्त करते हैं।
                
                ॐ भिक्षु भिक्षु नमः
                जय भिक्षु स्वामी
                जय तेरापंथ
            """.trimIndent()
        }
    }
    
    private fun getTulsiFullContent(title: String): String {
        return """
            $title
            
            तुलसीदास जी की यह रचना राम भक्ति और आध्यात्मिक ज्ञान से भरपूर है।
            
            इसमें श्री राम की महिमा, हनुमान जी की भक्ति और जीवन के आध्यात्मिक सत्य हैं।
            
            तुलसी की वाणी हमें सत्य, धर्म और भक्ति का मार्ग दिखाती है।
        """.trimIndent()
    }
    
    private fun getKaluFullContent(title: String): String {
        return """
            $title
            
            संत कालू की यह कहानी जीवन के महत्वपूर्ण सत्य और नैतिक शिक्षा प्रदान करती है।
            
            इसमें आध्यात्मिक ज्ञान, जीवन के अनुभव और मानवीय मूल्यों की शिक्षा है।
            
            कालू जी के उपदेश हमें सच्चाई, प्रेम और करुणा का मार्ग दिखाते हैं।
        """.trimIndent()
    }
    
    private fun getMahaprajnaFullContent(title: String): String {
        return """
            $title
            
            आचार्य महाप्रज्ञा की यह शिक्षा गहरे आध्यात्मिक ज्ञान और ध्यान की विधि प्रस्तुत करती है।
            
            इसमें जैन दर्शन, प्रेक्षा ध्यान और आत्म साधना के सूत्र हैं।
            
            महाप्रज्ञा जी के उपदेश हमें मन की शुद्धता और आत्मा के कल्याण का मार्ग दिखाते हैं।
        """.trimIndent()
    }
    
    private fun getGeetFullContent(title: String): String {
        return """
            $title
            
            यह एक सुंदर आध्यात्मिक गीत है जो मन को शांति और आनंद प्रदान करता है।
            
            इसमें भक्ति रस, आध्यात्मिक भावनाएं और दिव्य संगीत का मेल है।
            
            यह गीत हमारे हृदय में भक्ति और प्रेम की भावना जगाता है।
        """.trimIndent()
    }
    
    private fun getMangalFullContent(title: String): String {
        return """
            $title
            
            यह मंगल कामना और शुभ आशीर्वाद से भरी प्रार्थना है।
            
            इसमें सुख, शांति, कल्याण और मंगल की कामना है।
            
            यह प्रार्थना हमारे जीवन में सकारात्मकता और आशीर्वाद लाती है।
        """.trimIndent()
    }
    
    // Public methods for accessing content
    suspend fun searchContent(query: String) = repository.globalSearch(query)
    suspend fun getContentById(id: String) = repository.getContentById(id)
    suspend fun addBookmark(userId: String, content: SpiritualContent) = repository.addBookmark(userId, content)
    suspend fun removeBookmark(userId: String, contentId: String) = repository.removeBookmark(userId, contentId)
    suspend fun getUserBookmarks(userId: String) = repository.getUserBookmarks(userId)
    suspend fun isBookmarked(userId: String, contentId: String) = repository.isBookmarked(userId, contentId)
}
