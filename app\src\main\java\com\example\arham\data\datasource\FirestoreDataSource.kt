package com.example.arham.data.datasource

import com.example.arham.domain.models.SpiritualContent
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Firestore data source
 * Clean architecture - Data layer
 */
@Singleton
class FirestoreDataSource @Inject constructor() {
    
    private val db = FirebaseFirestore.getInstance()
    private val contentCollection = db.collection("spiritual_content")
    private val analyticsCollection = db.collection("content_analytics")
    
    suspend fun getAllContent(): List<SpiritualContent> {
        return try {
            val snapshot = contentCollection
                .orderBy("isPopular", Query.Direction.DESCENDING)
                .orderBy("viewCount", Query.Direction.DESCENDING)
                .get()
                .await()
            
            snapshot.documents.mapNotNull { doc ->
                doc.toObject(SpiritualContent::class.java)?.copy(id = doc.id)
            }
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    suspend fun getContentById(id: String): SpiritualContent? {
        return try {
            val doc = contentCollection.document(id).get().await()
            doc.toObject(SpiritualContent::class.java)?.copy(id = doc.id)
        } catch (e: Exception) {
            null
        }
    }
    
    suspend fun addContent(content: SpiritualContent) {
        contentCollection.document(content.id).set(content).await()
    }
    
    suspend fun updateContent(content: SpiritualContent) {
        contentCollection.document(content.id).set(content).await()
    }
    
    suspend fun deleteContent(id: String) {
        contentCollection.document(id).delete().await()
    }
    
    suspend fun incrementViewCount(contentId: String) {
        val docRef = contentCollection.document(contentId)
        db.runTransaction { transaction ->
            val snapshot = transaction.get(docRef)
            val currentCount = snapshot.getLong("viewCount") ?: 0
            transaction.update(docRef, "viewCount", currentCount + 1)
        }.await()
    }
    
    suspend fun getContentAnalytics(contentId: String): Map<String, Any> {
        return try {
            val doc = analyticsCollection.document(contentId).get().await()
            doc.data ?: emptyMap()
        } catch (e: Exception) {
            emptyMap()
        }
    }
}
