const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');

// Initialize Firebase Admin SDK
const serviceAccount = require('./serviceAccountKey.json'); // You'll need to download this

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

const db = admin.firestore();

async function uploadJSONToFirestore(jsonFilePath, collectionName) {
  try {
    console.log('Reading JSON file...');
    const jsonData = JSON.parse(fs.readFileSync(jsonFilePath, 'utf8'));

    console.log('Starting upload to Firestore...');
    console.log(`Data type: ${Array.isArray(jsonData) ? 'Array' : 'Object'}`);

    let batch = db.batch();
    let batchCount = 0;
    let totalUploaded = 0;

    // Function to commit current batch
    async function commitBatch() {
      if (batchCount > 0) {
        await batch.commit();
        console.log(`✅ Uploaded batch of ${batchCount} documents (Total: ${totalUploaded + batchCount})`);
        totalUploaded += batchCount;
        batch = db.batch();
        batchCount = 0;
      }
    }

    // Function to add document to batch
    function addToBatch(docData, docId = null) {
      // Generate document ID from title if not provided
      const documentId = docId || generateDocId(docData.title || `doc_${Date.now()}_${Math.random()}`);

      // Add timestamp and ID to document
      const documentData = {
        ...docData,
        id: documentId,
        uploadedAt: admin.firestore.FieldValue.serverTimestamp(),
        // Convert tags string to array if needed
        tags: typeof docData.tags === 'string' ?
              docData.tags.split(',').map(tag => tag.trim()) :
              docData.tags || []
      };

      const docRef = db.collection(collectionName).doc(documentId);
      batch.set(docRef, documentData);
      batchCount++;

      console.log(`📄 Added: ${docData.title || documentId}`);
    }

    // If JSON is an array of documents
    if (Array.isArray(jsonData)) {
      console.log(`Processing ${jsonData.length} documents...`);

      for (let i = 0; i < jsonData.length; i++) {
        addToBatch(jsonData[i]);

        // Commit batch when it reaches 500 documents or at the end
        if (batchCount === 500 || i === jsonData.length - 1) {
          await commitBatch();
        }
      }
    }
    // If JSON is a single document
    else if (typeof jsonData === 'object' && jsonData.title) {
      console.log('Processing single document...');
      addToBatch(jsonData);
      await commitBatch();
    }
    // If JSON is an object with multiple documents
    else if (typeof jsonData === 'object') {
      const entries = Object.entries(jsonData);
      console.log(`Processing ${entries.length} documents...`);

      for (let i = 0; i < entries.length; i++) {
        const [docId, docData] = entries[i];
        addToBatch(docData, docId);

        if (batchCount === 500 || i === entries.length - 1) {
          await commitBatch();
        }
      }
    }

    console.log(`🎉 Upload completed successfully! Total documents uploaded: ${totalUploaded}`);

  } catch (error) {
    console.error('❌ Error uploading to Firestore:', error);
    if (error.code) {
      console.error('Error code:', error.code);
    }
  }
}

// Helper function to generate document ID from title
function generateDocId(title) {
  return title
    .toLowerCase()
    .replace(/[^\w\s]/g, '') // Remove special characters
    .replace(/\s+/g, '_')    // Replace spaces with underscores
    .substring(0, 50);       // Limit length
}

// Usage - Update these paths for your files
const jsonFilePath = './your-data.json'; // Replace with your JSON file path
const collectionName = 'spiritual_content'; // Firestore collection name

// Run the upload
console.log('🚀 Starting Firestore upload...');
console.log(`📁 File: ${jsonFilePath}`);
console.log(`📚 Collection: ${collectionName}`);
console.log('⏳ Please wait...\n');

uploadJSONToFirestore(jsonFilePath, collectionName)
  .then(() => {
    console.log('\n🎉 All done! Check your Firestore console to verify the data.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Upload failed:', error);
    process.exit(1);
  });
