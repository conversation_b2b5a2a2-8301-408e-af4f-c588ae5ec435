package com.example.arham

import android.app.Application
import android.content.Context
import com.example.arham.ui.DataManager
import com.example.arham.firebase.FirebaseManager
import dagger.hilt.android.HiltAndroidApp

@HiltAndroidApp
class ArhamApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        // Initialize any application-wide components here

        // Initialize Firebase
        FirebaseManager.initialize(this)
    }
    
    companion object {
        // For accessing application context from non-activity classes
        lateinit var appContext: Context
            private set
    }
    
    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        appContext = base
    }
}
