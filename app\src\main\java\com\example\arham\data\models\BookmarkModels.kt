package com.example.arham.data.models

import androidx.compose.ui.graphics.Color

data class Bookmark(
    val id: String,
    val bookId: String,
    val bookTitle: String,
    val author: String,
    val highlightedText: String,
    val context: String, // Surrounding text for context
    val pageNumber: Int,
    val timestamp: Long,
    val highlightColor: Color = Color.Yellow.copy(alpha = 0.3f),
    val notes: String = ""
)

data class ReadingProgress(
    val bookId: String,
    val progress: Float, // 0.0 to 1.0
    val lastReadPosition: Int,
    val totalPages: Int,
    val lastReadTime: Long
)

data class Book(
    val id: String,
    val title: String,
    val author: String,
    val content: String,
    val totalPages: Int,
    val category: String,
    val coverImageUrl: String? = null,
    val description: String = "",
    val publishedDate: String = "",
    val language: String = "Hindi"
)

object BookmarkRepository {
    private val bookmarks = mutableListOf<Bookmark>()
    private val readingProgress = mutableMapOf<String, ReadingProgress>()
    
    fun addBookmark(bookmark: Bookmark) {
        bookmarks.add(bookmark)
    }
    
    fun removeBookmark(bookmarkId: String) {
        bookmarks.removeAll { it.id == bookmarkId }
    }
    
    fun getBookmarks(): List<Bookmark> {
        return bookmarks.sortedByDescending { it.timestamp }
    }
    
    fun getBookmarksByBook(bookId: String): List<Bookmark> {
        return bookmarks.filter { it.bookId == bookId }
    }
    
    fun updateReadingProgress(bookId: String, progress: ReadingProgress) {
        readingProgress[bookId] = progress
    }
    
    fun getReadingProgress(bookId: String): ReadingProgress? {
        return readingProgress[bookId]
    }
    
    fun searchBookmarks(query: String): List<Bookmark> {
        return bookmarks.filter { 
            it.highlightedText.contains(query, ignoreCase = true) ||
            it.bookTitle.contains(query, ignoreCase = true) ||
            it.author.contains(query, ignoreCase = true) ||
            it.notes.contains(query, ignoreCase = true)
        }
    }
}
