package com.example.arham.data

import com.example.arham.data.models.*
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.runBlocking

object BhajanContent {

    fun getBhajanContent(title: String): String {
        // First try to load from database
        return try {
            runBlocking {
                val db = FirebaseFirestore.getInstance()

                // Check if this is from spiritual_reading collection (content_ prefix)
                if (title.startsWith("content_")) {
                    val docId = title.removePrefix("content_")
                    val result = db.collection("spiritual_reading")
                        .document(docId)
                        .get()
                        .await()

                    if (result.exists()) {
                        result.getString("content") ?: getStaticContent(title)
                    } else {
                        getStaticContent(title)
                    }
                } else {
                    getStaticContent(title)
                }
            }
        } catch (e: Exception) {
            getStaticContent(title)
        }
    }

    private fun getStaticContent(title: String): String {
        return when (title) {
            "भिक्षु म्हारै प्रगट्या जी" -> """
                भिक्षु म्हारै प्रगट्या जी, धन्य धन्य यो दिन
                सुख शांति रो दाता आयो, हरष्यो म्हारो मन
                
                तेरापंथ रो तारणहार, भिक्षु स्वामी आयो
                अहिंसा रो संदेश लेकर, जग में ज्योति जगायो
                
                सत्य अहिंसा रो पंथ दिखायो
                मानव धर्म सिखायो
                भिक्षु म्हारै प्रगट्या जी, धन्य धन्य यो दिन
                
                करुणा दया रो सागर आयो
                प्रेम भाव सिखायो
                जीव दया रो महत्व बतायो
                पाप मुक्ति रो राह दिखायो
                
                भिक्षु स्वामी रो नाम सुमिरां
                मन में शांति पावां
                तेरापंथ रो गौरव गावां
                आत्मा रो कल्याण करावां
            """.trimIndent()
            
            "चैत्य पुरुष" -> """
                चैत्य पुरुष भिक्षु स्वामी, तुम हो जग के नेता
                सत्य अहिंसा के मार्ग पर, तुमने जग को चेता
                
                तेरापंथ के संस्थापक तुम
                धर्म के रक्षक तुम
                अहिंसा के प्रचारक तुम
                सत्य के उपदेशक तुम
                
                चैत्य पुरुष भिक्षु स्वामी, तुम हो जग के नेता
                
                जीव दया का संदेश दिया
                मानव धर्म सिखाया
                पाप मुक्ति का मार्ग दिखाया
                आत्मा का कल्याण कराया
                
                तुम्हारे चरणों में शीश नवाएं
                तुम्हारा आशीर्वाद पाएं
                भिक्षु स्वामी की जय जयकार
                हो तेरापंथ का उद्धार
            """.trimIndent()
            
            "वंदना लो झेलो" -> """
                वंदना लो झेलो भिक्षु स्वामी री
                चरणां में शीश नवावां
                
                गुरु महिमा अपरंपार है
                गुरु कृपा का भंडार है
                गुरु चरणों में जो आए
                वो भव सागर पार हो जाए
                
                वंदना लो झेलो भिक्षु स्वामी री
                चरणां में शीश नवावां
                
                सत्य अहिंसा का पाठ पढ़ाया
                धर्म का मार्ग दिखाया
                जीव दया का महत्व बताया
                आत्मा का कल्याण कराया
                
                भिक्षु स्वामी के चरणों में
                हम सब शीश नवाते हैं
                उनकी कृपा से जीवन में
                शांति और सुख पाते हैं
            """.trimIndent()
            
            "सिरियारी रो संत" -> """
                सिरियारी रो संत भिक्षु स्वामी
                तेरापंथ रो तारणहार
                
                राजस्थान री धरती पर
                जन्म लियो महान संत ने
                सत्य अहिंसा रो संदेश
                दियो जग के कल्याण ने
                
                सिरियारी रो संत भिक्षु स्वामी
                तेरापंथ रो तारणहार
                
                धर्म ध्वजा फहराई तुमने
                अहिंसा रो पाठ पढ़ाया
                जीव दया रो महत्व बताया
                मानव धर्म सिखाया
                
                भिक्षु स्वामी री जय जयकार
                हो तेरापंथ का उद्धार
                सिरियारी से निकला जो ज्योति
                आज भी जग को राह दिखाती
            """.trimIndent()
            
            "ओ म्हांरा गुरुदेव!" -> """
                ओ म्हांरा गुरुदेव! भिक्षु स्वामी
                तुम हो जग के नेता
                सत्य अहिंसा के मार्ग पर
                तुमने जग को चेता
                
                गुरुदेव तुम्हारी महिमा न्यारी
                गुरुदेव तुम्हारी कृपा भारी
                गुरुदेव के चरणों में जो आए
                वो भव सागर पार हो जाए
                
                ओ म्हांरा गुरुदेव! भिक्षु स्वामी
                तुम हो जग के नेता
                
                तेरापंथ के संस्थापक तुम
                धर्म के रक्षक तुम
                अहिंसा के प्रचारक तुम
                सत्य के उपदेशक तुम
                
                गुरुदेव की जय जयकार
                हो तेरापंथ का उद्धार
                भिक्षु स्वामी के नाम से
                जग में शांति का प्रसार
            """.trimIndent()
            
            "घणा सुहावो माता" -> """
                घणा सुहावो माता, भिक्षु स्वामी रो नाम
                मन में बसायो राखूं, सदा करूं गुणगान
                
                सुहावो लागे मन नै
                भिक्षु स्वामी रो नाम
                जपते रहूं दिन रात
                करूं सदा गुणगान
                
                घणा सुहावो माता, भिक्षु स्वामी रो नाम
                मन में बसायो राखूं, सदा करूं गुणगान
                
                तेरापंथ रो तारणहार
                अहिंसा रो उपदेशक
                सत्य धर्म रो प्रचारक
                जीव दया रो शिक्षक
                
                भिक्षु स्वामी री कृपा से
                मिले जीवन में शांति
                उनके नाम के जाप से
                मिले मन को प्रसन्नता
            """.trimIndent()
            
            else -> """
                $title
                
                यह एक पवित्र भजन है जो आचार्य भिक्षु की महिमा में गाया जाता है।
                
                भिक्षु स्वामी तेरापंथ के संस्थापक थे और उन्होंने सत्य, अहिंसा और जीव दया का संदेश दिया।
                
                उनकी शिक्षाएं आज भी हमारे जीवन में प्रासंगिक हैं और हमें सही मार्ग दिखाती हैं।
                
                इस भजन के माध्यम से हम उनकी महिमा का गुणगान करते हैं और उनसे आशीर्वाद प्राप्त करते हैं।
                
                ॐ भिक्षु भिक्षु नमः
                जय भिक्षु स्वामी
                जय तेरापंथ
            """.trimIndent()
        }
    }
    
    fun getBhajanAuthor(title: String): String {
        // First try to load from database
        return try {
            runBlocking {
                val db = FirebaseFirestore.getInstance()
                val result = db.collection("content")
                    .whereEqualTo("TitleName", title)
                    .limit(1)
                    .get()
                    .await()

                if (!result.isEmpty) {
                    result.documents.first().getString("AuthorName") ?: "आचार्य भिक्षु"
                } else {
                    "आचार्य भिक्षु" // Default author
                }
            }
        } catch (e: Exception) {
            "आचार्य भिक्षु" // Default author
        }
    }
    
    fun getBhajanCategory(title: String): String {
        // First try to load from database
        return try {
            runBlocking {
                val db = FirebaseFirestore.getInstance()
                val result = db.collection("content")
                    .whereEqualTo("TitleName", title)
                    .limit(1)
                    .get()
                    .await()

                if (!result.isEmpty) {
                    result.documents.first().getString("CategoryName") ?: getStaticCategory(title)
                } else {
                    getStaticCategory(title)
                }
            }
        } catch (e: Exception) {
            getStaticCategory(title)
        }
    }

    private fun getStaticCategory(title: String): String {
        return when {
            title.contains("आरती") -> "आरती"
            title.contains("स्तुति") -> "स्तुति"
            title.contains("भजन") -> "भजन"
            title.contains("नाम") -> "नाम स्मरण"
            else -> "भक्ति गीत"
        }
    }
}
