package com.example.arham.domain.models

/**
 * Domain model for spiritual content
 * Clean architecture - Domain layer entity
 */
data class SpiritualContent(
    val id: String,
    val title: String,
    val content: String,
    val author: String,
    val category: ContentCategory,
    val type: ContentType,
    val language: Language,
    val tags: List<String> = emptyList(),
    val searchKeywords: List<String> = emptyList(),
    val isPopular: Boolean = false,
    val viewCount: Long = 0,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

enum class ContentCategory {
    BHIKSHU, TULSI, KALU, MAHAPRAJNA, GEET, MANGAL, 
    DAINIK_SWADHYAY, <PERSON>H<PERSON><PERSON><PERSON><PERSON>_SMARAN, READ_SCREEN, GENERAL
}

enum class ContentType {
    PRAYER, SONG, STORY, TEACHING, MEDITATION, MANTRA
}

enum class Language {
    HINDI, ENGLISH, SANSKRIT
}
