package com.example.arham.data.seeding

import com.example.arham.data.models.*
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.tasks.await

class DataSeeder {
    private val db = FirebaseFirestore.getInstance()
    
    suspend fun seedAllData() {
        seedAuthors()
        seedBhikshuContent()
        seedTulsiContent()
        seedKaluContent()
        seedMahaprajnaContent()
        seedGeetContent()
        seedMangalContent()
        seedSearchIndex()
        seedContentCollections()
    }
    
    private suspend fun seedAuthors() {
        val authors = listOf(
            Author(
                id = "acharya_bhikshu",
                name = "आचार्य भिक्षु",
                biography = "तेरापंथ के संस्थापक आचार्य भिक्षु (1726-1803) एक महान जैन आचार्य थे। उन्होंने सत्य, अहिंसा और जीव दया का संदेश दिया।",
                birthDate = "1726",
                birthPlace = "सिरियारी, राजस्थान",
                achievements = listOf("तेरापंथ की स्थापना", "धर्म सुधार", "अहिंसा का प्रचार"),
                famousWorks = listOf("भिक्षु भजन", "तेरापंथ सिद्धांत", "धर्म उपदेश"),
                category = ContentCategory.BHIKSHU
            ),
            Author(
                id = "tulsidas",
                name = "तुलसीदास",
                biography = "गोस्वामी तुलसीदास (1532-1623) हिंदी साहित्य के महान कवि थे। रामचरितमानस के रचयिता।",
                birthDate = "1532",
                birthPlace = "राजापुर, उत्तर प्रदेश",
                achievements = listOf("रामचरितमानस", "हनुमान चालीसा", "विनय पत्रिका"),
                famousWorks = listOf("रामचरितमानस", "हनुमान चालीसा", "दोहावली"),
                category = ContentCategory.TULSI
            ),
            Author(
                id = "sant_kalu",
                name = "संत कालू",
                biography = "संत कालू एक प्रसिद्ध आध्यात्मिक गुरु थे जिन्होंने सरल भाषा में गहरे आध्यात्मिक सत्य प्रस्तुत किए।",
                birthDate = "अज्ञात",
                birthPlace = "राजस्थान",
                achievements = listOf("आध्यात्मिक शिक्षा", "लोक कथाएं", "नैतिक उपदेश"),
                famousWorks = listOf("कालू की कहानियां", "आध्यात्मिक कथाएं"),
                category = ContentCategory.KALU
            ),
            Author(
                id = "acharya_mahaprajna",
                name = "आचार्य महाप्रज्ञा",
                biography = "आचार्य महाप्रज्ञा (1920-2010) तेरापंथ के दसवें आचार्य थे। वे एक महान विद्वान, लेखक और आध्यात्मिक गुरु थे।",
                birthDate = "1920",
                birthPlace = "तमकोर, राजस्थान",
                achievements = listOf("प्रेक्षा ध्यान", "अनुवाद कार्य", "आध्यात्मिक लेखन"),
                famousWorks = listOf("प्रेक्षा ध्यान", "जैन दर्शन", "आत्म साधना"),
                category = ContentCategory.MAHAPRAJNA
            )
        )
        
        authors.forEach { author ->
            db.collection("authors").document(author.id).set(author).await()
        }
    }
    
    private suspend fun seedBhikshuContent() {
        val bhikshuBhajans = listOf(
            "भिक्षु म्हारै प्रगट्या जी", "चैत्य पुरुष", "वंदना लो झेलो", "सिरियारी रो संत",
            "ओ म्हांरा गुरुदेव!", "घणा सुहावो माता", "स्वामीजी! थांरी साधना री",
            "रुं रूं में सांवरियो", "स्वामी भीखणजी रो नाम", "बादळियो आंखड़ल्यां में",
            "म्हांनै घणा सुहावै जी", "निहारा तुमको कितनी बार", "हमारे भाग्य बड़े बलवान",
            "मंगल है आज तेरे शासन में", "भीखणजी स्वामी! भारी मर्यादा", "गुरुदेव! थांरी खिण-खिण याद",
            "प्रगट्यो एक नयो उद्योत", "प्रभो! यह तेरापन्थ महान", "देखो मर्यादा की महिमां",
            "देवते! बतलाओ", "स्वामी पंथ दिखाओ जी", "भिक्षु-स्मरण", "म्हारै सांस-सांस में बोलै रे",
            "स्वामीजी म्हानै दर्शन दीन्हाजी", "भिक्षुस्वाम भिक्षुस्वाम रटन लगावां",
            "भिक्षु-भिक्षु-भिक्षु म्हांरी आतमा", "भज मन भिखु स्याम", "आस्था रा अनुपम दीप",
            "स्वामीजी का नाम हमारे", "ओ स्वामीजी रो नाम", "स्वामीजी! थांरै चरणां",
            "आओ स्वामीजी", "धन्य धन्य भीखणजी स्वाम", "स्वामीजी! आओ देखल्यो",
            "भिक्षु-भिक्षु भजन हो", "आओ-आओ भिक्षु स्वामी", "स्वामीजी रै नाम री", "भिक्षु आरती"
        )
        
        bhikshuBhajans.forEachIndexed { index, title ->
            val content = SpiritualContent(
                id = "bhikshu_$index",
                title = title,
                content = getBhajanContent(title),
                author = "आचार्य भिक्षु",
                category = ContentCategory.BHIKSHU,
                type = ContentType.BHAJAN,
                language = Language.HINDI,
                tags = listOf("भिक्षु", "भजन", "तेरापंथ", "आध्यात्म"),
                searchKeywords = generateSearchKeywords(title, "आचार्य भिक्षु"),
                isPopular = index < 10, // First 10 are popular
                viewCount = (100..1000).random().toLong()
            )
            
            db.collection("spiritual_content").document(content.id).set(content).await()
        }
    }
    
    private suspend fun seedTulsiContent() {
        val tulsiContent = listOf(
            "तुलसी के दोहे", "हनुमान चालीसा", "रामायण के श्लोक", "राम नाम की महिमा",
            "सीता राम भजन", "हनुमान स्तुति", "राम रक्षा स्तोत्र", "तुलसी की वाणी",
            "रामचरितमानस के पद", "भक्ति रस", "राम कथा", "हनुमान आरती"
        )
        
        tulsiContent.forEachIndexed { index, title ->
            val content = SpiritualContent(
                id = "tulsi_$index",
                title = title,
                content = getTulsiContent(title),
                author = "तुलसीदास",
                category = ContentCategory.TULSI,
                type = if (title.contains("दोहे")) ContentType.TEACHING else ContentType.BHAJAN,
                language = Language.HINDI,
                tags = listOf("तुलसी", "राम", "हनुमान", "भक्ति"),
                searchKeywords = generateSearchKeywords(title, "तुलसीदास"),
                isPopular = index < 5,
                viewCount = (200..1500).random().toLong()
            )
            
            db.collection("spiritual_content").document(content.id).set(content).await()
        }
    }
    
    private suspend fun seedKaluContent() {
        val kaluContent = listOf(
            "कालू की कहानियां", "आध्यात्मिक कथाएं", "संत कालू के उपदेश", "जीवन के सत्य",
            "नैतिक शिक्षा", "आत्मा की खोज", "सच्चाई की राह", "प्रेम और करुणा"
        )
        
        kaluContent.forEachIndexed { index, title ->
            val content = SpiritualContent(
                id = "kalu_$index",
                title = title,
                content = getKaluContent(title),
                author = "संत कालू",
                category = ContentCategory.KALU,
                type = ContentType.STORY,
                language = Language.HINDI,
                tags = listOf("कालू", "कहानी", "उपदेश", "नैतिकता"),
                searchKeywords = generateSearchKeywords(title, "संत कालू"),
                isPopular = index < 3,
                viewCount = (150..800).random().toLong()
            )
            
            db.collection("spiritual_content").document(content.id).set(content).await()
        }
    }
    
    private suspend fun seedMahaprajnaContent() {
        val mahaprajnaContent = listOf(
            "प्रेक्षा ध्यान", "आत्म साधना", "जैन दर्शन", "महाप्रज्ञा के सूत्र",
            "आध्यात्मिक ज्ञान", "मन की शुद्धता", "कर्म सिद्धांत", "मोक्ष मार्ग"
        )
        
        mahaprajnaContent.forEachIndexed { index, title ->
            val content = SpiritualContent(
                id = "mahaprajna_$index",
                title = title,
                content = getMahaprajnaContent(title),
                author = "आचार्य महाप्रज्ञा",
                category = ContentCategory.MAHAPRAJNA,
                type = ContentType.TEACHING,
                language = Language.HINDI,
                tags = listOf("महाप्रज्ञा", "ध्यान", "दर्शन", "साधना"),
                searchKeywords = generateSearchKeywords(title, "आचार्य महाप्रज्ञा"),
                isPopular = index < 4,
                viewCount = (300..1200).random().toLong()
            )
            
            db.collection("spiritual_content").document(content.id).set(content).await()
        }
    }
    
    private suspend fun seedGeetContent() {
        val geetContent = listOf(
            "आध्यात्मिक गीत", "भक्ति संगीत", "मंगल गान", "प्रार्थना गीत",
            "शांति मंत्र", "ध्यान संगीत", "आरती गीत", "स्तुति गान"
        )
        
        geetContent.forEachIndexed { index, title ->
            val content = SpiritualContent(
                id = "geet_$index",
                title = title,
                content = getGeetContent(title),
                author = "विभिन्न संत",
                category = ContentCategory.GENERAL,
                type = ContentType.GEET,
                language = Language.HINDI,
                tags = listOf("गीत", "संगीत", "भक्ति", "आध्यात्म"),
                searchKeywords = generateSearchKeywords(title, "विभिन्न संत"),
                isPopular = index < 3,
                viewCount = (100..600).random().toLong()
            )
            
            db.collection("spiritual_content").document(content.id).set(content).await()
        }
    }
    
    private suspend fun seedMangalContent() {
        val mangalContent = listOf(
            "मंगल आरती", "शुभ मंगल", "कल्याण मंत्र", "सुख शांति प्रार्थना",
            "मंगल कामना", "आशीर्वाद गीत", "शुभकामना मंत्र", "कल्याण पाठ"
        )
        
        mangalContent.forEachIndexed { index, title ->
            val content = SpiritualContent(
                id = "mangal_$index",
                title = title,
                content = getMangalContent(title),
                author = "विभिन्न संत",
                category = ContentCategory.MANGAL,
                type = ContentType.PRAYER,
                language = Language.HINDI,
                tags = listOf("मंगल", "आरती", "प्रार्थना", "कल्याण"),
                searchKeywords = generateSearchKeywords(title, "विभिन्न संत"),
                isPopular = index < 2,
                viewCount = (80..400).random().toLong()
            )
            
            db.collection("spiritual_content").document(content.id).set(content).await()
        }
    }
    
    private suspend fun seedSearchIndex() {
        // This will be populated automatically when content is added
        // For now, we'll create a few sample entries
    }
    
    private suspend fun seedContentCollections() {
        val collections = listOf(
            ContentCollection(
                id = "popular_bhajans",
                name = "लोकप्रिय भजन",
                description = "सबसे लोकप्रिय और प्रिय भजनों का संग्रह",
                category = ContentCategory.GENERAL,
                isPublic = true
            ),
            ContentCollection(
                id = "bhikshu_collection",
                name = "आचार्य भिक्षु संग्रह",
                description = "आचार्य भिक्षु के सभी भजन और उपदेश",
                category = ContentCategory.BHIKSHU,
                isPublic = true
            ),
            ContentCollection(
                id = "daily_prayers",
                name = "दैनिक प्रार्थना",
                description = "रोज़ाना पढ़ने योग्य प्रार्थनाएं और मंत्र",
                category = ContentCategory.GENERAL,
                isPublic = true
            )
        )
        
        collections.forEach { collection ->
            db.collection("content_collections").document(collection.id).set(collection).await()
        }
    }
    
    // Helper functions for content generation
    private fun generateSearchKeywords(title: String, author: String): List<String> {
        val keywords = mutableListOf<String>()
        
        // Add title words
        keywords.addAll(title.split(" ").filter { it.isNotBlank() })
        
        // Add author words
        keywords.addAll(author.split(" ").filter { it.isNotBlank() })
        
        // Add common spiritual terms
        keywords.addAll(listOf("भजन", "गीत", "प्रार्थना", "आध्यात्म", "भक्ति"))
        
        return keywords.distinct()
    }
    
    // Content generation functions (simplified for brevity)
    private fun getBhajanContent(title: String): String {
        return "यह $title का पूरा पाठ है। इसमें आध्यात्मिक भावनाएं और भक्ति रस भरा हुआ है।"
    }
    
    private fun getTulsiContent(title: String): String {
        return "तुलसीदास जी की $title। इसमें राम भक्ति और आध्यात्मिक ज्ञान है।"
    }
    
    private fun getKaluContent(title: String): String {
        return "संत कालू की $title। इसमें जीवन के महत्वपूर्ण सत्य और नैतिक शिक्षा है।"
    }
    
    private fun getMahaprajnaContent(title: String): String {
        return "आचार्य महाप्रज्ञा की $title। इसमें गहरा आध्यात्मिक ज्ञान और ध्यान की विधि है।"
    }
    
    private fun getGeetContent(title: String): String {
        return "$title - एक सुंदर आध्यात्मिक गीत जो मन को शांति प्रदान करता है।"
    }
    
    private fun getMangalContent(title: String): String {
        return "$title - मंगल कामना और शुभ आशीर्वाद से भरी प्रार्थना।"
    }
}
