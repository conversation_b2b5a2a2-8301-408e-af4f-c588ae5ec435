#!/usr/bin/env python3
"""
Excel to JSON Converter for ArhamApp Firestore Data
Converts Excel/CSV files to JSON format suitable for Firestore upload
"""

import pandas as pd
import json
import uuid
from datetime import datetime
import sys
import os

def excel_to_json(file_path, output_path=None, sheet_name=0):
    """
    Convert Excel file to JSON format for Firestore
    
    Args:
        file_path: Path to Excel file
        output_path: Output JSON file path (optional)
        sheet_name: Sheet name or index (default: 0)
    """
    
    try:
        # Read Excel file
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
        else:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
        
        print(f"✅ File loaded successfully: {len(df)} rows found")
        print(f"📋 Columns: {list(df.columns)}")
        
        # Convert to JSON format suitable for Firestore
        json_data = []
        
        for index, row in df.iterrows():
            # Create document with unique ID
            doc = {
                "id": str(uuid.uuid4()),
                "createdAt": int(datetime.now().timestamp() * 1000),
                "updatedAt": int(datetime.now().timestamp() * 1000)
            }
            
            # Add all columns as fields
            for column in df.columns:
                value = row[column]
                
                # Handle NaN values
                if pd.isna(value):
                    value = ""
                elif isinstance(value, (int, float)):
                    value = int(value) if value == int(value) else float(value)
                else:
                    value = str(value).strip()
                
                # Clean column name for Firestore
                field_name = column.strip().replace(' ', '_').lower()
                doc[field_name] = value
            
            json_data.append(doc)
        
        # Generate output filename if not provided
        if not output_path:
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            output_path = f"{base_name}_firestore.json"
        
        # Save JSON file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)
        
        print(f"🎉 JSON file created: {output_path}")
        print(f"📊 Total documents: {len(json_data)}")
        
        # Show sample document
        if json_data:
            print("\n📋 Sample document:")
            print(json.dumps(json_data[0], indent=2, ensure_ascii=False))
        
        return output_path
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def create_spiritual_content_json(file_path):
    """
    Convert Excel to Spiritual Content format for ArhamApp
    Expected columns: title, content, author, category, type, language
    """
    
    try:
        # Read Excel file
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
        else:
            df = pd.read_excel(file_path)
        
        spiritual_content = []
        
        for index, row in df.iterrows():
            content = {
                "id": str(uuid.uuid4()),
                "title": str(row.get('title', '')).strip(),
                "content": str(row.get('content', '')).strip(),
                "author": str(row.get('author', '')).strip(),
                "category": str(row.get('category', 'GENERAL')).upper(),
                "type": str(row.get('type', 'TEACHING')).upper(),
                "language": str(row.get('language', 'HINDI')).upper(),
                "tags": str(row.get('tags', '')).split(',') if row.get('tags') else [],
                "searchKeywords": [],
                "isPopular": bool(row.get('isPopular', False)),
                "viewCount": int(row.get('viewCount', 0)),
                "createdAt": int(datetime.now().timestamp() * 1000),
                "updatedAt": int(datetime.now().timestamp() * 1000)
            }
            
            # Generate search keywords
            keywords = []
            keywords.extend(content['title'].lower().split())
            keywords.extend(content['author'].lower().split())
            keywords.extend([tag.strip().lower() for tag in content['tags']])
            content['searchKeywords'] = list(set(keywords))
            
            spiritual_content.append(content)
        
        # Save JSON
        output_path = "spiritual_content_firestore.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(spiritual_content, f, indent=2, ensure_ascii=False)
        
        print(f"🎉 Spiritual Content JSON created: {output_path}")
        print(f"📊 Total content: {len(spiritual_content)}")
        
        return output_path
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

if __name__ == "__main__":
    print("🔄 Excel to JSON Converter for ArhamApp")
    print("=" * 50)
    
    if len(sys.argv) < 2:
        print("Usage: python excel_to_json_converter.py <excel_file_path>")
        print("Example: python excel_to_json_converter.py data.xlsx")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        sys.exit(1)
    
    # Ask user for conversion type
    print("\nSelect conversion type:")
    print("1. General Excel to JSON")
    print("2. Spiritual Content format")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        excel_to_json(file_path)
    elif choice == "2":
        create_spiritual_content_json(file_path)
    else:
        print("❌ Invalid choice")
