package com.example.arham.domain.usecases

import com.example.arham.domain.models.Habit
import com.example.arham.domain.models.HabitCompletion
import com.example.arham.domain.models.HabitWithCompletions
import com.example.arham.domain.repository.HabitRepository
import kotlinx.coroutines.flow.Flow
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject

/**
 * Use case for managing habits
 * Clean architecture - Domain layer business logic
 */
class ManageHabitsUseCase @Inject constructor(
    private val repository: HabitRepository
) {
    
    suspend fun getAllHabitsWithCompletions(): List<HabitWithCompletions> {
        return try {
            repository.getHabitsWithCompletions()
                .sortedWith(
                    compareByDescending<HabitWithCompletions> { it.habit.isActive }
                        .thenByDescending { it.currentStreak }
                        .thenBy { it.habit.name }
                )
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    fun getHabitsFlow(): Flow<List<Habit>> {
        return repository.getHabitsFlow()
    }
    
    suspend fun addHabit(habit: Habit): Result<Unit> {
        return repository.addHabit(habit)
    }
    
    suspend fun completeHabit(habitId: String, count: Int = 1, notes: String = ""): Result<Unit> {
        val today = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
        
        // Check if already completed today
        val existingCompletion = repository.getTodayCompletion(habitId)
        
        return if (existingCompletion != null) {
            // Update existing completion
            val updatedCompletion = existingCompletion.copy(
                count = existingCompletion.count + count,
                notes = if (notes.isNotEmpty()) notes else existingCompletion.notes,
                completedAt = System.currentTimeMillis()
            )
            repository.addHabitCompletion(updatedCompletion)
        } else {
            // Create new completion
            val completion = HabitCompletion(
                id = UUID.randomUUID().toString(),
                habitId = habitId,
                date = today,
                count = count,
                notes = notes
            )
            repository.addHabitCompletion(completion)
        }
    }
    
    suspend fun removeHabitCompletion(habitId: String): Result<Unit> {
        val todayCompletion = repository.getTodayCompletion(habitId)
        return if (todayCompletion != null) {
            repository.removeHabitCompletion(todayCompletion.id)
        } else {
            Result.failure(Exception("No completion found for today"))
        }
    }
    
    suspend fun calculateStreak(habitId: String): Int {
        return repository.calculateStreak(habitId)
    }
    
    suspend fun getHabitStats(habitId: String): Map<String, Any> {
        return repository.getHabitStats(habitId)
    }
}
