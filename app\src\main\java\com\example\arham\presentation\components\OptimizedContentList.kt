package com.example.arham.presentation.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.example.arham.domain.models.SpiritualContent

/**
 * Optimized content list with performance improvements
 * - Proper key usage for recomposition optimization
 * - Lazy loading
 * - State hoisting
 * - Minimal recomposition
 */
@Composable
fun OptimizedContentList(
    content: List<SpiritualContent>,
    onContentClick: (SpiritualContent) -> Unit,
    modifier: Modifier = Modifier,
    isLoading: Boolean = false,
    error: String? = null,
    onRetry: () -> Unit = {}
) {
    val listState = rememberLazyListState()
    
    Box(modifier = modifier.fillMaxSize()) {
        when {
            isLoading && content.isEmpty() -> {
                LoadingIndicator(
                    modifier = Modifier.align(Alignment.Center)
                )
            }
            
            error != null && content.isEmpty() -> {
                ErrorMessage(
                    error = error,
                    onRetry = onRetry,
                    modifier = Modifier.align(Alignment.Center)
                )
            }
            
            content.isEmpty() -> {
                EmptyState(
                    modifier = Modifier.align(Alignment.Center)
                )
            }
            
            else -> {
                LazyColumn(
                    state = listState,
                    contentPadding = PaddingValues(16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                    modifier = Modifier.fillMaxSize()
                ) {
                    items(
                        items = content,
                        key = { it.id } // Important for performance
                    ) { contentItem ->
                        OptimizedContentCard(
                            content = contentItem,
                            onClick = { onContentClick(contentItem) }
                        )
                    }
                    
                    if (isLoading) {
                        item {
                            LoadingIndicator(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(16.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun LoadingIndicator(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator()
    }
}

@Composable
private fun ErrorMessage(
    error: String,
    onRetry: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = error,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.error
        )
        Spacer(modifier = Modifier.height(8.dp))
        Button(onClick = onRetry) {
            Text("Retry")
        }
    }
}

@Composable
private fun EmptyState(
    modifier: Modifier = Modifier
) {
    Text(
        text = "No content available",
        style = MaterialTheme.typography.bodyLarge,
        modifier = modifier
    )
}
