#!/usr/bin/env python3
"""
ArhamApp Simple Data Converter
Converts Excel to JSON for Firestore
"""

import pandas as pd
import json
import uuid
from datetime import datetime
import sys
import os

def convert_excel_to_json(file_path):
    """
    Simple Excel to JSON converter
    """
    
    try:
        print("🔄 Reading Excel file...")
        print(f"📁 File: {file_path}")
        
        # Read all sheets
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        
        print(f"📋 Found {len(sheet_names)} sheets:")
        for i, name in enumerate(sheet_names):
            print(f"   Sheet {i+1}: {name}")
        
        # Read each sheet
        all_data = {}
        hierarchy = {
            "arhamApp": {
                "sheets": {},
                "navigation": {},
                "content": [],
                "metadata": {
                    "totalSheets": len(sheet_names),
                    "fileName": os.path.basename(file_path),
                    "createdAt": int(datetime.now().timestamp() * 1000)
                }
            }
        }
        
        for i, sheet_name in enumerate(sheet_names):
            print(f"\n🔄 Processing Sheet {i+1}: {sheet_name}")
            
            try:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                print(f"   ✅ Rows: {len(df)}, Columns: {len(df.columns)}")
                print(f"   📋 Columns: {list(df.columns)}")
                
                # Show sample data
                if len(df) > 0:
                    print(f"   📊 Sample data from first row:")
                    for col in df.columns[:3]:  # Show first 3 columns
                        sample_value = df.iloc[0][col] if not pd.isna(df.iloc[0][col]) else "Empty"
                        print(f"      {col}: {sample_value}")
                
                # Convert to JSON
                sheet_data = []
                for index, row in df.iterrows():
                    item = {
                        "id": str(uuid.uuid4()),
                        "sheetName": sheet_name,
                        "sheetIndex": i,
                        "rowIndex": index,
                        "data": {}
                    }
                    
                    # Add all columns
                    for col in df.columns:
                        value = row[col]
                        if pd.notna(value):
                            if isinstance(value, (int, float)):
                                item["data"][col] = value
                            else:
                                item["data"][col] = str(value).strip()
                        else:
                            item["data"][col] = ""
                    
                    item["createdAt"] = int(datetime.now().timestamp() * 1000)
                    sheet_data.append(item)
                    hierarchy["arhamApp"]["content"].append(item)
                
                # Store sheet data
                hierarchy["arhamApp"]["sheets"][sheet_name] = {
                    "name": sheet_name,
                    "index": i,
                    "rowCount": len(df),
                    "columnCount": len(df.columns),
                    "columns": list(df.columns),
                    "data": sheet_data
                }
                
                all_data[sheet_name] = df.to_dict('records')
                print(f"   ✅ Processed {len(sheet_data)} items")
                
            except Exception as e:
                print(f"   ❌ Error processing sheet {sheet_name}: {str(e)}")
                continue
        
        # Create navigation structure
        create_navigation_structure(hierarchy)
        
        # Save files
        save_output_files(hierarchy, all_data, file_path)
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def create_navigation_structure(hierarchy):
    """
    Create navigation structure from sheets
    """
    
    sheets = hierarchy["arhamApp"]["sheets"]
    navigation = {}
    
    # Level mapping based on sheet index
    level_names = ["exploreScreen", "section", "category", "author", "title"]
    
    for sheet_name, sheet_info in sheets.items():
        level_index = sheet_info["index"]
        level_name = level_names[level_index] if level_index < len(level_names) else f"level_{level_index}"
        
        navigation[sheet_name] = {
            "name": sheet_name,
            "level": level_index,
            "type": level_name,
            "itemCount": sheet_info["rowCount"],
            "columns": sheet_info["columns"],
            "items": []
        }
        
        # Add items from this sheet
        for item in sheet_info["data"]:
            nav_item = {
                "id": item["id"],
                "name": get_item_name(item["data"]),
                "level": level_index,
                "type": level_name,
                "data": item["data"]
            }
            navigation[sheet_name]["items"].append(nav_item)
    
    hierarchy["arhamApp"]["navigation"] = navigation

def get_item_name(data):
    """
    Get the main name/title from item data
    """
    # Try common name fields
    name_fields = ["name", "title", "Name", "Title", "text", "content", "cardTitle", "SectionTitle", "CategoryName", "AuthorName", "TitleName"]
    
    for field in name_fields:
        if field in data and data[field]:
            return str(data[field]).strip()
    
    # If no name field found, use first non-empty value
    for key, value in data.items():
        if value and str(value).strip():
            return str(value).strip()
    
    return "Unnamed Item"

def save_output_files(hierarchy, all_data, original_file_path):
    """
    Save multiple output formats
    """
    
    base_name = os.path.splitext(os.path.basename(original_file_path))[0]
    output_dir = os.path.dirname(original_file_path)
    
    # 1. Complete hierarchy
    hierarchy_file = os.path.join(output_dir, f"{base_name}_hierarchy.json")
    with open(hierarchy_file, 'w', encoding='utf-8') as f:
        json.dump(hierarchy, f, indent=2, ensure_ascii=False)
    print(f"✅ Saved: {hierarchy_file}")
    
    # 2. Navigation only
    nav_file = os.path.join(output_dir, f"{base_name}_navigation.json")
    nav_data = {"navigation": hierarchy["arhamApp"]["navigation"]}
    with open(nav_file, 'w', encoding='utf-8') as f:
        json.dump(nav_data, f, indent=2, ensure_ascii=False)
    print(f"✅ Saved: {nav_file}")
    
    # 3. Flat content for Firestore
    firestore_file = os.path.join(output_dir, f"{base_name}_firestore.json")
    flat_data = []
    
    for item in hierarchy["arhamApp"]["content"]:
        flat_item = {
            "id": item["id"],
            "title": get_item_name(item["data"]),
            "content": json.dumps(item["data"], ensure_ascii=False),
            "sheetName": item["sheetName"],
            "level": item["sheetIndex"],
            "metadata": {
                "originalRow": item["rowIndex"],
                "sheetName": item["sheetName"]
            },
            "createdAt": item["createdAt"],
            "updatedAt": item["createdAt"]
        }
        flat_data.append(flat_item)
    
    with open(firestore_file, 'w', encoding='utf-8') as f:
        json.dump(flat_data, f, indent=2, ensure_ascii=False)
    print(f"✅ Saved: {firestore_file}")
    
    # 4. Raw Excel data
    raw_file = os.path.join(output_dir, f"{base_name}_raw.json")
    with open(raw_file, 'w', encoding='utf-8') as f:
        json.dump(all_data, f, indent=2, ensure_ascii=False)
    print(f"✅ Saved: {raw_file}")
    
    print(f"\n🎉 All files saved in: {output_dir}")

if __name__ == "__main__":
    print("🔄 ArhamApp Simple Data Converter")
    print("=" * 50)
    
    if len(sys.argv) < 2:
        print("Usage: python arham_converter.py <excel_file_path>")
        print("Example: python arham_converter.py data.xlsx")
        
        # Try to find Excel files in current directory
        excel_files = [f for f in os.listdir('.') if f.endswith(('.xlsx', '.xls'))]
        if excel_files:
            print(f"\nFound Excel files in current directory:")
            for i, file in enumerate(excel_files):
                print(f"  {i+1}. {file}")
            
            try:
                choice = input(f"\nEnter file number (1-{len(excel_files)}) or press Enter to exit: ").strip()
                if choice and choice.isdigit():
                    file_index = int(choice) - 1
                    if 0 <= file_index < len(excel_files):
                        file_path = excel_files[file_index]
                        print(f"Selected: {file_path}")
                        convert_excel_to_json(file_path)
                    else:
                        print("Invalid selection")
                else:
                    print("Exiting...")
            except KeyboardInterrupt:
                print("\nExiting...")
        
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        sys.exit(1)
    
    success = convert_excel_to_json(file_path)
    
    if success:
        print("\n🎉 Conversion completed successfully!")
        print("\n📁 Check the generated JSON files in the same directory as your Excel file.")
        print("\n📋 Next steps:")
        print("1. Review the generated files")
        print("2. Upload *_firestore.json to Firestore")
        print("3. Use *_navigation.json for app navigation")
    else:
        print("❌ Conversion failed!")
    
    input("\nPress Enter to exit...")
