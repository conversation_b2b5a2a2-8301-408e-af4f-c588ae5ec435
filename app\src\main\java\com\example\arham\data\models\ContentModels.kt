package com.example.arham.data.models

import kotlinx.coroutines.tasks.await

data class ContentItem(
    val id: String,
    val title: String,
    val author: String,
    val category: String, // "tulsi", "bhikshu", "kalu", etc.
    val content: String,
    val shortDescription: String = "",
    val totalPages: Int = 1
)

object ContentRepository {
    private val contentMap = mutableMapOf<String, ContentItem>()

    init {
        // Initialize with sample content for each category
        initializeTulsiContent()
        initializeBhikshuContent()
        initializeKaluContent()
        initializeMahaprajnaContent()
        initializeMangalContent()
    }

    suspend fun getContent(contentId: String): ContentItem? {
        // First try to get from cache
        contentMap[contentId]?.let { return it }

        // If not in cache, try to load from Firestore
        return try {
            val db = com.google.firebase.firestore.FirebaseFirestore.getInstance()
            val doc = db.collection("content")
                .document(contentId)
                .get()
                .await()

            if (doc.exists()) {
                val item = ContentItem(
                    id = contentId,
                    title = doc.getString("TitleName") ?: "Unknown Title",
                    author = doc.getString("AuthorName") ?: "Unknown Author",
                    category = doc.getString("CategoryName") ?: "general",
                    content = doc.getString("Content") ?: "Content not available",
                    shortDescription = doc.getString("Tags") ?: "",
                    totalPages = 1
                )
                // Cache it for future use
                contentMap[contentId] = item
                item
            } else {
                // Fallback to static content
                contentMap[contentId]
            }
        } catch (e: Exception) {
            // Fallback to static content
            contentMap[contentId]
        }
    }
    
    fun getContentByCategory(category: String): List<ContentItem> {
        return contentMap.values.filter { it.category == category }
    }
    
    private fun initializeTulsiContent() {
        val tulsiItems = listOf(
            ContentItem(
                id = "tulsi_1",
                title = "तुलसी माता की जय",
                author = "आचार्य तुलसी",
                category = "tulsi",
                content = """
                    तुलसी माता की जय हो। तुलसी माता की जय हो जय हो। तुलसी माता की जय हो जय हो जय हो।
                    
                    आचार्य तुलसी जी महाराज का जीवन एक प्रेरणादायक कहानी है। उन्होंने अपने जीवन में अनेक कार्य किए जो समाज के लिए अत्यंत उपयोगी थे।
                    
                    उनकी शिक्षाएं आज भी हमारे लिए मार्गदर्शन का काम करती हैं। वे एक महान संत थे जिन्होंने धर्म और अध्यात्म के क्षेत्र में अमूल्य योगदान दिया।
                    
                    तुलसी माता की जय हो। तुलसी माता की जय हो जय हो। तुलसी माता की जय हो जय हो जय हो।
                    
                    उनके द्वारा स्थापित संस्थाएं आज भी समाज सेवा में लगी हुई हैं। उनका जीवन दर्शन हमें सिखाता है कि कैसे हम अपने जीवन को सार्थक बना सकते हैं।
                    
                    आचार्य तुलसी जी के विचार आज भी प्रासंगिक हैं और हमें जीवन में सही राह दिखाते हैं।
                    
                    तुलसी माता की जय हो। तुलसी माता की जय हो जय हो। तुलसी माता की जय हो जय हो जय हो।
                """.trimIndent(),
                shortDescription = "तुलसी माता की महिमा और आचार्य तुलसी जी के जीवन दर्शन पर आधारित स्तुति।"
            ),
            ContentItem(
                id = "tulsi_2",
                title = "तुलसी माता की जय हो",
                author = "आचार्य तुलसी",
                category = "tulsi",
                content = """
                    तुलसी माता की जय हो जय हो। तुलसी माता की जय हो जय हो जय हो।
                    
                    आचार्य तुलसी जी महाराज के जीवन से हमें यह सीख मिलती है कि सच्चा धर्म सेवा में है। उन्होंने अपना पूरा जीवन मानवता की सेवा में समर्पित कर दिया।
                    
                    उनके द्वारा चलाए गए अनेक आंदोलन आज भी समाज में सकारात्मक बदलाव ला रहे हैं। शिक्षा, स्वास्थ्य और सामाजिक सुधार के क्षेत्र में उनका योगदान अतुलनीय है।
                    
                    तुलसी माता की जय हो। तुलसी माता की जय हो जय हो।
                """.trimIndent(),
                shortDescription = "आचार्य तुलसी जी के सामाजिक योगदान और उनके आदर्शों पर आधारित स्तुति।"
            )
        )
        
        tulsiItems.forEach { contentMap[it.id] = it }
    }
    
    private fun initializeBhikshuContent() {
        val bhikshuItems = listOf(
            ContentItem(
                id = "bhikshu_1",
                title = "भिक्षु स्तुति",
                author = "जैन आचार्य",
                category = "bhikshu",
                content = """
                    भिक्षु जीवन की महिमा अपार है। यह त्याग और तपस्या का मार्ग है।
                    
                    भिक्षु वह है जो सांसारिक मोह-माया को त्यागकर आत्मा की शुद्धता के लिए प्रयासरत रहता है। उनका जीवन सादगी और अहिंसा के सिद्धांतों पर आधारित होता है।
                    
                    भिक्षुओं का जीवन हमें सिखाता है कि सच्ची खुशी भौतिक वस्तुओं में नहीं बल्कि आत्मिक शांति में है। वे अपने आचरण से समाज को सही राह दिखाते हैं।
                    
                    भिक्षु जीवन की यह स्तुति हमें प्रेरणा देती है कि हम भी अपने जीवन में सत्य और अहिंसा के मार्ग पर चलें।
                """.trimIndent(),
                shortDescription = "भिक्षु जीवन की महिमा और उनके आदर्शों पर आधारित स्तुति।"
            )
        )
        
        bhikshuItems.forEach { contentMap[it.id] = it }
    }
    
    private fun initializeKaluContent() {
        val kaluItems = listOf(
            ContentItem(
                id = "kalu_1",
                title = "कालु स्तुति",
                author = "जैन आचार्य",
                category = "kalu",
                content = """
                    कालु की महिमा अपरंपार है। यह समय और काल के स्वामी की स्तुति है।
                    
                    काल का चक्र निरंतर चलता रहता है। इस संसार में सब कुछ काल के अधीन है। कालु स्तुति हमें समय की महत्ता और जीवन की नश्वरता का बोध कराती है।
                    
                    हमें अपने समय का सदुपयोग करना चाहिए और धर्म के मार्ग पर चलना चाहिए। काल किसी की प्रतीक्षा नहीं करता, इसलिए हमें अभी से ही सत्कर्म करने चाहिए।
                    
                    कालु स्तुति हमें यह संदेश देती है कि जीवन अनमोल है और इसका सदुपयोग करना हमारा कर्तव्य है।
                """.trimIndent(),
                shortDescription = "काल की महिमा और समय के महत्व पर आधारित स्तुति।"
            )
        )
        
        kaluItems.forEach { contentMap[it.id] = it }
    }
    
    private fun initializeMahaprajnaContent() {
        val mahaprajnaItems = listOf(
            ContentItem(
                id = "mahaprajna_1",
                title = "महाप्रज्ञ स्तुति",
                author = "आचार्य महाप्रज्ञ",
                category = "mahaprajna",
                content = """
                    महाप्रज्ञ आचार्य की महिमा अपार है। उनका ज्ञान और बुद्धिमत्ता अतुलनीय है।
                    
                    आचार्य महाप्रज्ञ जी ने अपने जीवन में अनेक ग्रंथों की रचना की और धर्म के प्रचार-प्रसार में अपना योगदान दिया। उनकी शिक्षाएं आज भी लाखों लोगों के लिए प्रेरणा का स्रोत हैं।
                    
                    उन्होंने जैन धर्म के सिद्धांतों को आधुनिक संदर्भ में प्रस्तुत किया और समाज में व्याप्त कुरीतियों के विरुद्ध आवाज उठाई। उनका जीवन सेवा और त्याग का उदाहरण है।
                    
                    महाप्रज्ञ आचार्य की यह स्तुति हमें उनके आदर्शों पर चलने की प्रेरणा देती है।
                """.trimIndent(),
                shortDescription = "आचार्य महाप्रज्ञ जी के जीवन और उनके योगदान पर आधारित स्तुति।"
            )
        )
        
        mahaprajnaItems.forEach { contentMap[it.id] = it }
    }
    
    private fun initializeMangalContent() {
        val mangalItems = listOf(
            ContentItem(
                id = "mangal_1",
                title = "मंगल स्तुति",
                author = "जैन आचार्य",
                category = "mangal",
                content = """
                    मंगल की स्तुति से जीवन में शुभता आती है। यह कल्याण और समृद्धि का मार्ग है।
                    
                    मंगल का अर्थ है शुभ, कल्याणकारी और हितकारी। मंगल स्तुति करने से मन में सकारात्मक भावनाएं जागती हैं और जीवन में खुशियां आती हैं।
                    
                    यह स्तुति हमें सिखाती है कि हमें हमेशा दूसरों के कल्याण की कामना करनी चाहिए। जब हम दूसरों की भलाई चाहते हैं तो हमारा भी कल्याण होता है।
                    
                    मंगल स्तुति से हमारे जीवन में शांति, प्रेम और सद्भावना का वास होता है।
                """.trimIndent(),
                shortDescription = "मंगल और कल्याण की भावना पर आधारित स्तुति।"
            )
        )
        
        mangalItems.forEach { contentMap[it.id] = it }
    }
    
    // Helper function to get content list for UI
    fun getTulsiContentList(): List<Pair<String, String>> {
        return getContentByCategory("tulsi").map { it.id to it.title }
    }
    
    fun getBhikshuContentList(): List<Pair<String, String>> {
        return getContentByCategory("bhikshu").map { it.id to it.title }
    }
    
    fun getKaluContentList(): List<Pair<String, String>> {
        return getContentByCategory("kalu").map { it.id to it.title }
    }
    
    fun getMahaprajnaContentList(): List<Pair<String, String>> {
        return getContentByCategory("mahaprajna").map { it.id to it.title }
    }
    
    fun getMangalContentList(): List<Pair<String, String>> {
        return getContentByCategory("mangal").map { it.id to it.title }
    }
}
