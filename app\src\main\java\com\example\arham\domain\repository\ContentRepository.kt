package com.example.arham.domain.repository

import com.example.arham.domain.models.SpiritualContent
import com.example.arham.domain.models.ContentCategory
import kotlinx.coroutines.flow.Flow

/**
 * Domain layer repository interface
 * Clean architecture - Repository pattern
 */
interface ContentRepository {
    
    // Content operations
    suspend fun getAllContent(): List<SpiritualContent>
    suspend fun getContentByCategory(category: ContentCategory): List<SpiritualContent>
    suspend fun getContentById(id: String): SpiritualContent?
    suspend fun searchContent(query: String): List<SpiritualContent>
    suspend fun getPopularContent(): List<SpiritualContent>
    
    // Reactive data streams
    fun getContentFlow(): Flow<List<SpiritualContent>>
    fun getContentByCategoryFlow(category: ContentCategory): Flow<List<SpiritualContent>>
    
    // Content management
    suspend fun addContent(content: SpiritualContent): Result<Unit>
    suspend fun updateContent(content: SpiritualContent): Result<Unit>
    suspend fun deleteContent(id: String): Result<Unit>
    
    // Analytics
    suspend fun incrementViewCount(contentId: String): Result<Unit>
    suspend fun getContentAnalytics(contentId: String): Map<String, Any>
}
