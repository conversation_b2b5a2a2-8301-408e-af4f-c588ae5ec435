<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArhamApp Admin - Database Management</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: #f8f9fa;
            color: #333;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            color: #333;
        }
        
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .nav-tab.active {
            background: #007bff;
            color: white;
        }
        
        .nav-tab:hover {
            background: #e9ecef;
        }
        
        .nav-tab.active:hover {
            background: #0056b3;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* Explore Screen Copy */
        .explore-screen {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .explore-title {
            font-size: 2em;
            margin-bottom: 30px;
            text-align: center;
            color: #333;
        }
        
        .mini-apps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
        }
        
        .mini-app-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .mini-app-card:hover {
            border-color: #007bff;
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .mini-app-card.active {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .mini-app-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        
        .mini-app-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .mini-app-desc {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 15px;
        }
        
        .mini-app-features {
            font-size: 0.8em;
            color: #888;
        }
        
        /* Data Entry Panel */
        .data-panel {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 20px;
        }
        
        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .panel-title {
            font-size: 1.8em;
            color: #333;
        }
        
        .stats-row {
            display: flex;
            gap: 20px;
        }
        
        .stat-box {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 6px;
            text-align: center;
            min-width: 80px;
        }
        
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            font-size: 0.8em;
            color: #666;
        }
        
        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
        }
        
        .form-panel {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .btn:disabled:hover {
            background: #6c757d;
            transform: none;
        }
        
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        
        .btn-warning { background: #ffc107; color: #333; }
        .btn-warning:hover { background: #e0a800; }
        
        .data-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            max-height: 500px;
            overflow-y: auto;
        }
        
        .data-item {
            background: white;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .item-info {
            flex: 1;
        }
        
        .item-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .item-meta {
            font-size: 0.8em;
            color: #666;
        }
        
        .item-actions {
            display: flex;
            gap: 5px;
        }
        
        .tree-view {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .tree-item {
            padding: 3px 0;
            cursor: pointer;
            border-radius: 3px;
            padding-left: 5px;
        }
        
        .tree-item:hover {
            background: #e9ecef;
        }
        
        .level-0 { font-weight: bold; color: #007bff; }
        .level-1 { margin-left: 20px; color: #28a745; }
        .level-2 { margin-left: 40px; color: #ffc107; }
        .level-3 { margin-left: 60px; color: #dc3545; }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 6px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.success { background: #28a745; }
        .notification.error { background: #dc3545; }
        .notification.info { background: #007bff; }
        
        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
            
            .mini-apps-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-tabs {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🕉️ ArhamApp Admin Panel</h1>
            <p>Database Management for Spiritual Reading Content</p>
        </div>
        
        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('explore')">
                📱 Explore Screen
            </button>
            <button class="nav-tab" onclick="showTab('firebase')">
                🔥 Firebase Setup
            </button>
            <button class="nav-tab" onclick="showTab('database')">
                🗄️ Database Management
            </button>
            <button class="nav-tab" onclick="showTab('export')">
                📤 Export/Import
            </button>
        </div>

        <!-- Explore Screen Tab -->
        <div id="explore" class="tab-content active">
            <div class="explore-screen">
                <h2 class="explore-title">📱 Explore Screen (App Copy)</h2>

                <div class="mini-apps-grid">
                    <!-- Disha Yantra -->
                    <div class="mini-app-card">
                        <div class="mini-app-icon">🧭</div>
                        <div class="mini-app-title">Disha Yantra</div>
                        <div class="mini-app-desc">Spiritual compass for direction finding</div>
                        <div class="mini-app-features">• Qibla direction<br>• Spiritual guidance</div>
                    </div>

                    <!-- Sadhana Tracker -->
                    <div class="mini-app-card">
                        <div class="mini-app-icon">📊</div>
                        <div class="mini-app-title">Sadhana Tracker</div>
                        <div class="mini-app-desc">Track your spiritual habits and progress</div>
                        <div class="mini-app-features">• Add Habit Screen<br>• Habit Detail Screen<br>• Habit Onboarding Screen</div>
                    </div>

                    <!-- Dainik Swadhyay -->
                    <div class="mini-app-card" onclick="openDainikSwadhyay()">
                        <div class="mini-app-icon">📖</div>
                        <div class="mini-app-title">दैनिक स्वाध्याय</div>
                        <div class="mini-app-desc">Daily spiritual reading content</div>
                        <div class="mini-app-features">• UniversalContentScreen<br>• E-Reader Screen<br>• Hierarchical Content</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Firebase Setup Tab -->
        <div id="firebase" class="tab-content">
            <div class="data-panel">
                <div class="panel-header">
                    <h2 class="panel-title">🔥 Firebase Configuration</h2>
                    <div class="stats-row">
                        <div class="stat-box" id="connectionStatus">
                            <div class="stat-number" id="statusIcon">🔴</div>
                            <div class="stat-label" id="statusText">Disconnected</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-number" id="syncStatus">❌</div>
                            <div class="stat-label">Sync Status</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-number" id="liveUsers">0</div>
                            <div class="stat-label">Live Users</div>
                        </div>
                    </div>
                </div>

                <div class="main-grid">
                    <!-- Firebase Config Panel -->
                    <div class="form-panel">
                        <h3>🔧 Firebase Configuration</h3>

                        <div class="form-group">
                            <label class="form-label">Firebase Project ID *</label>
                            <input type="text" class="form-input" id="firebaseProjectId" placeholder="your-project-id">
                            <small style="color: #666;">Find this in Firebase Console → Project Settings</small>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Firebase API Key *</label>
                            <input type="text" class="form-input" id="firebaseApiKey" placeholder="AIzaSyC...">
                            <small style="color: #666;">Find this in Firebase Console → Project Settings → Web API Key</small>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Collection Name</label>
                            <input type="text" class="form-input" id="collectionName" value="spiritual_reading" readonly>
                            <small style="color: #666;">This is your app's data collection</small>
                        </div>

                        <button class="btn btn-success" onclick="connectFirebase()">
                            🔗 Connect to Firebase
                        </button>
                        <button class="btn btn-info" onclick="testConnection()">
                            ✅ Test Connection
                        </button>
                        <button class="btn btn-warning" onclick="clearFirebaseConfig()">
                            🗑️ Clear Config
                        </button>

                        <!-- Database Management Buttons -->
                        <div style="margin-top: 15px; padding: 15px; background: #fff3cd; border-radius: 6px; border-left: 4px solid #ffc107;">
                            <strong>📊 Database Management:</strong><br>
                            <div style="margin-top: 10px;">
                                <button class="btn btn-success" onclick="downloadTemplate()">
                                    📥 Download Excel Template
                                </button>
                                <input type="file" id="excelFile" accept=".xlsx,.xls" style="display: none;" onchange="handleExcelUpload(event)">
                                <button class="btn btn-primary" onclick="document.getElementById('excelFile').click()">
                                    📤 Upload Excel Data
                                </button>
                                <button class="btn btn-danger" onclick="clearDatabase()" style="margin-left: 10px;">
                                    🗑️ Clear All Database
                                </button>
                            </div>
                        </div>

                        <div style="margin-top: 20px; padding: 15px; background: #e3f2fd; border-radius: 6px;">
                            <strong>📋 Setup Instructions:</strong><br>
                            1. Go to <a href="https://console.firebase.google.com" target="_blank">Firebase Console</a><br>
                            2. Select your ArhamApp project<br>
                            3. Go to Project Settings → General<br>
                            4. Copy Project ID and Web API Key<br>
                            5. Paste here and click Connect
                        </div>
                    </div>

                    <!-- Live Sync Panel -->
                    <div class="form-panel">
                        <h3>📱 Live App Sync</h3>

                        <div style="margin-bottom: 15px;">
                            <button class="btn btn-success" onclick="uploadAllData()" id="uploadBtn" disabled>
                                📱 Upload All Data to App
                            </button>
                            <button class="btn btn-info" onclick="syncWithApp()" id="syncBtn" disabled>
                                🔄 Sync with App
                            </button>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label>
                                <input type="checkbox" id="autoSync" onchange="toggleAutoSync()">
                                🔄 Auto-sync when adding data
                            </label>
                        </div>

                        <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-bottom: 15px;">
                            <strong>📊 Sync Statistics:</strong><br>
                            <div style="margin-top: 10px;">
                                • Total Items: <span id="totalSyncItems">0</span><br>
                                • Last Sync: <span id="lastSyncTime">Never</span><br>
                                • Sync Status: <span id="syncStatusText">Not connected</span>
                            </div>
                        </div>

                        <div id="livePreview" style="background: #f8f9fa; padding: 15px; border-radius: 6px; max-height: 300px; overflow-y: auto;">
                            <h4>📱 Live App Preview</h4>
                            <p style="text-align: center; color: #666; padding: 20px;">Connect to Firebase to see live app data</p>
                        </div>
                    </div>
                </div>

                <!-- Firebase Instructions -->
                <div class="data-panel" style="margin-top: 20px;">
                    <h3>📚 How to Get Firebase Credentials</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                            <h4>🔍 Step 1: Find Project ID</h4>
                            <ol>
                                <li>Go to <a href="https://console.firebase.google.com" target="_blank">Firebase Console</a></li>
                                <li>Select your ArhamApp project</li>
                                <li>Click ⚙️ Settings → Project Settings</li>
                                <li>Copy "Project ID" from General tab</li>
                            </ol>
                        </div>

                        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                            <h4>🔑 Step 2: Find API Key</h4>
                            <ol>
                                <li>In Project Settings → General tab</li>
                                <li>Scroll down to "Your apps" section</li>
                                <li>Find Web app configuration</li>
                                <li>Copy "apiKey" value</li>
                            </ol>
                        </div>

                        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                            <h4>🔒 Step 3: Setup Firestore</h4>
                            <ol>
                                <li>Go to Firestore Database</li>
                                <li>Create database in production mode</li>
                                <li>Set rules to allow read/write</li>
                                <li>Collection will be auto-created</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Management Tab -->
        <div id="database" class="tab-content">
            <div class="data-panel">
                <div class="panel-header">
                    <h2 class="panel-title">📖 दैनिक स्वाध्याय - Data Entry</h2>
                    <div class="stats-row">
                        <div class="stat-box">
                            <div class="stat-number" id="totalItems">0</div>
                            <div class="stat-label">Total</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-number" id="totalLists">0</div>
                            <div class="stat-label">Lists</div>
                        </div>
                        <div class="stat-box">
                            <div class="stat-number" id="totalContent">0</div>
                            <div class="stat-label">Content</div>
                        </div>
                    </div>
                </div>

                <div class="main-grid">
                    <!-- Form Panel -->
                    <div class="form-panel">
                        <h3>➕ Add New Item</h3>

                        <form id="contentForm">
                            <div class="form-group">
                                <label class="form-label">Title/Name *</label>
                                <input type="text" class="form-input" id="itemTitle" placeholder="जैसे: गीत/ढाल संग्रह, रु रु मैं संवारियो" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Parent Item</label>
                                <select class="form-select" id="parentSelect">
                                    <option value="">Root Level (No Parent)</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Type *</label>
                                <select class="form-select" id="itemType" required>
                                    <option value="list">📂 List (Has Children)</option>
                                    <option value="content">📄 Content (Final Item)</option>
                                </select>
                            </div>

                            <div class="form-group" id="contentGroup" style="display: none;">
                                <label class="form-label">Content Text</label>
                                <textarea class="form-textarea" id="contentText" placeholder="यहाँ मंत्र, श्लोक, भजन या content लिखें..."></textarea>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Author</label>
                                <input type="text" class="form-input" id="itemAuthor" placeholder="आचार्य तुलसी, महावीर स्वामी">
                            </div>

                            <div class="form-group">
                                <label class="form-label">Order</label>
                                <input type="number" class="form-input" id="itemOrder" value="1" min="1">
                            </div>

                            <button type="button" class="btn btn-success" onclick="addItem()">
                                ✅ Add Item
                            </button>
                            <button type="button" class="btn btn-info" onclick="addItemLive()" id="addLiveBtn" disabled>
                                📱 Add & Sync to App
                            </button>
                            <button type="button" class="btn" onclick="clearForm()">
                                🗑️ Clear
                            </button>
                        </form>
                    </div>

                    <!-- Data List Panel -->
                    <div class="form-panel">
                        <h3>📊 Current Data</h3>

                        <div class="form-group">
                            <input type="text" class="form-input" id="searchInput" placeholder="🔍 Search items..." onkeyup="searchItems()">
                        </div>

                        <div class="data-list" id="dataList">
                            <div style="text-align: center; padding: 40px; color: #666;">
                                No data yet. Add some items to get started!
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tree View -->
                <div class="data-panel" style="margin-top: 20px;">
                    <h3>🌳 Hierarchy Tree View</h3>
                    <div class="tree-view" id="treeView">
                        <div style="text-align: center; padding: 20px; color: #666;">
                            Tree structure will appear here as you add items
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Export/Import Tab -->
        <div id="export" class="tab-content">
            <div class="data-panel">
                <h2 class="panel-title">📤 Export/Import Data</h2>

                <div class="main-grid">
                    <div class="form-panel">
                        <h3>📱 Export for App</h3>
                        <p>Download data in spiritual_reading collection format for your ArhamApp.</p>

                        <button class="btn btn-success" onclick="exportForApp()">
                            📱 Export spiritual_reading Collection
                        </button>
                        <button class="btn btn-warning" onclick="exportBackup()">
                            💾 Create Backup
                        </button>

                        <div style="margin-top: 20px; padding: 15px; background: #e3f2fd; border-radius: 6px;">
                            <strong>📋 Export Instructions:</strong><br>
                            1. Click "Export spiritual_reading Collection"<br>
                            2. Upload JSON file to Firebase Firestore<br>
                            3. Collection name: <code>spiritual_reading</code><br>
                            4. Your app will automatically sync the data
                        </div>
                    </div>

                    <div class="form-panel">
                        <h3>📥 Import Data</h3>
                        <p>Import data from backup or existing files.</p>

                        <div class="form-group">
                            <label class="form-label">Select JSON File</label>
                            <input type="file" class="form-input" id="importFile" accept=".json">
                        </div>

                        <button class="btn btn-info" onclick="importData()">
                            📥 Import Data
                        </button>
                        <button class="btn btn-danger" onclick="clearAllData()">
                            🗑️ Clear All Data
                        </button>

                        <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 6px;">
                            <strong>⚠️ Import Notes:</strong><br>
                            • Importing will replace existing data<br>
                            • Create backup before importing<br>
                            • Supported formats: JSON backup files
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification"></div>

    <script>
        // Firebase will be loaded dynamically
        let firebaseLoaded = false;

        // Global data storage
        let spiritualData = [];
        let firebaseApp = null;
        let db = null;
        let isConnected = false;
        let autoSyncEnabled = false;
        let unsubscribe = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            updateStats();
            updateParentSelect();
            updateDataList();
            updateTreeView();
            loadFirebaseConfig();

            // Show/hide content field based on type
            document.getElementById('itemType').addEventListener('change', function() {
                const contentGroup = document.getElementById('contentGroup');
                if (this.value === 'content') {
                    contentGroup.style.display = 'block';
                } else {
                    contentGroup.style.display = 'none';
                }
            });
        });

        // Load Firebase dynamically
        async function loadFirebase() {
            if (firebaseLoaded) return;

            try {
                // Load Firebase modules dynamically
                const { initializeApp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js');
                const { getFirestore, collection, addDoc, getDocs, onSnapshot, query, orderBy, doc, setDoc, deleteDoc } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');

                // Make Firebase functions globally available
                window.initializeApp = initializeApp;
                window.getFirestore = getFirestore;
                window.collection = collection;
                window.addDoc = addDoc;
                window.getDocs = getDocs;
                window.onSnapshot = onSnapshot;
                window.query = query;
                window.orderBy = orderBy;
                window.doc = doc;
                window.setDoc = setDoc;
                window.deleteDoc = deleteDoc;

                firebaseLoaded = true;
                showNotification('🔥 Firebase loaded successfully!', 'info');
            } catch (error) {
                console.error('Firebase loading error:', error);
                showNotification('❌ Failed to load Firebase: ' + error.message, 'error');
            }
        }

        // Firebase disconnect function
        window.disconnectFirebase = function() {
            try {
                if (firebaseApp) {
                    // Reset connection state
                    isConnected = false;
                    firebaseApp = null;
                    db = null;

                    // Clear saved config
                    localStorage.removeItem('firebaseConfig');

                    // Update UI
                    document.getElementById('connectionStatus').textContent = 'Not connected';
                    document.getElementById('connectionStatus').className = 'status-disconnected';

                    showNotification('🔌 Disconnected from Firebase', 'info');
                }
            } catch (error) {
                console.error('Disconnect error:', error);
            }
        };

        // Firebase connection functions
        window.connectFirebase = async function() {
            // Load Firebase first
            await loadFirebase();
            if (!firebaseLoaded) return;
            const projectId = document.getElementById('firebaseProjectId').value.trim();
            const apiKey = document.getElementById('firebaseApiKey').value.trim();

            if (!projectId || !apiKey) {
                showNotification('Please enter Project ID and API Key!', 'error');
                return;
            }

            try {
                const firebaseConfig = {
                    apiKey: apiKey,
                    authDomain: `${projectId}.firebaseapp.com`,
                    projectId: projectId,
                    storageBucket: `${projectId}.appspot.com`,
                    messagingSenderId: "123456789",
                    appId: "1:123456789:web:abcdef123456"
                };

                // Check if Firebase app already exists
                try {
                    firebaseApp = window.getApp(); // Get existing app
                    console.log('Using existing Firebase app');
                } catch (e) {
                    // App doesn't exist, create new one
                    firebaseApp = window.initializeApp(firebaseConfig);
                    console.log('Created new Firebase app');
                }

                db = window.getFirestore(firebaseApp);

                // Save config
                localStorage.setItem('firebaseConfig', JSON.stringify({
                    projectId: projectId,
                    apiKey: apiKey
                }));

                // Test connection
                await testConnection();

                if (isConnected) {
                    setupLiveSync();
                    enableFirebaseButtons();
                    showNotification('🎉 Connected to Firebase successfully!', 'success');
                }

            } catch (error) {
                console.error('Firebase connection error:', error);

                // Handle specific "DEFAULT app already exists" error
                if (error.message.includes('already exists')) {
                    showNotification('⚠️ Firebase already connected! Click "Clear Config" first, then reconnect.', 'warning');
                } else {
                    showNotification('❌ Connection failed: ' + error.message, 'error');
                }
            }
        };

        window.testConnection = async function() {
            if (!db) {
                showNotification('Please connect to Firebase first!', 'error');
                return;
            }

            try {
                const collectionName = document.getElementById('collectionName').value || 'spiritual_reading';
                const testQuery = window.query(window.collection(db, collectionName));
                await window.getDocs(testQuery);

                isConnected = true;
                updateConnectionStatus(true);
                showNotification('✅ Connection test successful!', 'success');

            } catch (error) {
                isConnected = false;
                updateConnectionStatus(false);
                showNotification('❌ Connection test failed: ' + error.message, 'error');
            }
        };

        function loadFirebaseConfig() {
            const saved = localStorage.getItem('firebaseConfig');
            if (saved) {
                const config = JSON.parse(saved);
                document.getElementById('firebaseProjectId').value = config.projectId || '';
                document.getElementById('firebaseApiKey').value = config.apiKey || '';
            }

            // Load auto-sync setting
            const autoSyncSaved = localStorage.getItem('autoSyncEnabled');
            if (autoSyncSaved) {
                autoSyncEnabled = JSON.parse(autoSyncSaved);
                document.getElementById('autoSync').checked = autoSyncEnabled;
            }
        }

        function updateConnectionStatus(connected) {
            const statusIcon = document.getElementById('statusIcon');
            const statusText = document.getElementById('statusText');
            const connectionStatus = document.getElementById('connectionStatus');

            if (connected) {
                statusIcon.textContent = '🟢';
                statusText.textContent = 'Connected';
                connectionStatus.style.background = '#d4edda';
            } else {
                statusIcon.textContent = '🔴';
                statusText.textContent = 'Disconnected';
                connectionStatus.style.background = '#f8d7da';
            }
        }

        function enableFirebaseButtons() {
            document.getElementById('uploadBtn').disabled = false;
            document.getElementById('syncBtn').disabled = false;
            document.getElementById('addLiveBtn').disabled = false;
        }

        function setupLiveSync() {
            if (!db) return;

            const collectionName = document.getElementById('collectionName').value || 'spiritual_reading';
            const q = window.query(window.collection(db, collectionName), window.orderBy('level'), window.orderBy('order'));

            // Real-time listener
            unsubscribe = window.onSnapshot(q, (snapshot) => {
                updateLivePreview(snapshot);
                updateSyncStats();
                showNotification('📱 App data synced!', 'info');
            });
        }

        window.clearFirebaseConfig = function() {
            try {
                // Disconnect Firebase properly
                disconnectFirebase();

                // Clear form fields
                document.getElementById('firebaseProjectId').value = '';
                document.getElementById('firebaseApiKey').value = '';

                // Reset connection state
                isConnected = false;
                updateConnectionStatus(false);

                // Disable buttons
                document.getElementById('uploadBtn').disabled = true;
                document.getElementById('syncBtn').disabled = true;
                document.getElementById('addLiveBtn').disabled = true;

                showNotification('🗑️ Firebase config cleared! You can now connect fresh.', 'success');
            } catch (error) {
                console.error('Clear config error:', error);
                showNotification('⚠️ Config cleared with warnings: ' + error.message, 'warning');
            }
        };

        // Firebase sync functions
        window.uploadAllData = async function() {
            if (!isConnected) {
                showNotification('Please connect to Firebase first!', 'error');
                return;
            }

            if (spiritualData.length === 0) {
                showNotification('No data to upload!', 'error');
                return;
            }

            try {
                const collectionName = document.getElementById('collectionName').value || 'spiritual_reading';
                const batch = [];

                // Upload each item
                for (const item of spiritualData) {
                    const docRef = window.doc(db, collectionName, item.id);
                    batch.push(window.setDoc(docRef, item));
                }

                await Promise.all(batch);

                updateSyncStats();
                showNotification(`🎉 Uploaded ${spiritualData.length} items to app successfully!`, 'success');

            } catch (error) {
                console.error('Upload error:', error);
                showNotification('❌ Upload failed: ' + error.message, 'error');
            }
        };

        window.syncWithApp = async function() {
            if (!isConnected) {
                showNotification('Please connect to Firebase first!', 'error');
                return;
            }

            try {
                const collectionName = document.getElementById('collectionName').value || 'spiritual_reading';
                const snapshot = await window.getDocs(window.query(window.collection(db, collectionName)));

                updateLivePreview(snapshot);
                updateSyncStats();
                showNotification('🔄 Synced with app successfully!', 'success');

            } catch (error) {
                console.error('Sync error:', error);
                showNotification('❌ Sync failed: ' + error.message, 'error');
            }
        };

        window.addItemLive = async function() {
            if (!isConnected) {
                showNotification('Please connect to Firebase first!', 'error');
                return;
            }

            // First add to local data
            addItem();

            // Then sync to Firebase if auto-sync is enabled
            if (autoSyncEnabled && spiritualData.length > 0) {
                const latestItem = spiritualData[spiritualData.length - 1];

                try {
                    const collectionName = document.getElementById('collectionName').value || 'spiritual_reading';
                    const docRef = window.doc(db, collectionName, latestItem.id);
                    await window.setDoc(docRef, latestItem);

                    showNotification('📱 Item added and synced to app!', 'success');
                    updateSyncStats();

                } catch (error) {
                    console.error('Live sync error:', error);
                    showNotification('⚠️ Item added locally but sync failed: ' + error.message, 'error');
                }
            }
        };

        window.toggleAutoSync = function() {
            autoSyncEnabled = document.getElementById('autoSync').checked;
            localStorage.setItem('autoSyncEnabled', autoSyncEnabled);

            if (autoSyncEnabled) {
                showNotification('🔄 Auto-sync enabled!', 'info');
            } else {
                showNotification('⏸️ Auto-sync disabled!', 'info');
            }
        };

        function updateLivePreview(snapshot) {
            const container = document.getElementById('livePreview');

            if (!snapshot || snapshot.empty) {
                container.innerHTML = '<h4>📱 Live App Preview</h4><p style="text-align: center; color: #666; padding: 20px;">No data in app yet</p>';
                return;
            }

            let html = '<h4>📱 Live App Preview (Real-time)</h4>';

            const docs = snapshot.docs.slice(0, 5); // Show first 5 items
            docs.forEach(doc => {
                const data = doc.data();
                const indent = '  '.repeat(data.level || 0);
                const icon = data.contentType === 'list' ? '📂' : '📄';

                html += `
                    <div style="padding: 8px; margin: 5px 0; background: white; border-radius: 4px; border-left: 3px solid #007bff;">
                        <div style="font-weight: bold;">${indent}${icon} ${data.title}</div>
                        <div style="font-size: 0.8em; color: #666;">Level: ${data.level} | Type: ${data.contentType}</div>
                    </div>
                `;
            });

            if (snapshot.docs.length > 5) {
                html += `<p style="text-align: center; color: #666; margin-top: 10px;">और ${snapshot.docs.length - 5} items...</p>`;
            }

            container.innerHTML = html;
        }

        function updateSyncStats() {
            document.getElementById('totalSyncItems').textContent = spiritualData.length;
            document.getElementById('lastSyncTime').textContent = new Date().toLocaleString('hi-IN');
            document.getElementById('syncStatusText').textContent = isConnected ? 'Connected & Synced' : 'Not connected';
            document.getElementById('syncStatus').textContent = isConnected ? '✅' : '❌';
            document.getElementById('liveUsers').textContent = Math.floor(Math.random() * 20) + 5; // Simulated
        }

        // Helper function to sync single item
        async function syncItemToFirebase(item) {
            try {
                const collectionName = document.getElementById('collectionName').value || 'spiritual_reading';
                const docRef = window.doc(db, collectionName, item.id);
                await window.setDoc(docRef, item);
                showNotification('📱 Auto-synced to app!', 'info');
            } catch (error) {
                console.error('Auto-sync error:', error);
                showNotification('⚠️ Auto-sync failed: ' + error.message, 'error');
            }
        }

        // Tab management
        function showTab(tabName) {
            // Hide all tabs
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));

            // Remove active class from all nav tabs
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => tab.classList.remove('active'));

            // Show selected tab
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked nav tab
            event.target.classList.add('active');
        }

        // Open Dainik Swadhyay from Explore Screen
        function openDainikSwadhyay() {
            // Highlight the card
            const cards = document.querySelectorAll('.mini-app-card');
            cards.forEach(card => card.classList.remove('active'));
            event.target.closest('.mini-app-card').classList.add('active');

            // Switch to database tab
            showTab('database');

            // Update nav tab active state
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => tab.classList.remove('active'));
            navTabs[1].classList.add('active'); // Database tab

            showNotification('📖 दैनिक स्वाध्याय data entry opened!', 'info');
        }

        // Load data from localStorage
        function loadData() {
            const saved = localStorage.getItem('arhamSpiritualReading');
            if (saved) {
                spiritualData = JSON.parse(saved);
            } else {
                // Initialize with root item
                spiritualData = [{
                    id: 'readcard_root',
                    title: 'दैनिक स्वाध्याय',
                    level: 0,
                    parentId: null,
                    hasChildren: false,
                    childrenCount: 0,
                    contentType: 'list',
                    order: 1,
                    content: null,
                    metadata: {
                        description: 'Daily spiritual reading content',
                        category: 'DAINIK_SWADHYAY',
                        isActive: true,
                        createdAt: Date.now(),
                        updatedAt: Date.now()
                    }
                }];
                saveData();
            }
        }

        // Save data to localStorage
        function saveData() {
            localStorage.setItem('arhamSpiritualReading', JSON.stringify(spiritualData));
            updateStats();
            updateParentSelect();
            updateDataList();
            updateTreeView();
        }

        // Add new item
        function addItem() {
            const title = document.getElementById('itemTitle').value.trim();
            const parentId = document.getElementById('parentSelect').value || null;
            const type = document.getElementById('itemType').value;
            const content = document.getElementById('contentText').value.trim();
            const author = document.getElementById('itemAuthor').value.trim();
            const order = parseInt(document.getElementById('itemOrder').value) || 1;

            if (!title || !type) {
                showNotification('Please fill in title and type!', 'error');
                return;
            }

            if (type === 'content' && !content) {
                showNotification('Please add content text for content items!', 'error');
                return;
            }

            // Calculate level
            let level = 0;
            if (parentId) {
                const parent = spiritualData.find(item => item.id === parentId);
                if (parent) {
                    level = parent.level + 1;
                }
            }

            const newItem = {
                id: 'item_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                title: title,
                level: level,
                parentId: parentId,
                hasChildren: type === 'list',
                childrenCount: 0,
                contentType: type,
                order: order,
                content: type === 'content' ? content : null,
                metadata: {
                    author: author || null,
                    language: 'hindi',
                    category: 'DAINIK_SWADHYAY',
                    isActive: true,
                    createdAt: Date.now(),
                    updatedAt: Date.now()
                }
            };

            spiritualData.push(newItem);

            // Update parent's children count and hasChildren
            if (parentId) {
                const parent = spiritualData.find(item => item.id === parentId);
                if (parent) {
                    parent.childrenCount = spiritualData.filter(item => item.parentId === parentId).length;
                    parent.hasChildren = parent.childrenCount > 0;
                }
            }

            saveData();
            clearForm();
            showNotification('✅ Item added successfully!', 'success');

            // Auto-sync to Firebase if enabled
            if (autoSyncEnabled && isConnected) {
                syncItemToFirebase(newItem);
            }
        }

        // Clear form
        function clearForm() {
            document.getElementById('contentForm').reset();
            document.getElementById('contentGroup').style.display = 'none';
            document.getElementById('itemTitle').focus();
        }

        // Update statistics
        function updateStats() {
            const totalItems = spiritualData.length;
            const totalLists = spiritualData.filter(item => item.contentType === 'list').length;
            const totalContent = spiritualData.filter(item => item.contentType === 'content').length;

            document.getElementById('totalItems').textContent = totalItems;
            document.getElementById('totalLists').textContent = totalLists;
            document.getElementById('totalContent').textContent = totalContent;
        }

        // Update parent select dropdown
        function updateParentSelect() {
            const select = document.getElementById('parentSelect');
            select.innerHTML = '<option value="">Root Level (No Parent)</option>';

            const listItems = spiritualData
                .filter(item => item.contentType === 'list')
                .sort((a, b) => a.level - b.level || a.order - b.order);

            listItems.forEach(item => {
                const indent = '  '.repeat(item.level);
                const option = document.createElement('option');
                option.value = item.id;
                option.textContent = `${indent}${item.title}`;
                select.appendChild(option);
            });
        }

        // Update data list
        function updateDataList() {
            const container = document.getElementById('dataList');

            if (spiritualData.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;">No data yet. Add some items to get started!</div>';
                return;
            }

            const sortedData = [...spiritualData].sort((a, b) => {
                if (a.level !== b.level) return a.level - b.level;
                return a.order - b.order;
            });

            let html = '';
            sortedData.forEach(item => {
                const indent = '  '.repeat(item.level);
                const typeIcon = item.contentType === 'list' ? '📂' : '📄';
                const contentPreview = item.content ? item.content.substring(0, 50) + '...' : '';

                html += `
                    <div class="data-item">
                        <div class="item-info">
                            <div class="item-title">${indent}${typeIcon} ${item.title}</div>
                            <div class="item-meta">
                                Level: ${item.level} | Type: ${item.contentType} |
                                ${item.metadata.author ? 'Author: ' + item.metadata.author : 'No author'}
                                ${contentPreview ? '<br>' + contentPreview : ''}
                            </div>
                        </div>
                        <div class="item-actions">
                            <button class="btn" onclick="editItem('${item.id}')" style="padding: 5px 10px; font-size: 12px;">✏️</button>
                            <button class="btn btn-danger" onclick="deleteItem('${item.id}')" style="padding: 5px 10px; font-size: 12px;">🗑️</button>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // Update tree view
        function updateTreeView() {
            const container = document.getElementById('treeView');

            if (spiritualData.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">Tree structure will appear here as you add items</div>';
                return;
            }

            const tree = buildTree();
            let html = '';

            function renderTree(items, level = 0) {
                items.forEach(item => {
                    const indent = '  '.repeat(level);
                    const icon = item.contentType === 'list' ? '📂' : '📄';
                    const children = item.children || [];

                    html += `<div class="tree-item level-${level}" onclick="selectTreeItem('${item.id}')">${indent}${icon} ${item.title}</div>`;

                    if (children.length > 0) {
                        renderTree(children, level + 1);
                    }
                });
            }

            renderTree(tree);
            container.innerHTML = html;
        }

        // Build tree structure
        function buildTree() {
            const itemMap = {};
            const tree = [];

            spiritualData.forEach(item => {
                itemMap[item.id] = { ...item, children: [] };
            });

            spiritualData.forEach(item => {
                if (item.parentId && itemMap[item.parentId]) {
                    itemMap[item.parentId].children.push(itemMap[item.id]);
                } else {
                    tree.push(itemMap[item.id]);
                }
            });

            function sortLevel(items) {
                items.sort((a, b) => a.order - b.order);
                items.forEach(item => {
                    if (item.children.length > 0) {
                        sortLevel(item.children);
                    }
                });
            }

            sortLevel(tree);
            return tree;
        }

        // Select tree item for editing
        function selectTreeItem(itemId) {
            const item = spiritualData.find(i => i.id === itemId);
            if (item) {
                document.getElementById('itemTitle').value = item.title;
                document.getElementById('parentSelect').value = item.parentId || '';
                document.getElementById('itemType').value = item.contentType;
                document.getElementById('contentText').value = item.content || '';
                document.getElementById('itemAuthor').value = item.metadata.author || '';
                document.getElementById('itemOrder').value = item.order;

                const contentGroup = document.getElementById('contentGroup');
                if (item.contentType === 'content') {
                    contentGroup.style.display = 'block';
                } else {
                    contentGroup.style.display = 'none';
                }

                showNotification('Item loaded for editing!', 'info');
            }
        }

        // Edit item
        function editItem(itemId) {
            selectTreeItem(itemId);
        }

        // Delete item
        function deleteItem(itemId) {
            const item = spiritualData.find(i => i.id === itemId);
            if (!item) return;

            if (confirm(`Delete "${item.title}"?\n\nThis will also delete all its children.`)) {
                function findAllChildren(parentId) {
                    const children = spiritualData.filter(i => i.parentId === parentId);
                    let allChildren = [...children];

                    children.forEach(child => {
                        allChildren = allChildren.concat(findAllChildren(child.id));
                    });

                    return allChildren;
                }

                const toDelete = [item, ...findAllChildren(itemId)];
                const deleteIds = toDelete.map(i => i.id);

                spiritualData = spiritualData.filter(i => !deleteIds.includes(i.id));

                if (item.parentId) {
                    const parent = spiritualData.find(i => i.id === item.parentId);
                    if (parent) {
                        parent.childrenCount = spiritualData.filter(i => i.parentId === item.parentId).length;
                        parent.hasChildren = parent.childrenCount > 0;
                    }
                }

                saveData();
                showNotification(`Deleted "${item.title}" and ${toDelete.length - 1} children`, 'success');
            }
        }

        // Search items
        function searchItems() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const items = document.querySelectorAll('.data-item');

            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // Export for app
        function exportForApp() {
            if (spiritualData.length === 0) {
                showNotification('No data to export!', 'error');
                return;
            }

            const exportData = {
                spiritual_reading: spiritualData.map(item => ({
                    id: item.id,
                    title: item.title,
                    level: item.level,
                    parentId: item.parentId,
                    hasChildren: item.hasChildren,
                    childrenCount: item.childrenCount,
                    contentType: item.contentType,
                    order: item.order,
                    content: item.content,
                    metadata: item.metadata
                })),
                metadata: {
                    version: '1.0',
                    totalItems: spiritualData.length,
                    exportedAt: Date.now(),
                    appName: 'ArhamApp',
                    collection: 'spiritual_reading'
                }
            };

            downloadJSON(exportData, `arham_spiritual_reading_${new Date().toISOString().split('T')[0]}.json`);
            showNotification('📱 Data exported for app successfully!', 'success');
        }

        // Export backup
        function exportBackup() {
            if (spiritualData.length === 0) {
                showNotification('No data to backup!', 'error');
                return;
            }

            downloadJSON(spiritualData, `arham_backup_${new Date().toISOString().split('T')[0]}.json`);
            showNotification('💾 Backup created successfully!', 'success');
        }

        // Import data
        function importData() {
            const fileInput = document.getElementById('importFile');
            const file = fileInput.files[0];

            if (!file) {
                showNotification('Please select a JSON file!', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const importedData = JSON.parse(e.target.result);

                    if (Array.isArray(importedData)) {
                        spiritualData = importedData;
                    } else if (importedData.spiritual_reading) {
                        spiritualData = importedData.spiritual_reading;
                    } else {
                        throw new Error('Invalid file format');
                    }

                    saveData();
                    showNotification('📥 Data imported successfully!', 'success');
                    fileInput.value = '';
                } catch (error) {
                    showNotification('❌ Invalid JSON file!', 'error');
                }
            };

            reader.readAsText(file);
        }

        // Clear all data
        function clearAllData() {
            if (confirm('Are you sure you want to delete ALL data?\n\nThis cannot be undone!')) {
                spiritualData = [];
                saveData();
                clearForm();
                showNotification('🗑️ All data cleared!', 'success');
            }
        }

        // Download JSON file
        function downloadJSON(data, filename) {
            const blob = new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // Database Management Functions
        window.downloadTemplate = function() {
            // Create Excel template with proper structure
            const templateData = [
                {
                    'Document ID': 'readcard_root',
                    'Parent ID': null,
                    'Level': 0,
                    'Title': 'दैनिक स्वाध्याय',
                    'Content Type': 'list',
                    'Content': '',
                    'Has Children': true
                },
                {
                    'Document ID': 'item_example_1',
                    'Parent ID': 'readcard_root',
                    'Level': 1,
                    'Title': 'गीत/ढाल संग्रह',
                    'Content Type': 'list',
                    'Content': '',
                    'Has Children': true
                },
                {
                    'Document ID': 'item_example_2',
                    'Parent ID': 'item_example_1',
                    'Level': 2,
                    'Title': 'भिक्षु-स्तुति',
                    'Content Type': 'list',
                    'Content': '',
                    'Has Children': true
                },
                {
                    'Document ID': 'content_example_1',
                    'Parent ID': 'item_example_2',
                    'Level': 3,
                    'Title': 'भिक्षु म्हारै प्रगट्या जी',
                    'Content Type': 'content',
                    'Content': 'Your spiritual content in Hindi goes here...',
                    'Has Children': false
                }
            ];

            // Convert to CSV format
            const headers = Object.keys(templateData[0]);
            const csvContent = [
                headers.join(','),
                ...templateData.map(row =>
                    headers.map(header => {
                        const value = row[header];
                        return value === null ? '' : `"${String(value).replace(/"/g, '""')}"`;
                    }).join(',')
                )
            ].join('\n');

            // Download as CSV file
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = 'arham_data_template.csv';
            link.click();

            showNotification('📥 Excel template downloaded! Fill it with your data and upload back.', 'success');
        };

        window.handleExcelUpload = async function(event) {
            if (!isConnected) {
                showNotification('Please connect to Firebase first!', 'error');
                return;
            }

            const file = event.target.files[0];
            if (!file) return;

            try {
                showNotification('📤 Processing Excel file...', 'info');

                const text = await file.text();
                const lines = text.split('\n');
                const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());

                const collectionName = document.getElementById('collectionName').value || 'spiritual_reading';
                let uploadCount = 0;

                for (let i = 1; i < lines.length; i++) {
                    const line = lines[i].trim();
                    if (!line) continue;

                    const values = line.split(',').map(v => v.replace(/"/g, '').trim());
                    const docData = {};

                    headers.forEach((header, index) => {
                        const value = values[index];
                        switch (header) {
                            case 'Parent ID':
                                docData.parentId = value === '' ? null : value;
                                break;
                            case 'Level':
                                docData.level = value === '' ? 0 : parseInt(value);
                                break;
                            case 'Title':
                                docData.title = value;
                                break;
                            case 'Content Type':
                                docData.contentType = value || 'list';
                                break;
                            case 'Content':
                                docData.content = value;
                                break;
                            case 'Has Children':
                                docData.hasChildren = value.toLowerCase() === 'true';
                                break;
                        }
                    });

                    // Add timestamp
                    docData.createdAt = new Date();
                    docData.updatedAt = new Date();

                    // Upload to Firebase
                    const docId = values[0]; // Document ID is first column
                    if (docId) {
                        await db.collection(collectionName).doc(docId).set(docData);
                        uploadCount++;
                    }
                }

                showNotification(`✅ Successfully uploaded ${uploadCount} items to Firebase!`, 'success');

                // Refresh the live preview
                if (window.syncWithApp) {
                    await window.syncWithApp();
                }

            } catch (error) {
                console.error('Excel upload error:', error);
                showNotification('❌ Error uploading Excel file: ' + error.message, 'error');
            }

            // Clear the file input
            event.target.value = '';
        };

        window.clearDatabase = async function() {
            if (!isConnected) {
                showNotification('Please connect to Firebase first!', 'error');
                return;
            }

            const confirmed = confirm('⚠️ WARNING: This will delete ALL data in the spiritual_reading collection!\n\nThis action cannot be undone. Are you sure?');
            if (!confirmed) return;

            const doubleConfirm = confirm('🚨 FINAL WARNING: You are about to delete ALL your spiritual content data!\n\nType "DELETE" in the next prompt to confirm.');
            if (!doubleConfirm) return;

            const typeConfirm = prompt('Type "DELETE" to confirm deletion:');
            if (typeConfirm !== 'DELETE') {
                showNotification('❌ Deletion cancelled - incorrect confirmation text.', 'info');
                return;
            }

            try {
                showNotification('🗑️ Clearing database...', 'info');

                const collectionName = document.getElementById('collectionName').value || 'spiritual_reading';
                const snapshot = await db.collection(collectionName).get();

                const batch = db.batch();
                snapshot.docs.forEach(doc => {
                    batch.delete(doc.ref);
                });

                await batch.commit();

                showNotification(`✅ Successfully cleared ${snapshot.size} documents from database!`, 'success');

                // Clear local data and refresh UI
                spiritualData = [];
                renderDataList();

                // Refresh the live preview
                if (window.syncWithApp) {
                    await window.syncWithApp();
                }

            } catch (error) {
                console.error('Database clear error:', error);
                showNotification('❌ Error clearing database: ' + error.message, 'error');
            }
        };

        // Database Management Functions
        window.downloadTemplate = function() {
            // Create Excel template with proper structure
            const templateData = [
                {
                    'Document ID': 'readcard_root',
                    'Parent ID': '',
                    'Level': 0,
                    'Title': 'दैनिक स्वाध्याय',
                    'Content Type': 'list',
                    'Content': '',
                    'Has Children': 'true'
                },
                {
                    'Document ID': 'item_example_1',
                    'Parent ID': 'readcard_root',
                    'Level': 1,
                    'Title': 'गीत/ढाल संग्रह',
                    'Content Type': 'list',
                    'Content': '',
                    'Has Children': 'true'
                },
                {
                    'Document ID': 'item_example_2',
                    'Parent ID': 'item_example_1',
                    'Level': 2,
                    'Title': 'भिक्षु-स्तुति',
                    'Content Type': 'list',
                    'Content': '',
                    'Has Children': 'true'
                },
                {
                    'Document ID': 'content_example_1',
                    'Parent ID': 'item_example_2',
                    'Level': 3,
                    'Title': 'भिक्षु म्हारै प्रगट्या जी',
                    'Content Type': 'content',
                    'Content': 'Your spiritual content in Hindi goes here...',
                    'Has Children': 'false'
                }
            ];

            // Convert to CSV format
            const headers = Object.keys(templateData[0]);
            const csvContent = [
                headers.join(','),
                ...templateData.map(row =>
                    headers.map(header => {
                        const value = row[header];
                        return value === '' ? '' : `"${String(value).replace(/"/g, '""')}"`;
                    }).join(',')
                )
            ].join('\n');

            // Download as CSV file
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = 'arham_data_template.csv';
            link.click();

            showNotification('📥 Excel template downloaded! Fill it with your data and upload back.', 'success');
        };

        window.handleExcelUpload = async function(event) {
            if (!isConnected) {
                showNotification('Please connect to Firebase first!', 'error');
                return;
            }

            const file = event.target.files[0];
            if (!file) return;

            try {
                showNotification('📤 Processing Excel file...', 'info');

                const text = await file.text();
                const lines = text.split('\n');
                const headers = lines[0].split(',').map(h => h.replace(/"/g, '').trim());

                const collectionName = document.getElementById('collectionName').value || 'spiritual_reading';
                let uploadCount = 0;

                for (let i = 1; i < lines.length; i++) {
                    const line = lines[i].trim();
                    if (!line) continue;

                    const values = line.split(',').map(v => v.replace(/"/g, '').trim());
                    const docData = {};

                    headers.forEach((header, index) => {
                        const value = values[index];
                        switch (header) {
                            case 'Parent ID':
                                docData.parentId = value === '' ? null : value;
                                break;
                            case 'Level':
                                docData.level = value === '' ? 0 : parseInt(value);
                                break;
                            case 'Title':
                                docData.title = value;
                                break;
                            case 'Content Type':
                                docData.contentType = value || 'list';
                                break;
                            case 'Content':
                                docData.content = value;
                                break;
                            case 'Has Children':
                                docData.hasChildren = value.toLowerCase() === 'true';
                                break;
                        }
                    });

                    // Add timestamp
                    docData.createdAt = new Date();
                    docData.updatedAt = new Date();

                    // Upload to Firebase
                    const docId = values[0]; // Document ID is first column
                    if (docId) {
                        await db.collection(collectionName).doc(docId).set(docData);
                        uploadCount++;
                    }
                }

                showNotification(`✅ Successfully uploaded ${uploadCount} items to Firebase!`, 'success');

                // Refresh the live preview
                if (window.syncWithApp) {
                    await window.syncWithApp();
                }

            } catch (error) {
                console.error('Excel upload error:', error);
                showNotification('❌ Error uploading Excel file: ' + error.message, 'error');
            }

            // Clear the file input
            event.target.value = '';
        };

        window.clearDatabase = async function() {
            if (!isConnected) {
                showNotification('Please connect to Firebase first!', 'error');
                return;
            }

            const confirmed = confirm('⚠️ WARNING: This will delete ALL data in the spiritual_reading collection!\n\nThis action cannot be undone. Are you sure?');
            if (!confirmed) return;

            const doubleConfirm = confirm('🚨 FINAL WARNING: You are about to delete ALL your spiritual content data!\n\nType "DELETE" in the next prompt to confirm.');
            if (!doubleConfirm) return;

            const typeConfirm = prompt('Type "DELETE" to confirm deletion:');
            if (typeConfirm !== 'DELETE') {
                showNotification('❌ Deletion cancelled - incorrect confirmation text.', 'info');
                return;
            }

            try {
                showNotification('🗑️ Clearing database...', 'info');

                const collectionName = document.getElementById('collectionName').value || 'spiritual_reading';
                const snapshot = await db.collection(collectionName).get();

                const batch = db.batch();
                snapshot.docs.forEach(doc => {
                    batch.delete(doc.ref);
                });

                await batch.commit();

                showNotification(`✅ Successfully cleared ${snapshot.size} documents from database!`, 'success');

                // Clear local data and refresh UI
                spiritualData = [];
                renderDataList();

                // Refresh the live preview
                if (window.syncWithApp) {
                    await window.syncWithApp();
                }

            } catch (error) {
                console.error('Database clear error:', error);
                showNotification('❌ Error clearing database: ' + error.message, 'error');
            }
        };

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                addItem();
            }
            if (e.ctrlKey && e.key === 'e') {
                e.preventDefault();
                exportForApp();
            }
        });
    </script>
</body>
</html>
