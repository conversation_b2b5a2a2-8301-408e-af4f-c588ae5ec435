/**
 * Firestore Data Upload Script for ArhamApp
 * Uploads JSON data to Firebase Firestore
 */

const admin = require('firebase-admin');
const fs = require('fs');

// Initialize Firebase Admin (you'll need to add your service account key)
const serviceAccount = require('./firebase-service-account-key.json'); // You need to download this

admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: "https://your-project-id.firebaseio.com" // Replace with your project URL
});

const db = admin.firestore();

async function uploadToFirestore(jsonFilePath, collectionName, batchSize = 500) {
    try {
        console.log('🔄 Reading JSON file...');
        
        // Read JSON data
        const jsonData = JSON.parse(fs.readFileSync(jsonFilePath, 'utf8'));
        console.log(`✅ Found ${jsonData.length} documents to upload`);
        
        // Upload in batches
        const batches = [];
        for (let i = 0; i < jsonData.length; i += batchSize) {
            const batch = jsonData.slice(i, i + batchSize);
            batches.push(batch);
        }
        
        console.log(`📦 Uploading in ${batches.length} batches...`);
        
        let totalUploaded = 0;
        
        for (let i = 0; i < batches.length; i++) {
            const batch = batches[i];
            const firestoreBatch = db.batch();
            
            batch.forEach(doc => {
                const docRef = db.collection(collectionName).doc(doc.id);
                firestoreBatch.set(docRef, doc);
            });
            
            await firestoreBatch.commit();
            totalUploaded += batch.length;
            
            console.log(`✅ Batch ${i + 1}/${batches.length} uploaded (${totalUploaded}/${jsonData.length})`);
        }
        
        console.log(`🎉 Successfully uploaded ${totalUploaded} documents to ${collectionName}`);
        
    } catch (error) {
        console.error('❌ Upload failed:', error.message);
    }
}

async function uploadSpiritualContent(jsonFilePath) {
    await uploadToFirestore(jsonFilePath, 'spiritual_content');
}

async function uploadHabits(jsonFilePath) {
    await uploadToFirestore(jsonFilePath, 'habits');
}

async function uploadCustomCollection(jsonFilePath, collectionName) {
    await uploadToFirestore(jsonFilePath, collectionName);
}

// Command line usage
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length < 2) {
        console.log('Usage: node upload_to_firestore.js <json_file> <collection_name>');
        console.log('Example: node upload_to_firestore.js spiritual_content.json spiritual_content');
        process.exit(1);
    }
    
    const jsonFilePath = args[0];
    const collectionName = args[1];
    
    if (!fs.existsSync(jsonFilePath)) {
        console.error(`❌ File not found: ${jsonFilePath}`);
        process.exit(1);
    }
    
    console.log('🔥 Firestore Upload Script for ArhamApp');
    console.log('='.repeat(50));
    
    uploadCustomCollection(jsonFilePath, collectionName)
        .then(() => {
            console.log('✅ Upload completed successfully!');
            process.exit(0);
        })
        .catch(error => {
            console.error('❌ Upload failed:', error);
            process.exit(1);
        });
}

module.exports = {
    uploadToFirestore,
    uploadSpiritualContent,
    uploadHabits,
    uploadCustomCollection
};
