package com.example.arham.ui.screens

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import android.os.VibrationEffect
import android.os.Vibrator
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Text
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat.getSystemService
import kotlin.math.abs
import coil.compose.AsyncImage
import com.example.arham.R
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.ui.text.font.FontWeight

@Composable
fun DishaYantraScreen(isDarkMode: Boolean, onToggleTheme: () -> Unit) {
    val context = LocalContext.current
    val sensorManager = remember { getSystemService(context, SensorManager::class.java) }
    val vibrator = remember { getSystemService(context, Vibrator::class.java) }

    val accelerometer = sensorManager?.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)
    val magnetometer = sensorManager?.getDefaultSensor(Sensor.TYPE_MAGNETIC_FIELD)

    var azimuth by remember { mutableStateOf(0f) }
    var isAligned by remember { mutableStateOf(false) }

    val ishaanDirection = 45f  // Degrees
    val marginOfError = 5f     // Acceptable deviation

    val sensorEventListener = remember { object : SensorEventListener {
        val gravity = FloatArray(3)
        val geomagnetic = FloatArray(3)

        override fun onSensorChanged(event: SensorEvent?) {
            if (event == null) return
            if (event.sensor.type == Sensor.TYPE_ACCELEROMETER) {
                System.arraycopy(event.values, 0, gravity, 0, gravity.size)
            }
            if (event.sensor.type == Sensor.TYPE_MAGNETIC_FIELD) {
                System.arraycopy(event.values, 0, geomagnetic, 0, geomagnetic.size)
            }

            val R = FloatArray(9)
            val I = FloatArray(9)
            if (SensorManager.getRotationMatrix(R, I, gravity, geomagnetic)) {
                val orientation = FloatArray(3)
                SensorManager.getOrientation(R, orientation)
                azimuth = Math.toDegrees(orientation[0].toDouble()).toFloat() // Azimuth in degrees

                val difference = abs(azimuth - ishaanDirection)
                val currentlyAligned = difference <= marginOfError || abs(difference - 360f) <= marginOfError

                if (currentlyAligned) {
                    if (!isAligned) { // Only trigger on entering alignment
                        val pattern = longArrayOf(0, 500, 200) // Start immediately, vibrate for 500ms, pause for 200ms
                        vibrator?.vibrate(VibrationEffect.createWaveform(pattern, 0)) // Repeat indefinitely
                        isAligned = true
                    }
                } else {
                    if (isAligned) { // Only stop on exiting alignment
                        vibrator?.cancel()
                        isAligned = false
                    }
                }
            }
        }

        override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
            // Do nothing for now
        }
    }}

    DisposableEffect(sensorManager) {
        sensorManager?.registerListener(
            sensorEventListener,
            accelerometer,
            SensorManager.SENSOR_DELAY_UI
        )
        sensorManager?.registerListener(
            sensorEventListener,
            magnetometer,
            SensorManager.SENSOR_DELAY_UI
        )
        onDispose {
            sensorManager?.unregisterListener(sensorEventListener)
            vibrator?.cancel() // Ensure vibration stops when screen is disposed
        }
    }

    val glowAlpha by animateFloatAsState(
        targetValue = if (isAligned) 1f else 0f,
        animationSpec = tween(durationMillis = 400)
    )

    val infiniteTransition = rememberInfiniteTransition()
    val scale by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = 1.05f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 1000, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        )
    )

    val density = LocalDensity.current
    val glowRadiusPx = with(density) { 150.dp.toPx() } // 300dp diameter

    val glowBrush = if (isDarkMode) {
        Brush.radialGradient(
            colors = listOf(Color(0xFFFFCA28).copy(alpha = 0.8f), Color.Transparent), // Amber for dark mode
            radius = glowRadiusPx
        )
    } else {
        Brush.radialGradient(
            colors = listOf(Color(0xFF42A5F5).copy(alpha = 0.8f), Color.Transparent), // Blue for light mode
            radius = glowRadiusPx
        )
    }

    val screenBackgroundColor = MaterialTheme.colorScheme.background

    Box(
        modifier = Modifier.fillMaxSize().background(screenBackgroundColor), // Use dynamic background
        contentAlignment = Alignment.Center
    ) {
        // Glow View
        Box(
            modifier = Modifier
                .size(300.dp) // 300dp diameter
                .alpha(glowAlpha)
                .background(
                    brush = glowBrush,
                    shape = CircleShape
                )
        )

        // Center Image (Mandhar Image)
        AsyncImage(
            model = R.drawable.bhagwanseemandhar,
            contentDescription = "Bhagwan Seemandhar",
            modifier = Modifier
                .size(240.dp)
                .graphicsLayer(scaleX = scale, scaleY = scale)
        )

        // Divine Message
        Text(
            text = "भगवान श्री सीमंधर स्वामी",
            color = if (isDarkMode) Color(0xFFFFD700) else Color.Black, // Gold in dark, Black in light
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 40.dp)
                .alpha(glowAlpha) // Alpha controlled by glow
        )
    }
}

@Preview(showBackground = true)
@Composable
fun DishaYantraScreenPreview() {
    DishaYantraScreen(isDarkMode = false, onToggleTheme = {})
}
