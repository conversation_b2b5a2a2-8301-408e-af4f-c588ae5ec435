package com.example.arham.data.models

import com.google.firebase.Timestamp

/**
 * Data class representing content headings/categories
 */
data class ContentHeading(
    val headingId: String = "",
    val headingTitle: String = "",
    val order: Int = 0,
    val description: String = "",
    val isActive: Boolean = true,
    val createdAt: Timestamp = Timestamp.now(),
    val updatedAt: Timestamp = Timestamp.now()
)

/**
 * Data class for content items under headings
 */
data class HeadingContent(
    val contentId: String = "",
    val headingId: String = "",
    val title: String = "",
    val content: String = "",
    val author: String = "",
    val type: ContentType = ContentType.BHAJAN,
    val language: Language = Language.HINDI,
    val tags: List<String> = emptyList(),
    val order: Int = 0,
    val isPopular: Boolean = false,
    val viewCount: Long = 0,
    val createdAt: Timestamp = Timestamp.now()
)

/**
 * Combined data class for heading with its content
 */
data class HeadingWithContent(
    val heading: ContentHeading,
    val content: List<HeadingContent> = emptyList()
)
