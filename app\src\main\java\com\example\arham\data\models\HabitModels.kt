package com.example.arham.data.models

import androidx.compose.ui.graphics.Color
import java.time.LocalDate
import java.time.YearMonth

/**
 * Habit category enum with associated colors and icons
 */
enum class HabitCategory(
    val displayName: String,
    val color: Color,
    val emoji: String
) {
    SPORTS("Sports", Color(0xFF8B4513), "🏃"),
    HEALTH("Health", Color(0xFF4CAF50), "💚"),
    WORK("Work", Color(0xFF673AB7), "💼"),
    FOOD("Food", Color(0xFF00BCD4), "🍽️"),
    FITNESS("Fitness", Color(0xFFFF5722), "💪"),
    MEDITATION("Meditation", Color(0xFF9C27B0), "🧘"),
    READING("Reading", Color(0xFF3F51B5), "📚"),
    SLEEP("Sleep", Color(0xFF607D8B), "😴"),
    WATER("Water", Color(0xFF2196F3), "💧"),
    SOCIAL("Social", Color(0xFFE91E63), "👥")
}

/**
 * Main habit data class
 */
data class Habit(
    val id: String,
    val name: String,
    val category: HabitCategory,
    val color: Color = category.color,
    val createdDate: LocalDate = LocalDate.now(),
    val isActive: Boolean = true,
    val targetDays: Int = 7, // Days per week target
    val description: String = ""
)

/**
 * Habit completion record
 */
data class HabitCompletion(
    val habitId: String,
    val date: LocalDate,
    val isCompleted: Boolean = true,
    val completedAt: Long = System.currentTimeMillis()
)

/**
 * Streak information for a habit
 */
data class StreakInfo(
    val currentStreak: Int,
    val longestStreak: Int,
    val lastCompletedDate: LocalDate?
)

/**
 * Monthly analytics data
 */
data class MonthlyStats(
    val month: YearMonth,
    val completedDays: Int,
    val totalDays: Int,
    val completionRate: Float
) {
    val percentage: Int get() = (completionRate * 100).toInt()
}

/**
 * Habit analytics containing all statistics
 */
data class HabitAnalytics(
    val habit: Habit,
    val streak: StreakInfo,
    val monthlyStats: List<MonthlyStats>,
    val completions: List<HabitCompletion>,
    val totalCompletions: Int,
    val averageCompletionRate: Float
) {
    val currentMonthStats: MonthlyStats?
        get() = monthlyStats.find { it.month == YearMonth.now() }
    
    val lastMonthStats: MonthlyStats?
        get() = monthlyStats.find { it.month == YearMonth.now().minusMonths(1) }
}

/**
 * View mode for habit display
 */
enum class HabitViewMode {
    WEEK_VIEW,
    GRID_VIEW
}

/**
 * Repository interface for habit data operations
 */
interface HabitRepository {
    suspend fun getAllHabits(): List<Habit>
    suspend fun getHabitById(id: String): Habit?
    suspend fun insertHabit(habit: Habit)
    suspend fun updateHabit(habit: Habit)
    suspend fun deleteHabit(habitId: String)
    
    suspend fun getCompletionsForHabit(habitId: String): List<HabitCompletion>
    suspend fun getCompletionsForDate(date: LocalDate): List<HabitCompletion>
    suspend fun insertCompletion(completion: HabitCompletion)
    suspend fun deleteCompletion(habitId: String, date: LocalDate)
    
    suspend fun getStreakInfo(habitId: String): StreakInfo
    suspend fun getHabitAnalytics(habitId: String): HabitAnalytics
}

/**
 * In-memory implementation for local storage
 */
class LocalHabitRepository : HabitRepository {
    private val habits = mutableListOf<Habit>()
    private val completions = mutableListOf<HabitCompletion>()
    
    override suspend fun getAllHabits(): List<Habit> = habits.toList()
    
    override suspend fun getHabitById(id: String): Habit? = habits.find { it.id == id }
    
    override suspend fun insertHabit(habit: Habit) {
        habits.add(habit)
    }
    
    override suspend fun updateHabit(habit: Habit) {
        val index = habits.indexOfFirst { it.id == habit.id }
        if (index != -1) {
            habits[index] = habit
        }
    }
    
    override suspend fun deleteHabit(habitId: String) {
        habits.removeAll { it.id == habitId }
        completions.removeAll { it.habitId == habitId }
    }
    
    override suspend fun getCompletionsForHabit(habitId: String): List<HabitCompletion> =
        completions.filter { it.habitId == habitId }
    
    override suspend fun getCompletionsForDate(date: LocalDate): List<HabitCompletion> =
        completions.filter { it.date == date }
    
    override suspend fun insertCompletion(completion: HabitCompletion) {
        // Remove existing completion for same habit and date
        completions.removeAll { it.habitId == completion.habitId && it.date == completion.date }
        completions.add(completion)
    }
    
    override suspend fun deleteCompletion(habitId: String, date: LocalDate) {
        completions.removeAll { it.habitId == habitId && it.date == date }
    }
    
    override suspend fun getStreakInfo(habitId: String): StreakInfo {
        val habitCompletions = getCompletionsForHabit(habitId)
            .filter { it.isCompleted }
            .sortedByDescending { it.date }
        
        if (habitCompletions.isEmpty()) {
            return StreakInfo(0, 0, null)
        }
        
        var currentStreak = 0
        var longestStreak = 0
        var tempStreak = 0
        var currentDate = LocalDate.now()
        
        // Calculate current streak
        for (completion in habitCompletions) {
            if (completion.date == currentDate || completion.date == currentDate.minusDays(1)) {
                currentStreak++
                currentDate = completion.date.minusDays(1)
            } else {
                break
            }
        }
        
        // Calculate longest streak
        val allDates = habitCompletions.map { it.date }.sorted()
        var previousDate: LocalDate? = null
        
        for (date in allDates) {
            if (previousDate == null || date == previousDate.plusDays(1)) {
                tempStreak++
                longestStreak = maxOf(longestStreak, tempStreak)
            } else {
                tempStreak = 1
            }
            previousDate = date
        }
        
        return StreakInfo(
            currentStreak = currentStreak,
            longestStreak = longestStreak,
            lastCompletedDate = habitCompletions.firstOrNull()?.date
        )
    }
    
    override suspend fun getHabitAnalytics(habitId: String): HabitAnalytics {
        val habit = getHabitById(habitId) ?: throw IllegalArgumentException("Habit not found")
        val habitCompletions = getCompletionsForHabit(habitId)
        val streak = getStreakInfo(habitId)
        
        // Calculate monthly stats for last 12 months
        val monthlyStats = mutableListOf<MonthlyStats>()
        val currentMonth = YearMonth.now()
        
        for (i in 0..11) {
            val month = currentMonth.minusMonths(i.toLong())
            val monthCompletions = habitCompletions.filter { 
                YearMonth.from(it.date) == month && it.isCompleted 
            }
            val totalDaysInMonth = month.lengthOfMonth()
            val completedDays = monthCompletions.size
            val completionRate = completedDays.toFloat() / totalDaysInMonth
            
            monthlyStats.add(
                MonthlyStats(
                    month = month,
                    completedDays = completedDays,
                    totalDays = totalDaysInMonth,
                    completionRate = completionRate
                )
            )
        }
        
        val totalCompletions = habitCompletions.count { it.isCompleted }
        val averageCompletionRate = monthlyStats.map { it.completionRate }.average().toFloat()
        
        return HabitAnalytics(
            habit = habit,
            streak = streak,
            monthlyStats = monthlyStats,
            completions = habitCompletions,
            totalCompletions = totalCompletions,
            averageCompletionRate = averageCompletionRate
        )
    }
}
