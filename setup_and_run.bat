@echo off
echo 🔄 ArhamApp Data Converter Setup
echo ================================

echo.
echo Step 1: Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python not found. Trying python3...
    python3 --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ Python not installed. Please install Python from python.org
        pause
        exit /b 1
    ) else (
        set PYTHON_CMD=python3
        echo ✅ Found python3
    )
) else (
    set PYTHON_CMD=python
    echo ✅ Found python
)

echo.
echo Step 2: Installing required libraries...
%PYTHON_CMD% -m pip install pandas openpyxl xlrd

echo.
echo Step 3: Listing Excel files in current directory...
dir *.xlsx *.xls

echo.
echo Step 4: Ready to convert!
echo.
set /p filename="Enter your Excel file name (with extension): "

if not exist "%filename%" (
    echo ❌ File not found: %filename%
    echo Please make sure the file is in the same directory as this script.
    pause
    exit /b 1
)

echo.
echo 🔄 Converting %filename%...
%PYTHON_CMD% arham_auto_detect_converter.py "%filename%"

echo.
echo ✅ Conversion completed!
echo Check the generated JSON files in this directory.
pause
