package com.example.arham.ui.screens

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.arham.ui.components.GlassmorphismOverlay
import com.example.arham.ui.theme.eczarFamily
import com.example.arham.data.repository.FirestoreRepository
import com.example.arham.data.repository.SearchResult
import kotlinx.coroutines.launch

@Composable
fun SearchScreen(onClose: () -> Unit, isDarkMode: Boolean) {
    var searchText by remember { mutableStateOf("") }
    var searchResults by remember { mutableStateOf<List<SearchResult>>(emptyList()) }
    var isSearching by remember { mutableStateOf(false) }
    val focusRequester = remember { FocusRequester() }
    val keyboardController = LocalSoftwareKeyboardController.current
    val focusManager = LocalFocusManager.current
    val scope = rememberCoroutineScope()
    val repository = remember { FirestoreRepository() }

    // Function to perform search
    fun performSearch() {
        if (searchText.isNotBlank()) {
            isSearching = true
            scope.launch {
                try {
                    val results = repository.globalSearch(searchText)
                    searchResults = results
                } catch (e: Exception) {
                    // Handle error - maybe show a toast
                    searchResults = emptyList()
                } finally {
                    isSearching = false
                    keyboardController?.hide()
                    focusManager.clearFocus()
                }
            }
        }
    }

    // Handle system back button
    BackHandler {
        onClose()
    }

    // Auto-focus and show keyboard when screen opens
    LaunchedEffect(Unit) {
        focusRequester.requestFocus()
        keyboardController?.show()
    }

    // Perfect glassmorphism overlay with gradients
    GlassmorphismOverlay(
        modifier = Modifier
            .fillMaxSize()
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null
            ) { onClose() },
        blurRadius = 60f, // Extra strong blur to make colors pop but text unreadable
        isDarkTheme = isDarkMode
    ) {
        // Content with search bar positioned exactly like heading text in TulsiScreen
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.Start
        ) {
            // Empty space where back button was (maintaining same spacing)
            Spacer(modifier = Modifier.height(68.dp)) // 10+48+10 = 68dp total height

            // Global search field positioned EXACTLY where heading text is in TulsiScreen
            Column(
                modifier = Modifier.padding(start = 24.dp, top = 10.dp, bottom = 10.dp) // EXACT same padding as TulsiScreen heading
            ) {
                // Search field with placeholder text
                Box {
                    // Show placeholder when search is empty
                    if (searchText.isEmpty()) {
                        Text(
                            text = "खोजें...",
                            fontSize = 40.sp, // EXACT same as TulsiScreen
                            fontFamily = eczarFamily, // EXACT same as TulsiScreen
                            fontWeight = FontWeight.Normal, // Regular font weight
                            color = if (isDarkMode) {
                                Color(0xFF888888) // Solid gray for dark theme
                            } else {
                                Color(0xFF666666) // Solid gray for light theme
                            }
                        )
                    }

                    // Search input field
                    BasicTextField(
                        value = searchText,
                        onValueChange = { searchText = it },
                        modifier = Modifier
                            .fillMaxWidth()
                            .focusRequester(focusRequester),
                        textStyle = TextStyle(
                            fontSize = 40.sp,
                            fontFamily = eczarFamily,
                            fontWeight = FontWeight.Bold,
                            color = MaterialTheme.colorScheme.onBackground // Full color for actual text
                        ),
                        cursorBrush = SolidColor(MaterialTheme.colorScheme.onBackground),
                        keyboardOptions = KeyboardOptions(
                            imeAction = ImeAction.Search
                        ),
                        keyboardActions = KeyboardActions(
                            onSearch = { performSearch() }
                        ),
                        singleLine = true
                    )
                }

                // Underline positioned just beneath the text with 24dp padding from start and end
                Spacer(modifier = Modifier.height(4.dp)) // Small gap between text and line
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 0.dp, end = 24.dp) // 24dp padding from end (start already has 24dp from parent)
                        .height(2.dp)
                        .background(MaterialTheme.colorScheme.onBackground.copy(alpha = 0.3f))
                )

                // Search Results Section
                if (isSearching) {
                    Spacer(modifier = Modifier.height(32.dp))

                    Row(
                        modifier = Modifier.padding(start = 24.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            color = MaterialTheme.colorScheme.primary
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Text(
                            text = "Searching...",
                            fontSize = 16.sp,
                            fontFamily = eczarFamily,
                            color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.8f)
                        )
                    }
                } else if (searchResults.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(32.dp))

                    Text(
                        text = "Search Results (${searchResults.size})",
                        fontSize = 18.sp,
                        fontFamily = eczarFamily,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.8f),
                        modifier = Modifier.padding(start = 24.dp)
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Results List
                    searchResults.forEach { result ->
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 24.dp, vertical = 4.dp)
                                .clickable {
                                    // Navigate to content based on category
                                    when (result.category) {
                                        com.example.arham.data.models.ContentCategory.BHIKSHU -> {
                                            val encodedTitle = java.net.URLEncoder.encode(result.title, "UTF-8")
                                            // Navigate to bhajan e-reader
                                        }
                                        else -> {
                                            // Navigate to general e-reader
                                        }
                                    }
                                },
                            colors = CardDefaults.cardColors(
                                containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.7f)
                            )
                        ) {
                            Column(
                                modifier = Modifier.padding(16.dp)
                            ) {
                                Text(
                                    text = result.title,
                                    fontSize = 16.sp,
                                    fontFamily = eczarFamily,
                                    fontWeight = FontWeight.Medium,
                                    color = MaterialTheme.colorScheme.onSurface
                                )

                                Spacer(modifier = Modifier.height(4.dp))

                                Text(
                                    text = "by ${result.author}",
                                    fontSize = 14.sp,
                                    fontFamily = eczarFamily,
                                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                                )

                                if (result.snippet.isNotBlank()) {
                                    Spacer(modifier = Modifier.height(8.dp))
                                    Text(
                                        text = result.snippet,
                                        fontSize = 14.sp,
                                        fontFamily = eczarFamily,
                                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f),
                                        maxLines = 2
                                    )
                                }

                                Spacer(modifier = Modifier.height(4.dp))

                                Row(
                                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    Text(
                                        text = result.type.name,
                                        fontSize = 12.sp,
                                        fontFamily = eczarFamily,
                                        color = MaterialTheme.colorScheme.primary,
                                        modifier = Modifier
                                            .background(
                                                MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                                                RoundedCornerShape(4.dp)
                                            )
                                            .padding(horizontal = 6.dp, vertical = 2.dp)
                                    )

                                    Text(
                                        text = result.category.name,
                                        fontSize = 12.sp,
                                        fontFamily = eczarFamily,
                                        color = MaterialTheme.colorScheme.secondary,
                                        modifier = Modifier
                                            .background(
                                                MaterialTheme.colorScheme.secondary.copy(alpha = 0.1f),
                                                RoundedCornerShape(4.dp)
                                            )
                                            .padding(horizontal = 6.dp, vertical = 2.dp)
                                    )
                                }
                            }
                        }
                    }
                } else if (searchText.isNotBlank() && !isSearching) {
                    Spacer(modifier = Modifier.height(32.dp))

                    Text(
                        text = "No results found for \"$searchText\"",
                        fontSize = 16.sp,
                        fontFamily = eczarFamily,
                        color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.6f),
                        modifier = Modifier.padding(start = 24.dp)
                    )
                }
            }
        }

        // Gradient overlays for enhanced glassmorphism
        Box(modifier = Modifier.fillMaxSize()) {
            // Strong top gradient to completely hide status bar lines
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(60.dp) // Taller to ensure complete line coverage
                    .align(Alignment.TopCenter)
                    .background(
                        if (isDarkMode) {
                            Brush.verticalGradient(
                                colors = listOf(
                                    Color.Black.copy(alpha = 1.0f), // Completely opaque at top
                                    Color.Black.copy(alpha = 0.95f),
                                    Color.Black.copy(alpha = 0.8f),
                                    Color.Black.copy(alpha = 0.5f),
                                    Color.Black.copy(alpha = 0.2f),
                                    Color.Transparent
                                )
                            )
                        } else {
                            Brush.verticalGradient(
                                colors = listOf(
                                    Color.White.copy(alpha = 1.0f), // Completely opaque at top
                                    Color.White.copy(alpha = 0.95f),
                                    Color.White.copy(alpha = 0.8f),
                                    Color.White.copy(alpha = 0.5f),
                                    Color.White.copy(alpha = 0.2f),
                                    Color.Transparent
                                )
                            )
                        }
                    )
            )

            // Strong bottom gradient to completely hide navigation bar lines
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(60.dp) // Taller to ensure complete line coverage
                    .align(Alignment.BottomCenter)
                    .background(
                        if (isDarkMode) {
                            Brush.verticalGradient(
                                colors = listOf(
                                    Color.Transparent,
                                    Color.Black.copy(alpha = 0.2f),
                                    Color.Black.copy(alpha = 0.5f),
                                    Color.Black.copy(alpha = 0.8f),
                                    Color.Black.copy(alpha = 0.95f),
                                    Color.Black.copy(alpha = 1.0f) // Completely opaque at bottom
                                )
                            )
                        } else {
                            Brush.verticalGradient(
                                colors = listOf(
                                    Color.Transparent,
                                    Color.White.copy(alpha = 0.2f),
                                    Color.White.copy(alpha = 0.5f),
                                    Color.White.copy(alpha = 0.8f),
                                    Color.White.copy(alpha = 0.95f),
                                    Color.White.copy(alpha = 1.0f) // Completely opaque at bottom
                                )
                            )
                        }
                    )
            )
        }
    }
}