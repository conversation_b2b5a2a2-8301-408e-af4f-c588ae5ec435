package com.example.arham.ui.screens

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import android.view.HapticFeedbackConstants
import coil.compose.AsyncImage
import com.example.arham.R
import com.example.arham.ui.theme.eczarFamily

data class CardTheme(val lightBackground: Color, val darkBackground: Color, val highlight: Color)

val yatraOneRegular = FontFamily(Font(R.font.yatraone_regular))

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun AdhyatmaYatraScreen(navController: NavController, isDarkMode: Boolean, onToggleTheme: () -> Unit) {
    val backgroundColor = MaterialTheme.colorScheme.background

    val cardThemes = listOf(
        CardTheme(Color(0xFFFFF176), Color(0xFFB388EB), Color(0xFFFBC02D)), // Solar Glow / Mystic Mauve
        CardTheme(Color(0xFFFFCDD2), Color(0xFFFFAB91), Color(0xFFE91E63)), // Rose Radiance / Warm Coral Glow
        CardTheme(Color(0xFFB2EBF2), Color(0xFFA5D6A7), Color(0xFF00BCD4)), // Aqua Sky / Soft Mint Mist
        CardTheme(Color(0xFFC8E6C9), Color(0xFF81D4FA), Color(0xFF4CAF50)), // Mint Harmony / Tranquil Azure
        CardTheme(Color(0xFFE1BEE7), Color(0xFFFFD54F), Color(0xFF9C27B0)), // Mystic Violet / Radiant Amber
        CardTheme(Color(0xFFFFE0B2), Color(0xFFF8BBD0), Color(0xFFD6A77A))  // Saffron Light / Blush Blossom
    )

    val colors = if (isDarkMode) {
        listOf(
            Color(0xFFFFCA28), // Amber
            Color(0xFFFFA726), // Orange
            Color(0xFFFF7043)  // Deep Orange
        )
    } else {
        listOf(
            Color(0xFF42A5F5), // Blue 400
            Color(0xFF1E88E5), // Blue 600
            Color(0xFF0D47A1)  // Blue 900
        )
    }
    val infiniteTransition = rememberInfiniteTransition()
    val offset by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 2000, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        )
    )

    val brush = Brush.linearGradient(
        colors = colors,
        start = androidx.compose.ui.geometry.Offset(0f, 0f),
        end = androidx.compose.ui.geometry.Offset(offset * 500f, offset * 500f)
    )

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.Start
        ) {
            Text(
                text = "अध्यात्म यात्रा",
                fontSize = 48.sp,
                fontFamily = yatraOneRegular,
                modifier = Modifier.padding(start = 24.dp, top = 8.dp, bottom = 16.dp)
            )

            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 24.dp)
                    .weight(1f), // Make LazyColumn fill remaining space and scroll
                horizontalAlignment = Alignment.Start
            ) {
                items(6) { index ->
                    val currentCardTheme = cardThemes[index]
                    val cardHighlightColor = currentCardTheme.highlight

                    Card(
                        onClick = {
                            when (index) {
                                0 -> navController.navigate("content/DAINIK_SWADHYAY")
                                1 -> navController.navigate("adhyayan_smaran")
                                3 -> navController.navigate("dishayantra")
                                4 -> navController.navigate("sadhanatracker")
                            }
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(170.dp) // Card Height
                            .padding(vertical = 8.dp) // Spacing Between Cards
                            .border(2.dp, brush, RoundedCornerShape(20.dp)), // Animated gradient border
                        colors = CardDefaults.cardColors(containerColor = Color.Transparent), // Transparent background
                        shape = RoundedCornerShape(20.dp) // Rounded corners for calm feel
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(16.dp), // Card Padding
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Start // Arrange text and image
                        ) {
                            Column(modifier = Modifier.weight(1f)) {
                                when (index) {
                                    0 -> {
                                        Text(
                                            text = "दैनिक स्वाध्याय",
                                            style = MaterialTheme.typography.bodyLarge.copy(fontSize = 36.sp, fontFamily = eczarFamily, fontWeight = FontWeight.Bold, color = if (isDarkMode) Color(0xFFFF7400) else Color(0xFF4169E1))
                                        )
                                        Text(
                                            text = "पावन गीतों, श्लोकों और पाठ का श्रद्धा से अभ्यास करें।",
                                            style = MaterialTheme.typography.bodyLarge.copy(fontSize = 18.sp, fontFamily = eczarFamily, fontStyle = FontStyle.Italic, color = if (isDarkMode) Color.White else Color.Black)
                                        )
                                    }
                                    1 -> {
                                        Text(
                                            text = "अध्ययन व स्मरण",
                                            style = MaterialTheme.typography.bodyLarge.copy(fontSize = 36.sp, fontFamily = eczarFamily, fontWeight = FontWeight.Bold, color = if (isDarkMode) Color(0xFFFF7400) else Color(0xFF4169E1))
                                        )
                                        Text(
                                            text = "ज्ञान समझें और हृदयस्थ करें।",
                                            style = MaterialTheme.typography.bodyLarge.copy(fontSize = 18.sp, fontFamily = eczarFamily, fontStyle = FontStyle.Italic, color = if (isDarkMode) Color.White else Color.Black)
                                        )
                                    }
                                    2 -> {
                                        Text(
                                            text = "पंचांग",
                                            style = MaterialTheme.typography.bodyLarge.copy(fontSize = 36.sp, fontFamily = eczarFamily, fontWeight = FontWeight.Bold, color = if (isDarkMode) Color(0xFFFF7400) else Color(0xFF4169E1))
                                        )
                                        Text(
                                            text = "तिथि, पर्व और मुहूर्त की जानकारी पाएं।",
                                            style = MaterialTheme.typography.bodyLarge.copy(fontSize = 18.sp, fontFamily = eczarFamily, fontStyle = FontStyle.Italic, color = if (isDarkMode) Color.White else Color.Black)
                                        )
                                    }
                                    3 -> {
                                        Text(
                                            text = "दिशा यंत्र",
                                            style = MaterialTheme.typography.bodyLarge.copy(fontSize = 36.sp, fontFamily = eczarFamily, fontWeight = FontWeight.Bold, color = if (isDarkMode) Color(0xFFFF7400) else Color(0xFF4169E1))
                                        )
                                        Text(
                                            text = "तीर्थंकर व गुरुदेव की वंदना के लिए सही दिशा जानें।",
                                            style = MaterialTheme.typography.bodyLarge.copy(fontSize = 18.sp, fontFamily = eczarFamily, fontStyle = FontStyle.Italic, color = if (isDarkMode) Color.White else Color.Black)
                                        )
                                    }
                                    4 -> {
                                        Text(
                                            text = "साधना नियम",
                                            style = MaterialTheme.typography.bodyLarge.copy(fontSize = 36.sp, fontFamily = eczarFamily, fontWeight = FontWeight.Bold, color = if (isDarkMode) Color(0xFFFF7400) else Color(0xFF4169E1))
                                        )
                                        Text(
                                            text = "स्वाध्याय, उपवास और संयम ट्रैक करें।",
                                            style = MaterialTheme.typography.bodyLarge.copy(fontSize = 18.sp, fontFamily = eczarFamily, fontStyle = FontStyle.Italic, color = if (isDarkMode) Color.White else Color.Black)
                                        )
                                    }
                                    5 -> {
                                        Text(
                                            text = "ज्ञान प्रश्नोत्तरी",
                                            style = MaterialTheme.typography.bodyLarge.copy(fontSize = 36.sp, fontFamily = eczarFamily, fontWeight = FontWeight.Bold, color = if (isDarkMode) Color(0xFFFF7400) else Color(0xFF4169E1))
                                        )
                                        Text(
                                            text = "खेलते हुए सीखें जैन तत्व।",
                                            style = MaterialTheme.typography.bodyLarge.copy(fontSize = 18.sp, fontFamily = eczarFamily, fontStyle = FontStyle.Italic, color = if (isDarkMode) Color.White else Color.Black)
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // Bottom Navigation section (replicated from TodayScreen)
        Row(
            modifier = Modifier
                .fillMaxWidth() // Fill the width
                .align(Alignment.BottomCenter) // Align to the bottom center of the parent Box
                .background(backgroundColor), // Ensure background covers content behind it
            horizontalArrangement = Arrangement.SpaceAround // Space navigation items evenly
        ) {
            // Placeholder for bottom navigation items (e.g., buttons, icons)
        }
    }
}

@Preview(showBackground = true)
@Composable
fun AdhyatmaYatraScreenPreview() {
    val navController = rememberNavController()
    AdhyatmaYatraScreen(navController = navController, isDarkMode = false, onToggleTheme = {})
}
