#!/usr/bin/env python3
"""
ArhamApp Hierarchical Data Converter
Converts 5-level hierarchy Excel to nested JSON for ArhamApp
"""

import pandas as pd
import json
import uuid
from datetime import datetime
import sys
import os

def convert_arham_hierarchy(file_path):
    """
    Convert ArhamApp 5-level hierarchy Excel to nested JSON
    
    Structure:
    Sheet 1: Explore Screen (Dainik Swadhyaya)
    Sheet 2: Section (Geet/Dhaal Sangrah)  
    Sheet 3: Category (Bhikshu Stuti)
    Sheet 4: Author (<PERSON><PERSON>)
    Sheet 5: Title (Ru Ru mai sanwariyo)
    """
    
    try:
        print("🔄 Reading ArhamApp hierarchical data...")
        
        # Read all sheets
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        
        print(f"📋 Found {len(sheet_names)} sheets: {sheet_names}")
        
        # Read each sheet
        sheets_data = {}
        for sheet_name in sheet_names:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            sheets_data[sheet_name] = df
            print(f"✅ Sheet '{sheet_name}': {len(df)} rows")
        
        # Build hierarchical structure
        hierarchy = build_hierarchy_structure(sheets_data, sheet_names)
        
        # Save hierarchical JSON
        output_path = "arham_hierarchical_data.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(hierarchy, f, indent=2, ensure_ascii=False)
        
        print(f"🎉 Hierarchical JSON created: {output_path}")
        
        # Also create flat structure for Firestore
        flat_data = create_flat_firestore_data(hierarchy)
        flat_output = "arham_firestore_flat.json"
        with open(flat_output, 'w', encoding='utf-8') as f:
            json.dump(flat_data, f, indent=2, ensure_ascii=False)
        
        print(f"🔥 Firestore flat JSON created: {flat_output}")
        
        return output_path, flat_output
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None, None

def build_hierarchy_structure(sheets_data, sheet_names):
    """
    Build 5-level hierarchical structure
    """
    
    hierarchy = {
        "arhamApp": {
            "exploreScreens": {},
            "metadata": {
                "totalLevels": 5,
                "structure": ["exploreScreen", "section", "category", "author", "title"],
                "createdAt": int(datetime.now().timestamp() * 1000),
                "version": "1.0"
            }
        }
    }
    
    # Process each level
    current_path = []
    
    for level, sheet_name in enumerate(sheet_names):
        df = sheets_data[sheet_name]
        level_name = get_level_name(level)
        
        print(f"🔄 Processing Level {level + 1}: {level_name} ({sheet_name})")
        
        # Process each row in the sheet
        for _, row in df.iterrows():
            process_hierarchy_row(hierarchy, row, level, current_path, df.columns)
    
    return hierarchy

def get_level_name(level):
    """Get level name based on index"""
    level_names = [
        "exploreScreen",  # Level 0: Dainik Swadhyaya
        "section",        # Level 1: Geet/Dhaal Sangrah
        "category",       # Level 2: Bhikshu Stuti
        "author",         # Level 3: Acharya Tulsi
        "title"          # Level 4: Ru Ru mai sanwariyo
    ]
    return level_names[level] if level < len(level_names) else f"level_{level}"

def process_hierarchy_row(hierarchy, row, level, current_path, columns):
    """Process a single row and add to hierarchy"""
    
    # Extract main identifier (first column usually contains the name)
    main_column = columns[0]
    item_name = str(row[main_column]).strip()
    
    if not item_name or item_name == 'nan':
        return
    
    # Create item structure
    item = {
        "id": generate_id(item_name),
        "name": item_name,
        "level": level,
        "order": len(current_path) + 1,
        "metadata": {
            "createdAt": int(datetime.now().timestamp() * 1000)
        }
    }
    
    # Add all other columns as metadata
    for col in columns[1:]:
        if col in row and not pd.isna(row[col]):
            value = str(row[col]).strip()
            if value:
                field_name = col.lower().replace(' ', '_')
                item["metadata"][field_name] = value
    
    # Add to hierarchy based on level
    if level == 0:  # Explore Screen
        if item_name not in hierarchy["arhamApp"]["exploreScreens"]:
            hierarchy["arhamApp"]["exploreScreens"][item_name] = {
                **item,
                "sections": {},
                "stats": {"totalSections": 0, "totalItems": 0}
            }
    
    elif level == 1:  # Section
        # Find parent explore screen
        parent_screen = find_parent_explore_screen(hierarchy, row, columns)
        if parent_screen:
            if item_name not in hierarchy["arhamApp"]["exploreScreens"][parent_screen]["sections"]:
                hierarchy["arhamApp"]["exploreScreens"][parent_screen]["sections"][item_name] = {
                    **item,
                    "categories": {},
                    "stats": {"totalCategories": 0, "totalItems": 0}
                }
    
    elif level == 2:  # Category
        # Find parent section and explore screen
        parent_screen, parent_section = find_parent_section(hierarchy, row, columns)
        if parent_screen and parent_section:
            sections = hierarchy["arhamApp"]["exploreScreens"][parent_screen]["sections"]
            if parent_section in sections:
                if item_name not in sections[parent_section]["categories"]:
                    sections[parent_section]["categories"][item_name] = {
                        **item,
                        "authors": {},
                        "stats": {"totalAuthors": 0, "totalItems": 0}
                    }
    
    elif level == 3:  # Author
        # Find parent category, section, and explore screen
        parent_screen, parent_section, parent_category = find_parent_category(hierarchy, row, columns)
        if parent_screen and parent_section and parent_category:
            categories = hierarchy["arhamApp"]["exploreScreens"][parent_screen]["sections"][parent_section]["categories"]
            if parent_category in categories:
                if item_name not in categories[parent_category]["authors"]:
                    categories[parent_category]["authors"][item_name] = {
                        **item,
                        "titles": {},
                        "stats": {"totalTitles": 0}
                    }
    
    elif level == 4:  # Title (Final content)
        # Find all parents and add the actual content
        parents = find_all_parents(hierarchy, row, columns)
        if parents:
            parent_screen, parent_section, parent_category, parent_author = parents
            authors = hierarchy["arhamApp"]["exploreScreens"][parent_screen]["sections"][parent_section]["categories"][parent_category]["authors"]
            if parent_author in authors:
                content_item = {
                    **item,
                    "content": extract_content_data(row, columns),
                    "isLeaf": True
                }
                authors[parent_author]["titles"][item_name] = content_item

def generate_id(name):
    """Generate unique ID from name"""
    # Create a clean ID from the name
    clean_name = name.lower().replace(' ', '_').replace('/', '_')
    return f"{clean_name}_{str(uuid.uuid4())[:8]}"

def extract_content_data(row, columns):
    """Extract actual content data from the row"""
    content = {}
    
    # Common content fields
    content_fields = ['content', 'lyrics', 'text', 'description', 'meaning']
    
    for col in columns:
        col_lower = col.lower()
        if any(field in col_lower for field in content_fields):
            if col in row and not pd.isna(row[col]):
                content[col_lower.replace(' ', '_')] = str(row[col]).strip()
    
    # If no specific content field found, use all available data
    if not content:
        for col in columns[1:]:  # Skip first column (name)
            if col in row and not pd.isna(row[col]):
                value = str(row[col]).strip()
                if value:
                    content[col.lower().replace(' ', '_')] = value
    
    return content

def find_parent_explore_screen(hierarchy, row, columns):
    """Find parent explore screen for a section"""
    # This would need to be implemented based on your data structure
    # For now, return the first explore screen
    explore_screens = list(hierarchy["arhamApp"]["exploreScreens"].keys())
    return explore_screens[0] if explore_screens else None

def find_parent_section(hierarchy, row, columns):
    """Find parent section and explore screen for a category"""
    # Implementation depends on your data relationships
    # Return first available for now
    for screen_name, screen_data in hierarchy["arhamApp"]["exploreScreens"].items():
        sections = list(screen_data["sections"].keys())
        if sections:
            return screen_name, sections[0]
    return None, None

def find_parent_category(hierarchy, row, columns):
    """Find parent category, section, and explore screen for an author"""
    # Implementation depends on your data relationships
    for screen_name, screen_data in hierarchy["arhamApp"]["exploreScreens"].items():
        for section_name, section_data in screen_data["sections"].items():
            categories = list(section_data["categories"].keys())
            if categories:
                return screen_name, section_name, categories[0]
    return None, None, None

def find_all_parents(hierarchy, row, columns):
    """Find all parent levels for a title"""
    # Implementation depends on your data relationships
    for screen_name, screen_data in hierarchy["arhamApp"]["exploreScreens"].items():
        for section_name, section_data in screen_data["sections"].items():
            for category_name, category_data in section_data["categories"].items():
                authors = list(category_data["authors"].keys())
                if authors:
                    return screen_name, section_name, category_name, authors[0]
    return None

def create_flat_firestore_data(hierarchy):
    """Create flat structure for Firestore from hierarchical data"""
    
    flat_data = []
    
    def traverse_hierarchy(data, path="", level=0):
        if isinstance(data, dict):
            for key, value in data.items():
                if key in ["exploreScreens", "sections", "categories", "authors", "titles"]:
                    traverse_hierarchy(value, path, level)
                elif isinstance(value, dict) and "isLeaf" in value:
                    # This is a final content item
                    flat_item = {
                        "id": value.get("id", str(uuid.uuid4())),
                        "title": value.get("name", ""),
                        "content": json.dumps(value.get("content", {})),
                        "level": value.get("level", 0),
                        "path": path + "/" + key,
                        "metadata": value.get("metadata", {}),
                        "createdAt": int(datetime.now().timestamp() * 1000),
                        "updatedAt": int(datetime.now().timestamp() * 1000)
                    }
                    flat_data.append(flat_item)
                else:
                    new_path = path + "/" + key if path else key
                    traverse_hierarchy(value, new_path, level + 1)
    
    traverse_hierarchy(hierarchy)
    return flat_data

if __name__ == "__main__":
    print("🔄 ArhamApp Hierarchical Data Converter")
    print("=" * 50)
    
    if len(sys.argv) < 2:
        print("Usage: python arham_hierarchy_converter.py <excel_file_path>")
        print("Example: python arham_hierarchy_converter.py arham_data.xlsx")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        sys.exit(1)
    
    hierarchical_output, flat_output = convert_arham_hierarchy(file_path)
    
    if hierarchical_output and flat_output:
        print("\n✅ Conversion completed successfully!")
        print(f"📊 Hierarchical JSON: {hierarchical_output}")
        print(f"🔥 Firestore JSON: {flat_output}")
        print("\n📋 Next steps:")
        print("1. Review the generated JSON files")
        print("2. Upload flat JSON to Firestore")
        print("3. Use hierarchical JSON in your app navigation")
    else:
        print("❌ Conversion failed!")
