package com.example.arham.firebase

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.activity.result.ActivityResultLauncher
import com.facebook.AccessToken
import com.facebook.CallbackManager
import com.facebook.FacebookCallback
import com.facebook.FacebookException
import com.facebook.FacebookSdk
import com.facebook.login.LoginManager
import com.facebook.login.LoginResult
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.ApiException
import com.google.firebase.auth.*
import kotlinx.coroutines.tasks.await

class AuthService(private val context: Context) {
    
    private val auth = FirebaseManager.auth
    private lateinit var googleSignInClient: GoogleSignInClient
    private lateinit var callbackManager: CallbackManager
    
    init {
        // Initialize Facebook SDK (temporarily disabled until Client Token is added)
        // FacebookSdk.sdkInitialize(context)
        setupGoogleSignIn()
        // setupFacebookSignIn() // Disabled until Facebook is properly configured
    }
    
    /**
     * Setup Google Sign In
     */
    private fun setupGoogleSignIn() {
        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestIdToken("************-u169cvgn4q069elpgid2qgmmqc8uekae.apps.googleusercontent.com") // Web client ID from Firebase
            .requestEmail()
            .build()
        
        googleSignInClient = GoogleSignIn.getClient(context, gso)
    }
    
    /**
     * Setup Facebook Sign In
     */
    private fun setupFacebookSignIn() {
        callbackManager = CallbackManager.Factory.create()
    }
    
    /**
     * Get Google Sign In Intent
     */
    fun getGoogleSignInIntent(): Intent {
        return googleSignInClient.signInIntent
    }
    
    /**
     * Handle Google Sign In Result
     */
    suspend fun handleGoogleSignInResult(data: Intent?): AuthResult {
        return try {
            val task = GoogleSignIn.getSignedInAccountFromIntent(data)
            val account = task.getResult(ApiException::class.java)
            firebaseAuthWithGoogle(account)
        } catch (e: ApiException) {
            throw Exception("Google sign in failed: ${e.message}")
        }
    }
    
    /**
     * Authenticate with Firebase using Google credentials
     */
    private suspend fun firebaseAuthWithGoogle(account: GoogleSignInAccount): AuthResult {
        val credential = GoogleAuthProvider.getCredential(account.idToken, null)
        return auth.signInWithCredential(credential).await()
    }
    
    /**
     * Sign in with Facebook
     */
    fun signInWithFacebook(activity: Activity, onResult: (Boolean, String?) -> Unit) {
        LoginManager.getInstance().registerCallback(callbackManager, object : FacebookCallback<LoginResult> {
            override fun onSuccess(result: LoginResult) {
                handleFacebookAccessToken(result.accessToken, onResult)
            }
            
            override fun onCancel() {
                onResult(false, "Facebook sign in cancelled")
            }
            
            override fun onError(error: FacebookException) {
                onResult(false, "Facebook sign in error: ${error.message}")
            }
        })
        
        LoginManager.getInstance().logInWithReadPermissions(activity, listOf("email", "public_profile"))
    }
    
    /**
     * Handle Facebook Access Token
     */
    private fun handleFacebookAccessToken(token: AccessToken, onResult: (Boolean, String?) -> Unit) {
        val credential = FacebookAuthProvider.getCredential(token.token)
        auth.signInWithCredential(credential)
            .addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    onResult(true, null)
                } else {
                    onResult(false, task.exception?.message)
                }
            }
    }
    
    /**
     * Send phone verification code
     */
    fun sendPhoneVerificationCode(
        phoneNumber: String,
        activity: Activity,
        callbacks: PhoneAuthProvider.OnVerificationStateChangedCallbacks
    ) {
        val options = PhoneAuthOptions.newBuilder(auth)
            .setPhoneNumber(phoneNumber)
            .setTimeout(60L, java.util.concurrent.TimeUnit.SECONDS)
            .setActivity(activity)
            .setCallbacks(callbacks)
            .build()
        
        PhoneAuthProvider.verifyPhoneNumber(options)
    }
    
    /**
     * Verify phone number with code
     */
    suspend fun verifyPhoneNumberWithCode(verificationId: String, code: String): AuthResult {
        val credential = PhoneAuthProvider.getCredential(verificationId, code)
        return auth.signInWithCredential(credential).await()
    }
    
    /**
     * Sign in with email and password
     */
    suspend fun signInWithEmailPassword(email: String, password: String): AuthResult {
        return auth.signInWithEmailAndPassword(email, password).await()
    }
    
    /**
     * Create account with email and password
     */
    suspend fun createAccountWithEmailPassword(email: String, password: String): AuthResult {
        return auth.createUserWithEmailAndPassword(email, password).await()
    }
    
    /**
     * Handle Facebook callback result
     */
    fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        callbackManager.onActivityResult(requestCode, resultCode, data)
    }
}
