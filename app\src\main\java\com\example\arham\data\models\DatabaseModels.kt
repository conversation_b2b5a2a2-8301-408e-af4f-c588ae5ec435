package com.example.arham.data.models

import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.ServerTimestamp
import java.util.Date

// Main Content Categories
enum class ContentType {
    BHAJAN,
    GEET,
    STORY,
    TEACHING,
    PRAYER,
    MANTRA,
    SUTRA,
    BIOGRAPHY
}

enum class ContentCategory {
    BHIKSHU,
    TULSI,
    KALU,
    MAHAPRAJNA,
    MANGAL,
    GENERAL
}

enum class Language {
    HINDI,
    RAJASTHANI,
    ENGLISH,
    SANSKRIT
}

// Main Content Model
data class SpiritualContent(
    @DocumentId
    val id: String = "",
    val title: String = "",
    val content: String = "",
    val author: String = "",
    val category: ContentCategory = ContentCategory.GENERAL,
    val type: ContentType = ContentType.BHAJAN,
    val language: Language = Language.HINDI,
    val tags: List<String> = emptyList(),
    val searchKeywords: List<String> = emptyList(), // For better search
    val audioUrl: String? = null,
    val imageUrl: String? = null,
    val duration: Int? = null, // in minutes
    val difficulty: String = "Beginner", // Beginner, Intermediate, Advanced
    val isPopular: Boolean = false,
    val popular: Boolean = false, // Firebase field name
    val viewCount: Long = 0,
    val likeCount: Long = 0,
    @ServerTimestamp
    val createdAt: Date? = null,
    @ServerTimestamp
    val updatedAt: Date? = null
)

// Author Information
data class Author(
    @DocumentId
    val id: String = "",
    val name: String = "",
    val biography: String = "",
    val birthDate: String = "",
    val birthPlace: String = "",
    val imageUrl: String? = null,
    val achievements: List<String> = emptyList(),
    val famousWorks: List<String> = emptyList(),
    val category: ContentCategory = ContentCategory.GENERAL,
    @ServerTimestamp
    val createdAt: Date? = null
)

// User Bookmarks
data class UserBookmark(
    @DocumentId
    val id: String = "",
    val userId: String = "",
    val contentId: String = "",
    val contentTitle: String = "",
    val contentType: ContentType = ContentType.BHAJAN,
    val category: ContentCategory = ContentCategory.GENERAL,
    val notes: String = "",
    @ServerTimestamp
    val bookmarkedAt: Date? = null
)

// User Reading History
data class ReadingHistory(
    @DocumentId
    val id: String = "",
    val userId: String = "",
    val contentId: String = "",
    val contentTitle: String = "",
    val readingProgress: Float = 0f, // 0.0 to 1.0
    val timeSpent: Long = 0, // in seconds
    @ServerTimestamp
    val lastReadAt: Date? = null
)

// Search Index for Global Search
data class SearchIndex(
    @DocumentId
    val id: String = "",
    val contentId: String = "",
    val title: String = "",
    val author: String = "",
    val category: ContentCategory = ContentCategory.GENERAL,
    val type: ContentType = ContentType.BHAJAN,
    val language: Language = Language.HINDI,
    val keywords: List<String> = emptyList(),
    val searchText: String = "", // Combined searchable text
    val popularity: Long = 0,
    @ServerTimestamp
    val indexedAt: Date? = null
)

// User Preferences
data class UserPreferences(
    @DocumentId
    val userId: String = "",
    val preferredLanguage: Language = Language.HINDI,
    val favoriteCategories: List<ContentCategory> = emptyList(),
    val fontSize: Int = 18,
    val isDarkMode: Boolean = false,
    val notificationsEnabled: Boolean = true,
    val autoBookmark: Boolean = false,
    @ServerTimestamp
    val updatedAt: Date? = null
)

// Daily Spiritual Practice (Sadhana)
data class SadhanaEntry(
    @DocumentId
    val id: String = "",
    val userId: String = "",
    val date: String = "", // YYYY-MM-DD format
    val contentRead: List<String> = emptyList(), // Content IDs
    val timeSpent: Long = 0, // in minutes
    val bhajansChanted: Int = 0,
    val meditationTime: Long = 0, // in minutes
    val notes: String = "",
    @ServerTimestamp
    val createdAt: Date? = null
)

// Content Collections (like playlists)
data class ContentCollection(
    @DocumentId
    val id: String = "",
    val name: String = "",
    val description: String = "",
    val contentIds: List<String> = emptyList(),
    val category: ContentCategory = ContentCategory.GENERAL,
    val isPublic: Boolean = true,
    val createdBy: String = "admin",
    val imageUrl: String? = null,
    @ServerTimestamp
    val createdAt: Date? = null
)

// App Analytics
data class ContentAnalytics(
    @DocumentId
    val contentId: String = "",
    val views: Long = 0,
    val uniqueViews: Long = 0,
    val bookmarks: Long = 0,
    val shares: Long = 0,
    val averageReadTime: Long = 0, // in seconds
    val completionRate: Float = 0f,
    @ServerTimestamp
    val lastUpdated: Date? = null
)
