#!/usr/bin/env python3
"""
Excel to Nested JSON Converter for ArhamApp
Creates nested JSON structure from Excel data
"""

import pandas as pd
import json
import uuid
from datetime import datetime
import sys
import os

def create_nested_json(file_path, structure_type="category_based"):
    """
    Create nested JSON structure from Excel data
    
    Args:
        file_path: Path to Excel file
        structure_type: Type of nesting ("category_based", "author_based", "custom")
    """
    
    try:
        # Read Excel file
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
        else:
            df = pd.read_excel(file_path)
        
        print(f"✅ File loaded: {len(df)} rows")
        print(f"📋 Columns: {list(df.columns)}")
        
        if structure_type == "category_based":
            return create_category_based_structure(df)
        elif structure_type == "author_based":
            return create_author_based_structure(df)
        elif structure_type == "hierarchical":
            return create_hierarchical_structure(df)
        else:
            return create_custom_nested_structure(df)
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def create_category_based_structure(df):
    """
    Create nested structure grouped by category
    
    Output:
    {
      "MANTRA": {
        "items": [...],
        "count": 5,
        "metadata": {...}
      },
      "BHAJAN": {
        "items": [...],
        "count": 3,
        "metadata": {...}
      }
    }
    """
    
    nested_data = {}
    
    # Group by category
    if 'category' in df.columns:
        categories = df['category'].unique()
        
        for category in categories:
            category_data = df[df['category'] == category]
            
            items = []
            for _, row in category_data.iterrows():
                item = {
                    "id": str(uuid.uuid4()),
                    "title": str(row.get('title', '')).strip(),
                    "content": str(row.get('content', '')).strip(),
                    "author": str(row.get('author', '')).strip(),
                    "metadata": {
                        "type": str(row.get('type', 'TEACHING')).upper(),
                        "language": str(row.get('language', 'HINDI')).upper(),
                        "tags": str(row.get('tags', '')).split(',') if row.get('tags') else [],
                        "isPopular": bool(row.get('isPopular', False)),
                        "viewCount": int(row.get('viewCount', 0)),
                        "createdAt": int(datetime.now().timestamp() * 1000)
                    }
                }
                items.append(item)
            
            nested_data[str(category)] = {
                "items": items,
                "count": len(items),
                "metadata": {
                    "category": str(category),
                    "lastUpdated": int(datetime.now().timestamp() * 1000),
                    "totalViews": sum(item["metadata"]["viewCount"] for item in items)
                }
            }
    
    # Save nested JSON
    output_path = "nested_category_based.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(nested_data, f, indent=2, ensure_ascii=False)
    
    print(f"🎉 Nested JSON created: {output_path}")
    print(f"📊 Categories: {len(nested_data)}")
    
    return output_path

def create_author_based_structure(df):
    """
    Create nested structure grouped by author
    
    Output:
    {
      "तुलसीदास": {
        "profile": {...},
        "works": [...],
        "statistics": {...}
      }
    }
    """
    
    nested_data = {}
    
    if 'author' in df.columns:
        authors = df['author'].unique()
        
        for author in authors:
            author_data = df[df['author'] == author]
            
            works = []
            categories = set()
            total_views = 0
            
            for _, row in author_data.iterrows():
                work = {
                    "id": str(uuid.uuid4()),
                    "title": str(row.get('title', '')).strip(),
                    "content": str(row.get('content', '')).strip(),
                    "category": str(row.get('category', 'GENERAL')).upper(),
                    "type": str(row.get('type', 'TEACHING')).upper(),
                    "language": str(row.get('language', 'HINDI')).upper(),
                    "tags": str(row.get('tags', '')).split(',') if row.get('tags') else [],
                    "viewCount": int(row.get('viewCount', 0)),
                    "isPopular": bool(row.get('isPopular', False))
                }
                
                works.append(work)
                categories.add(work["category"])
                total_views += work["viewCount"]
            
            nested_data[str(author)] = {
                "profile": {
                    "name": str(author),
                    "totalWorks": len(works),
                    "categories": list(categories),
                    "totalViews": total_views
                },
                "works": works,
                "statistics": {
                    "mostPopular": max(works, key=lambda x: x["viewCount"]) if works else None,
                    "averageViews": total_views / len(works) if works else 0,
                    "lastUpdated": int(datetime.now().timestamp() * 1000)
                }
            }
    
    output_path = "nested_author_based.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(nested_data, f, indent=2, ensure_ascii=False)
    
    print(f"🎉 Author-based nested JSON created: {output_path}")
    print(f"📊 Authors: <AUTHORS>
    
    return output_path

def create_hierarchical_structure(df):
    """
    Create deep hierarchical structure
    
    Output:
    {
      "spiritualContent": {
        "categories": {
          "MANTRA": {
            "types": {
              "PRAYER": {
                "languages": {
                  "SANSKRIT": {
                    "items": [...]
                  }
                }
              }
            }
          }
        }
      }
    }
    """
    
    hierarchical_data = {
        "spiritualContent": {
            "categories": {},
            "metadata": {
                "totalItems": len(df),
                "createdAt": int(datetime.now().timestamp() * 1000)
            }
        }
    }
    
    for _, row in df.iterrows():
        category = str(row.get('category', 'GENERAL')).upper()
        content_type = str(row.get('type', 'TEACHING')).upper()
        language = str(row.get('language', 'HINDI')).upper()
        
        # Create nested structure
        if category not in hierarchical_data["spiritualContent"]["categories"]:
            hierarchical_data["spiritualContent"]["categories"][category] = {
                "types": {},
                "metadata": {"count": 0}
            }
        
        if content_type not in hierarchical_data["spiritualContent"]["categories"][category]["types"]:
            hierarchical_data["spiritualContent"]["categories"][category]["types"][content_type] = {
                "languages": {},
                "metadata": {"count": 0}
            }
        
        if language not in hierarchical_data["spiritualContent"]["categories"][category]["types"][content_type]["languages"]:
            hierarchical_data["spiritualContent"]["categories"][category]["types"][content_type]["languages"][language] = {
                "items": [],
                "metadata": {"count": 0}
            }
        
        # Add item
        item = {
            "id": str(uuid.uuid4()),
            "title": str(row.get('title', '')).strip(),
            "content": str(row.get('content', '')).strip(),
            "author": str(row.get('author', '')).strip(),
            "tags": str(row.get('tags', '')).split(',') if row.get('tags') else [],
            "isPopular": bool(row.get('isPopular', False)),
            "viewCount": int(row.get('viewCount', 0)),
            "createdAt": int(datetime.now().timestamp() * 1000)
        }
        
        hierarchical_data["spiritualContent"]["categories"][category]["types"][content_type]["languages"][language]["items"].append(item)
        
        # Update counts
        hierarchical_data["spiritualContent"]["categories"][category]["metadata"]["count"] += 1
        hierarchical_data["spiritualContent"]["categories"][category]["types"][content_type]["metadata"]["count"] += 1
        hierarchical_data["spiritualContent"]["categories"][category]["types"][content_type]["languages"][language]["metadata"]["count"] += 1
    
    output_path = "nested_hierarchical.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(hierarchical_data, f, indent=2, ensure_ascii=False)
    
    print(f"🎉 Hierarchical nested JSON created: {output_path}")
    
    return output_path

if __name__ == "__main__":
    print("🔄 Excel to Nested JSON Converter")
    print("=" * 50)
    
    if len(sys.argv) < 2:
        print("Usage: python excel_to_nested_json.py <excel_file_path>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        sys.exit(1)
    
    print("\nSelect nesting structure:")
    print("1. Category-based grouping")
    print("2. Author-based grouping") 
    print("3. Deep hierarchical structure")
    
    choice = input("Enter choice (1, 2, or 3): ").strip()
    
    if choice == "1":
        create_nested_json(file_path, "category_based")
    elif choice == "2":
        create_nested_json(file_path, "author_based")
    elif choice == "3":
        create_nested_json(file_path, "hierarchical")
    else:
        print("❌ Invalid choice")
