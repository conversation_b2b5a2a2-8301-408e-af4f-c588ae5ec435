package com.example.arham.domain.usecases

import com.example.arham.domain.models.SpiritualContent
import com.example.arham.domain.models.ContentCategory
import com.example.arham.domain.repository.ContentRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

/**
 * Use case for getting content by category
 * Clean architecture - Domain layer business logic
 */
class GetContentByCategoryUseCase @Inject constructor(
    private val repository: ContentRepository
) {
    
    suspend operator fun invoke(category: ContentCategory): List<SpiritualContent> {
        return try {
            repository.getContentByCategory(category)
                .sortedWith(
                    compareByDescending<SpiritualContent> { it.isPopular }
                        .thenByDescending { it.viewCount }
                        .thenBy { it.title }
                )
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    fun getFlow(category: ContentCategory): Flow<List<SpiritualContent>> {
        return repository.getContentByCategoryFlow(category)
            .map { contentList ->
                contentList.sortedWith(
                    compareByDescending<SpiritualContent> { it.isPopular }
                        .thenByDescending { it.viewCount }
                        .thenBy { it.title }
                )
            }
    }
}
