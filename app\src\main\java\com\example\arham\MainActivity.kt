package com.example.arham

import android.content.Context
import android.content.SharedPreferences
import android.os.Build
import android.os.Bundle
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.core.view.WindowCompat
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import com.example.arham.ui.theme.ArhamTheme
import com.example.arham.ui.navigation.AppNavigation
import com.example.arham.utils.FacebookKeyHashHelper
import com.example.arham.ui.components.AuthWrapper
import androidx.navigation.compose.rememberNavController
import com.example.arham.data.DatabaseManager
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    private val PREFS_NAME = "arham_prefs"
    private val KEY_DARK_MODE = "dark_mode"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Generate Facebook Key Hash (for development only)
        FacebookKeyHashHelper.printKeyHash(this)

        // Enable edge-to-edge display
        enableEdgeToEdge()

        // Make system bars transparent and blend with content
        WindowCompat.setDecorFitsSystemWindows(window, false)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            window.setDecorFitsSystemWindows(false)
        } else {
            @Suppress("DEPRECATION")
            window.setFlags(
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
            )
        }

        // Initialize database
        lifecycleScope.launch {
            DatabaseManager.getInstance().initializeDatabase()
        }

        setContent {
            val context = LocalContext.current
            val sharedPrefs = remember { context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE) }

            var isDarkMode by remember { mutableStateOf(sharedPrefs.getBoolean(KEY_DARK_MODE, false)) }

            ArhamTheme(darkTheme = isDarkMode) {
                // A surface container using the 'background' color from the theme
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    val navController = rememberNavController()

                    AuthWrapper(
                        navController = navController,
                        isDarkMode = isDarkMode
                    ) {
                        AppNavigation(isDarkMode = isDarkMode, onToggleTheme = {
                            isDarkMode = !isDarkMode
                            sharedPrefs.edit().putBoolean(KEY_DARK_MODE, isDarkMode).apply()
                        })
                    }
                }
            }
        }
    }
}
