<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArhamApp Data Entry - Excel जैसा Easy!</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .form-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            border-left: 5px solid #4ECDC4;
        }
        
        .form-section h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3em;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 15px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        label {
            font-weight: 600;
            color: #555;
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        input, textarea, select {
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #4ECDC4;
            box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
        }
        
        textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .btn {
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            margin: 10px 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
        }
        
        .btn-success {
            background: linear-gradient(45deg, #56ab2f, #a8e6cf);
        }
        
        .data-preview {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .data-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #4ECDC4;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .data-item h4 {
            color: #333;
            margin-bottom: 8px;
        }
        
        .data-item p {
            color: #666;
            margin: 5px 0;
            font-size: 14px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            opacity: 0.9;
        }
        
        .quick-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕉️ ArhamApp Data Entry</h1>
            <p>Excel जैसा आसान! बस Type करें और Enter दबाएं</p>
        </div>
        
        <div class="main-content">
            <!-- Stats Section -->
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalItems">0</div>
                    <div class="stat-label">कुल Items</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalCategories">0</div>
                    <div class="stat-label">Categories</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="lastUpdated">अभी</div>
                    <div class="stat-label">Last Updated</div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="quick-actions">
                <button class="btn" onclick="loadSampleData()">📝 Sample Data Load करें</button>
                <button class="btn btn-secondary" onclick="clearAllData()">🗑️ All Clear करें</button>
                <button class="btn btn-success" onclick="exportToApp()">📱 App में Export करें</button>
            </div>
            
            <!-- Data Entry Form -->
            <div class="form-section">
                <h3>📝 नया Item Add करें (Excel जैसा Easy!)</h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="itemTitle">📖 Title/Name *</label>
                        <input type="text" id="itemTitle" placeholder="जैसे: गायत्री मंत्र, हनुमान चालीसा" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="itemCategory">📂 Category *</label>
                        <select id="itemCategory">
                            <option value="">Select Category</option>
                            <option value="explorescreen">Explore Screen (दैनिक स्वाध्याय)</option>
                            <option value="section">Section (गीत/ढाल संग्रह)</option>
                            <option value="category">Category (भिक्षु स्तुति)</option>
                            <option value="author">Author (आचार्य तुलसी)</option>
                            <option value="title">Title (रु रु मैं संवारियो)</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="itemAuthor">👤 Author/Creator</label>
                        <input type="text" id="itemAuthor" placeholder="जैसे: आचार्य तुलसी, महावीर स्वामी">
                    </div>
                    
                    <div class="form-group">
                        <label for="itemLanguage">🌐 Language</label>
                        <select id="itemLanguage">
                            <option value="hindi">हिंदी</option>
                            <option value="sanskrit">संस्कृत</option>
                            <option value="english">English</option>
                            <option value="gujarati">गुजराती</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="itemContent">📄 Content/Description</label>
                    <textarea id="itemContent" placeholder="यहाँ मंत्र, श्लोक, या content लिखें..."></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="itemTags">🏷️ Tags (comma separated)</label>
                        <input type="text" id="itemTags" placeholder="जैसे: मंत्र, प्रार्थना, भक्ति">
                    </div>
                    
                    <div class="form-group">
                        <label for="itemType">📋 Type</label>
                        <select id="itemType">
                            <option value="mantra">मंत्र</option>
                            <option value="bhajan">भजन</option>
                            <option value="story">कथा</option>
                            <option value="teaching">शिक्षा</option>
                            <option value="prayer">प्रार्थना</option>
                        </select>
                    </div>
                </div>
                
                <button class="btn" onclick="addItem()">➕ Add Item (Enter दबाने जैसा!)</button>
            </div>
            
            <!-- Data Preview -->
            <div class="form-section">
                <h3>👀 आपका Data Preview</h3>
                <div id="dataPreview" class="data-preview">
                    <p style="text-align: center; color: #666; padding: 40px;">
                        अभी तक कोई data नहीं है। ऊपर form भरकर items add करें!
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Data storage
        let arhamData = [];
        
        // Load data from localStorage
        function loadData() {
            const saved = localStorage.getItem('arhamAppData');
            if (saved) {
                arhamData = JSON.parse(saved);
                updatePreview();
                updateStats();
            }
        }
        
        // Save data to localStorage
        function saveData() {
            localStorage.setItem('arhamAppData', JSON.stringify(arhamData));
        }
        
        // Add new item
        function addItem() {
            const title = document.getElementById('itemTitle').value.trim();
            const category = document.getElementById('itemCategory').value;
            const author = document.getElementById('itemAuthor').value.trim();
            const language = document.getElementById('itemLanguage').value;
            const content = document.getElementById('itemContent').value.trim();
            const tags = document.getElementById('itemTags').value.trim();
            const type = document.getElementById('itemType').value;
            
            if (!title || !category) {
                alert('कृपया Title और Category भरें!');
                return;
            }
            
            const newItem = {
                id: 'arham_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                title: title,
                category: category,
                author: author,
                language: language,
                content: content,
                tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag),
                type: type,
                createdAt: Date.now(),
                updatedAt: Date.now()
            };
            
            arhamData.push(newItem);
            saveData();
            updatePreview();
            updateStats();
            clearForm();
            
            // Success message
            alert('✅ Item successfully add हो गया!');
        }
        
        // Clear form
        function clearForm() {
            document.getElementById('itemTitle').value = '';
            document.getElementById('itemCategory').value = '';
            document.getElementById('itemAuthor').value = '';
            document.getElementById('itemContent').value = '';
            document.getElementById('itemTags').value = '';
            document.getElementById('itemTitle').focus();
        }
        
        // Update preview
        function updatePreview() {
            const preview = document.getElementById('dataPreview');
            
            if (arhamData.length === 0) {
                preview.innerHTML = '<p style="text-align: center; color: #666; padding: 40px;">अभी तक कोई data नहीं है। ऊपर form भरकर items add करें!</p>';
                return;
            }
            
            let html = '';
            arhamData.slice(-10).reverse().forEach((item, index) => {
                html += `
                    <div class="data-item">
                        <h4>📖 ${item.title}</h4>
                        <p><strong>Category:</strong> ${item.category}</p>
                        <p><strong>Author:</strong> ${item.author || 'Not specified'}</p>
                        <p><strong>Language:</strong> ${item.language}</p>
                        <p><strong>Type:</strong> ${item.type}</p>
                        <p><strong>Content:</strong> ${item.content.substring(0, 100)}${item.content.length > 100 ? '...' : ''}</p>
                        <p><strong>Tags:</strong> ${item.tags.join(', ')}</p>
                        <p style="font-size: 12px; color: #999;">ID: ${item.id}</p>
                        <button onclick="deleteItem('${item.id}')" style="background: #ff4757; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer; margin-top: 10px;">🗑️ Delete</button>
                    </div>
                `;
            });
            
            preview.innerHTML = html;
        }
        
        // Update stats
        function updateStats() {
            document.getElementById('totalItems').textContent = arhamData.length;
            
            const categories = [...new Set(arhamData.map(item => item.category))];
            document.getElementById('totalCategories').textContent = categories.length;
            
            const lastItem = arhamData[arhamData.length - 1];
            if (lastItem) {
                const time = new Date(lastItem.createdAt).toLocaleTimeString('hi-IN');
                document.getElementById('lastUpdated').textContent = time;
            }
        }
        
        // Delete item
        function deleteItem(id) {
            if (confirm('क्या आप इस item को delete करना चाहते हैं?')) {
                arhamData = arhamData.filter(item => item.id !== id);
                saveData();
                updatePreview();
                updateStats();
            }
        }
        
        // Load sample data
        function loadSampleData() {
            const sampleData = [
                {
                    id: 'sample_1',
                    title: 'गायत्री मंत्र',
                    category: 'title',
                    author: 'वेद व्यास',
                    language: 'sanskrit',
                    content: 'ॐ भूर्भुवः स्वः तत्सवितुर्वरेण्यं भर्गो देवस्य धीमहि धियो यो नः प्रचोदयात्',
                    tags: ['मंत्र', 'प्रार्थना', 'वेद'],
                    type: 'mantra',
                    createdAt: Date.now(),
                    updatedAt: Date.now()
                },
                {
                    id: 'sample_2',
                    title: 'हनुमान चालीसा',
                    category: 'title',
                    author: 'तुलसीदास',
                    language: 'hindi',
                    content: 'श्रीगुरु चरन सरोज रज निज मनु मुकुरु सुधारि',
                    tags: ['भजन', 'हनुमान', 'चालीसा'],
                    type: 'bhajan',
                    createdAt: Date.now(),
                    updatedAt: Date.now()
                }
            ];
            
            arhamData = [...arhamData, ...sampleData];
            saveData();
            updatePreview();
            updateStats();
            alert('✅ Sample data load हो गया!');
        }
        
        // Clear all data
        function clearAllData() {
            if (confirm('क्या आप सारा data delete करना चाहते हैं?')) {
                arhamData = [];
                saveData();
                updatePreview();
                updateStats();
                alert('✅ सारा data clear हो गया!');
            }
        }
        
        // Export to app format
        function exportToApp() {
            if (arhamData.length === 0) {
                alert('कोई data नहीं है export करने के लिए!');
                return;
            }
            
            const appFormat = {
                spiritual_content: arhamData,
                metadata: {
                    version: '1.0',
                    totalItems: arhamData.length,
                    exportedAt: Date.now(),
                    categories: [...new Set(arhamData.map(item => item.category))]
                }
            };
            
            // Download JSON file
            const blob = new Blob([JSON.stringify(appFormat, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'arham_app_data_' + new Date().toISOString().split('T')[0] + '.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            alert('✅ Data export हो गया! File download हो रही है।');
        }
        
        // Enter key support
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && e.ctrlKey) {
                addItem();
            }
        });
        
        // Initialize
        loadData();
        document.getElementById('itemTitle').focus();
    </script>
</body>
</html>
