package com.example.arham.ui.screens

import java.util.UUID
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.navigation.NavController
import com.example.arham.ui.theme.eczarFamily
import com.google.firebase.firestore.FirebaseFirestore
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

data class ContentItem(val id: Int, val content: String, val titleId: String? = null)

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun UniversalContentScreen(
    navController: NavController,
    category: String
) {
    var itemsList by remember { mutableStateOf<List<ContentItem>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    val db = FirebaseFirestore.getInstance()

    // Get screen title based on category
    val screenTitle = when (category.uppercase()) {
        "BHIKSHU" -> "भिक्षु-स्तुति"
        "DAINIK_SWADHYAY" -> "दैनिक स्वाध्याय"
        "GEET_DHAAL" -> "गीत/ढाल संग्रह"
        else -> "सामग्री"
    }

    // Load content from database
    LaunchedEffect(category) {
        loadStructuredContent(db, category) { items ->
            itemsList = items
            isLoading = false
        }
    }
    
    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.Start // Align content to start for back button
        ) {
            Box(
                modifier = Modifier
                    .padding(top = 10.dp, start = 8.dp, bottom = 10.dp) // External padding, 8dp left
                    .clickable { navController.popBackStack() } // Make the box clickable
            ) {
                Icon(
                    Icons.Filled.ArrowBack,
                    contentDescription = "Back",
                    modifier = Modifier.size(48.dp),
                    tint = MaterialTheme.colorScheme.onBackground
                ) // Larger icon
            }
            Text(
                text = screenTitle,
                fontSize = 40.sp,
                fontFamily = eczarFamily,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onBackground,
                modifier = Modifier.padding(start = 24.dp, top = 10.dp, bottom = 10.dp)
            )
            
            Box(
                modifier = Modifier
                    .fillMaxSize()
            ) {
                val view = LocalView.current
                val listState = rememberLazyListState()

                LaunchedEffect(listState.firstVisibleItemIndex) {
                    // Removed haptic feedback
                }

                // Very light fade effect at the top
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(16.dp)
                        .zIndex(1f)
                        .background(
                            brush = Brush.verticalGradient(
                                colors = listOf(
                                    MaterialTheme.colorScheme.background,
                                    MaterialTheme.colorScheme.background.copy(alpha = 0f)
                                )
                            )
                        )
                )

                // Very light fade effect at the bottom
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(16.dp)
                        .align(Alignment.BottomCenter)
                        .zIndex(1f)
                        .background(
                            brush = Brush.verticalGradient(
                                colors = listOf(
                                    MaterialTheme.colorScheme.background.copy(alpha = 0f),
                                    MaterialTheme.colorScheme.background
                                )
                            )
                        )
                )

                if (isLoading) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(
                            color = MaterialTheme.colorScheme.primary
                        )
                    }
                } else {
                    LazyColumn(
                        state = listState,
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(top = 20.dp, start = 24.dp, end = 24.dp), // Specified padding
                        contentPadding = PaddingValues(bottom = 70.dp) // 0dp padding inside the layout box
                    ) {
                        items(items = itemsList, key = { it.id }) { contentItem ->
                            Box(modifier = Modifier.animateItemPlacement()) {
                                ContentListItem(contentItem = contentItem, lazyListState = listState, navController = navController)
                            }
                        }
                    }
                }

                // Improved scroll indicator
                var isScrolling by remember { mutableStateOf(false) }
                val coroutineScope = rememberCoroutineScope()
                var scrollJob by remember { mutableStateOf<kotlinx.coroutines.Job?>(null) }

                // Detect scroll state changes
                LaunchedEffect(listState.isScrollInProgress) {
                    if (listState.isScrollInProgress) {
                        // User is actively scrolling
                        scrollJob?.cancel()
                        isScrolling = true
                    } else {
                        // Scrolling stopped, hide indicator after a short delay
                        scrollJob?.cancel()
                        scrollJob = coroutineScope.launch {
                            delay(150) // Brief delay before hiding
                            isScrolling = false
                        }
                    }
                }

                // Smooth animation for scroll indicator
                val indicatorAlpha by animateFloatAsState(
                    targetValue = if (isScrolling) 0.6f else 0f,
                    animationSpec = tween(durationMillis = 300, easing = LinearOutSlowInEasing),
                    label = "scroll_indicator_alpha"
                )

                // Show scroll indicator only when there are enough items and user is scrolling
                if (itemsList.size > 3) {
                    Box(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .padding(bottom = 100.dp)
                            .alpha(indicatorAlpha)
                    ) {
                        Box(
                            modifier = Modifier
                                .size(6.dp)
                                .background(
                                    MaterialTheme.colorScheme.primary.copy(alpha = 0.7f),
                                    CircleShape
                                )
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun ContentListItem(contentItem: ContentItem, lazyListState: LazyListState, navController: NavController) {
    Text(
        text = contentItem.content,
        fontSize = 20.sp,
        fontFamily = eczarFamily,
        color = MaterialTheme.colorScheme.onBackground,
        modifier = Modifier
            .fillMaxWidth()
            .clickable {
                // Check if this item has a titleId (from spiritual_reading collection)
                if (contentItem.titleId.isNotEmpty()) {
                    if (contentItem.titleId.startsWith("content_")) {
                        // This is final content, navigate to E-Reader
                        val encodedTitle = java.net.URLEncoder.encode(contentItem.titleId, "UTF-8")
                        navController.navigate("ereader/bhajan/$encodedTitle")
                    } else if (contentItem.titleId.startsWith("item_")) {
                        // This is a list item, navigate to next level
                        navController.navigate("content/SPIRITUAL_READING/${contentItem.titleId}")
                    } else {
                        // Fallback to E-Reader
                        val encodedTitle = java.net.URLEncoder.encode(contentItem.content, "UTF-8")
                        navController.navigate("ereader/bhajan/$encodedTitle")
                    }
                } else {
                    // Fallback to old navigation logic
                    when (contentItem.content) {
                        // Section level - navigate to categories
                        "गीत/ढाल संग्रह" -> navController.navigate("content/GEET_DHAAL")

                        // Category level - navigate to content within that category
                        "भिक्षु-स्तुति" -> navController.navigate("content/BHIKSHU")
                        "तुलसी स्तुति" -> navController.navigate("content/TULSI")
                        "जैन स्तोत्र" -> navController.navigate("content/JAIN_STOTRA")
                        "आध्यात्मिक गीत" -> navController.navigate("content/SPIRITUAL_SONGS")
                        "धार्मिक ढाल" -> navController.navigate("content/RELIGIOUS_DHAAL")

                        // Content level - navigate to Blinkist-style e-reader
                        else -> {
                            val encodedTitle = java.net.URLEncoder.encode(contentItem.content, "UTF-8")
                            navController.navigate("ereader/bhajan/$encodedTitle")
                        }
                    }
                }
            }
            .padding(vertical = 8.dp)
    )
}

private fun getStaticContentForCategory(category: String): List<String> {
    return when (category.uppercase()) {
        "BHIKSHU" -> listOf(
            "भिक्षु म्हारै प्रगट्या जी",
            "चैत्य पुरुष",
            "वंदना लो झेलो",
            "सिरियारी रो संत",
            "ओ म्हांरा गुरुदेव!",
            "घणा सुहावो माता",
            "स्वामीजी! थांरी साधना री",
            "रुं रूं में सांवरियो",
            "स्वामी भीखणजी रो नाम",
            "बादळियो आंखड़ल्यां में"
        )
        "TULSI" -> listOf(
            "तुलसी के दोहे",
            "हनुमान चालीसा",
            "राम नाम की महिमा",
            "सुंदरकांड",
            "राम रक्षा स्तोत्र"
        )
        "KALU" -> listOf(
            "कालू की कहानियां",
            "आध्यात्मिक कथाएं",
            "संत कालू के उपदेश",
            "जीवन दर्शन"
        )
        "MAHAPRAJNA" -> listOf(
            "प्रेक्षा ध्यान",
            "आत्म साधना",
            "जैन दर्शन",
            "अध्यात्म विज्ञान"
        )
        "GEET" -> listOf(
            "आध्यात्मिक गीत",
            "भक्ति संगीत",
            "धार्मिक गीत",
            "भजन संग्रह"
        )
        "MANGAL" -> listOf(
            "मंगल आरती",
            "शुभ मंगल",
            "कल्याणकारी मंत्र",
            "मंगल गीत"
        )
        "READ_SCREEN" -> listOf(
            "गीत/ढाल संग्रह",
            "भक्तामर स्तोत्र",
            "पच्चीस बोल",
            "अर्हत वंदना"
        )
        "DAINIK_SWADHYAY" -> listOf(
            "गीत/ढाल संग्रह",
            "भक्तामर स्तोत्र",
            "पच्चीस बोल",
            "अर्हत वंदना",
            "तुलसी के दोहे",
            "जैन स्तोत्र",
            "आध्यात्मिक गीत",
            "प्रातःकालीन पाठ",
            "संध्या आरती",
            "मंगल गीत"
        )
        "GEET_DHAAL" -> listOf(
            "भिक्षु-स्तुति",
            "तुलसी स्तुति",
            "जैन स्तोत्र",
            "आध्यात्मिक गीत",
            "धार्मिक ढाल"
        )
        "ADHYAYAN_SMARAN" -> listOf(
            "आचार्य तुलसी के उपदेश",
            "जैन दर्शन के मूल सिद्धांत",
            "अहिंसा और करुणा",
            "आत्मा की शुद्धता",
            "कर्म सिद्धांत",
            "मोक्ष मार्ग",
            "ध्यान और साधना",
            "सत्य और अहिंसा",
            "जीवन में धर्म",
            "आध्यात्मिक विकास",
            "संयम और नियम",
            "दया और क्षमा",
            "ज्ञान और विवेक",
            "शांति और प्रेम",
            "आचार्य भिक्षु के संदेश"
        )
        else -> listOf("सामग्री उपलब्ध नहीं")
    }
}

private suspend fun loadStructuredContent(
    db: FirebaseFirestore,
    category: String,
    onResult: (List<ContentItem>) -> Unit
) {
    try {
        // First try new spiritual_reading collection
        when (category.uppercase()) {
            "DAINIK_SWADHYAY" -> {
                // Load root level items from spiritual_reading collection
                val spiritualReadingResult = db.collection("spiritual_reading")
                    .whereEqualTo("parentId", null) // Root level items
                    .orderBy("order")
                    .get()
                    .await()

                if (!spiritualReadingResult.isEmpty) {
                    val items = spiritualReadingResult.documents.mapIndexed { index, doc ->
                        ContentItem(
                            id = index,
                            content = doc.getString("title") ?: "Unknown Title",
                            titleId = doc.id
                        )
                    }
                    onResult(items)
                    return
                }

                // Fallback to old sections collection
                val sectionsResult = db.collection("sections")
                    .whereEqualTo("CardID", 1)
                    .get()
                    .await()

                if (!sectionsResult.isEmpty) {
                    val items = sectionsResult.documents.mapIndexed { index, doc ->
                        ContentItem(
                            id = index,
                            content = doc.getString("SectionTitle") ?: "Unknown Section"
                        )
                    }
                    onResult(items)
                    return
                }
            }

            "GEET_DHAAL" -> {
                // Load categories for SectionID 1 (गीत/ढाल संग्रह)
                val categoriesResult = db.collection("categories")
                    .whereEqualTo("SectionID", 1)
                    .get()
                    .await()

                if (!categoriesResult.isEmpty) {
                    val items = categoriesResult.documents.mapIndexed { index, doc ->
                        ContentItem(
                            id = index,
                            content = doc.getString("CategoryName") ?: "Unknown Category"
                        )
                    }
                    onResult(items)
                    return
                }
            }

            else -> {
                // Check if this is a dynamic spiritual_reading category
                if (category.startsWith("SPIRITUAL_READING_")) {
                    val parentId = category.removePrefix("SPIRITUAL_READING_")

                    // Load children of this parent from spiritual_reading collection
                    val childrenResult = db.collection("spiritual_reading")
                        .whereEqualTo("parentId", parentId)
                        .orderBy("order")
                        .get()
                        .await()

                    if (!childrenResult.isEmpty) {
                        val items = childrenResult.documents.mapIndexed { index, doc ->
                            val contentType = doc.getString("contentType") ?: "list"
                            val content = doc.getString("content")

                            ContentItem(
                                id = index,
                                content = if (contentType == "content" && !content.isNullOrEmpty()) {
                                    // This is final content, show title
                                    doc.getString("title") ?: "Unknown Title"
                                } else {
                                    // This is a list, show title
                                    doc.getString("title") ?: "Unknown Title"
                                },
                                titleId = if (contentType == "content") "content_${doc.id}" else doc.id
                            )
                        }
                        onResult(items)
                        return
                    }
                }
            }

            "BHIKSHU" -> {
                // Load content for CategoryName "भिक्षु-स्तुति"
                val contentResult = db.collection("content")
                    .whereEqualTo("CategoryName", "भिक्षु-स्तुति")
                    .get()
                    .await()

                if (!contentResult.isEmpty) {
                    val items = contentResult.documents.mapIndexed { index, doc ->
                        ContentItem(
                            id = index,
                            content = doc.getString("TitleName") ?: "Unknown Title",
                            titleId = "content_${doc.getLong("TitleID") ?: index}"
                        )
                    }
                    onResult(items)
                    return
                }
            }

            else -> {
                // Try to load from content collection for other categories
                val contentResult = db.collection("content")
                    .whereEqualTo("CategoryName", when(category.uppercase()) {
                        "TULSI" -> "तुलसी"
                        "KALU" -> "कालू"
                        "MAHAPRAJNA" -> "महाप्रज्ञा"
                        "GEET" -> "गीत"
                        "MANGAL" -> "मंगल"
                        "READ_SCREEN" -> "दैनिक स्वाध्याय"
                        "ADHYAYAN_SMARAN" -> "अध्ययन व स्मरण"
                        else -> ""
                    })
                    .get()
                    .await()

                if (!contentResult.isEmpty) {
                    val items = contentResult.documents.mapIndexed { index, doc ->
                        ContentItem(
                            id = index,
                            content = doc.getString("TitleName") ?: "Unknown Title"
                        )
                    }
                    onResult(items)
                    return
                }
            }
        }

        // Try new spiritual_reading collection format
        val spiritualReadingResult = db.collection("spiritual_reading")
            .whereEqualTo("metadata.category", "DAINIK_SWADHYAY")
            .whereEqualTo("parentId", when(category.uppercase()) {
                "DAINIK_SWADHYAY" -> null // Root level
                else -> "readcard_root" // Default parent
            })
            .orderBy("order")
            .get()
            .await()

        if (!spiritualReadingResult.isEmpty) {
            val items = spiritualReadingResult.documents.mapIndexed { index, doc ->
                ContentItem(
                    id = index,
                    content = doc.getString("title") ?: "Unknown Title",
                    titleId = doc.id
                )
            }
            onResult(items)
            return
        }

        // Fallback to old format if structured data not found
        val oldFormatResult = db.collection("spiritual_content")
            .whereEqualTo("category", category.uppercase())
            .get()
            .await()

        if (!oldFormatResult.isEmpty) {
            val items = oldFormatResult.documents.mapIndexed { index, doc ->
                ContentItem(
                    id = index,
                    content = doc.getString("title") ?: "Unknown Title"
                )
            }
            onResult(items)
        } else {
            // Final fallback to static content
            val staticTexts = getStaticContentForCategory(category)
            val items = staticTexts.mapIndexed { index, content ->
                ContentItem(id = index, content = content)
            }
            onResult(items)
        }

    } catch (e: Exception) {
        // Error fallback to static content
        val staticTexts = getStaticContentForCategory(category)
        val items = staticTexts.mapIndexed { index, content ->
            ContentItem(id = index, content = content)
        }
        onResult(items)
    }
}
