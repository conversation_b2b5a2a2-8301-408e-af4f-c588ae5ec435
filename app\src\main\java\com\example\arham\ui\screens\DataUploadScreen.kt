package com.example.arham.ui.screens

import android.content.Context
import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.CloudUpload
import androidx.compose.material.icons.filled.FileOpen
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.FieldValue
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import org.json.JSONArray
import org.json.JSONObject
import java.io.BufferedReader
import java.io.InputStreamReader

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DataUploadScreen(
    navController: NavController,
    isDarkMode: Boolean = false
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val db = FirebaseFirestore.getInstance()
    
    var selectedFileUri by remember { mutableStateOf<Uri?>(null) }
    var fileName by remember { mutableStateOf("") }
    var collectionName by remember { mutableStateOf("spiritual_content") }
    var isUploading by remember { mutableStateOf(false) }
    var uploadProgress by remember { mutableStateOf(0f) }
    var uploadStatus by remember { mutableStateOf("") }
    var uploadLog by remember { mutableStateOf("") }
    
    // File picker launcher
    val filePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri ->
        selectedFileUri = uri
        fileName = uri?.let { getFileName(context, it) } ?: ""
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState())
    ) {
        // Top Bar
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = { navController.popBackStack() }) {
                Icon(Icons.Default.ArrowBack, contentDescription = "Back")
            }
            Text(
                text = "Upload JSON to Firestore",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(start = 8.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Collection Name Input
        OutlinedTextField(
            value = collectionName,
            onValueChange = { collectionName = it },
            label = { Text("Collection Name") },
            modifier = Modifier.fillMaxWidth(),
            enabled = !isUploading
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // File Selection
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Icon(
                    Icons.Default.FileOpen,
                    contentDescription = "Select File",
                    modifier = Modifier.size(48.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                if (fileName.isNotEmpty()) {
                    Text(
                        text = "Selected: $fileName",
                        fontWeight = FontWeight.Medium
                    )
                } else {
                    Text("No file selected")
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Button(
                    onClick = { filePickerLauncher.launch("application/json") },
                    enabled = !isUploading
                ) {
                    Icon(Icons.Default.FileOpen, contentDescription = null)
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("Select JSON File")
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Test Upload Button
        Button(
            onClick = {
                scope.launch {
                    uploadTestDocument(
                        collectionName = collectionName,
                        db = db,
                        onProgress = { progress -> uploadProgress = progress },
                        onStatusUpdate = { status -> uploadStatus = status },
                        onLogUpdate = { log -> uploadLog += "$log\n" },
                        onUploadStateChange = { uploading -> isUploading = uploading }
                    )
                }
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = !isUploading && collectionName.isNotBlank(),
            colors = ButtonDefaults.buttonColors(
                containerColor = MaterialTheme.colorScheme.secondary
            )
        ) {
            Icon(Icons.Default.CloudUpload, contentDescription = null)
            Spacer(modifier = Modifier.width(8.dp))
            Text("Test Upload (Simple Document)")
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Upload Button
        Button(
            onClick = {
                selectedFileUri?.let { uri ->
                    scope.launch {
                        uploadJsonToFirestore(
                            context = context,
                            uri = uri,
                            collectionName = collectionName,
                            db = db,
                            onProgress = { progress -> uploadProgress = progress },
                            onStatusUpdate = { status -> uploadStatus = status },
                            onLogUpdate = { log -> uploadLog += "$log\n" },
                            onUploadStateChange = { uploading -> isUploading = uploading }
                        )
                    }
                }
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = selectedFileUri != null && !isUploading && collectionName.isNotBlank()
        ) {
            if (isUploading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    strokeWidth = 2.dp
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("Uploading...")
            } else {
                Icon(Icons.Default.CloudUpload, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("Upload JSON File")
            }
        }
        
        // Progress Bar
        if (isUploading) {
            Spacer(modifier = Modifier.height(16.dp))
            LinearProgressIndicator(
                progress = uploadProgress,
                modifier = Modifier.fillMaxWidth()
            )
            Text(
                text = "${(uploadProgress * 100).toInt()}% - $uploadStatus",
                modifier = Modifier.padding(top = 8.dp)
            )
        }
        
        // Upload Log
        if (uploadLog.isNotEmpty()) {
            Spacer(modifier = Modifier.height(16.dp))
            Card(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Upload Log",
                        fontWeight = FontWeight.Bold,
                        fontSize = 16.sp
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = uploadLog,
                        fontSize = 12.sp,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }
    }
}

private fun getFileName(context: Context, uri: Uri): String {
    var fileName = "unknown_file.json"
    context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
        val nameIndex = cursor.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
        if (cursor.moveToFirst() && nameIndex >= 0) {
            fileName = cursor.getString(nameIndex)
        }
    }
    return fileName
}

private suspend fun uploadJsonToFirestore(
    context: Context,
    uri: Uri,
    collectionName: String,
    db: FirebaseFirestore,
    onProgress: (Float) -> Unit,
    onStatusUpdate: (String) -> Unit,
    onLogUpdate: (String) -> Unit,
    onUploadStateChange: (Boolean) -> Unit
) {
    try {
        onUploadStateChange(true)
        onStatusUpdate("Reading JSON file...")
        onLogUpdate("📖 Reading JSON file...")
        
        // Read JSON file
        val jsonString = context.contentResolver.openInputStream(uri)?.use { inputStream ->
            BufferedReader(InputStreamReader(inputStream)).use { reader ->
                reader.readText()
            }
        } ?: throw Exception("Could not read file")
        
        onLogUpdate("✅ File read successfully")
        onStatusUpdate("Parsing JSON...")
        
        // Parse JSON
        val jsonData = try {
            JSONObject(jsonString)
        } catch (e: Exception) {
            // Try as array
            JSONArray(jsonString)
        }
        
        onLogUpdate("✅ JSON parsed successfully")
        onStatusUpdate("Validating data...")

        // Validate and sanitize the data
        val sanitizedData = when (jsonData) {
            is JSONObject -> {
                onLogUpdate("📋 Validating single document...")
                validateAndSanitizeObject(jsonData, onLogUpdate)
            }
            is JSONArray -> {
                onLogUpdate("📋 Validating ${jsonData.length()} documents...")
                validateAndSanitizeArray(jsonData, onLogUpdate)
            }
            else -> throw Exception("Invalid JSON format")
        }

        onLogUpdate("✅ Data validation completed")
        onStatusUpdate("Uploading to Firestore...")

        when (sanitizedData) {
            is JSONObject -> {
                uploadSingleDocument(sanitizedData, collectionName, db, onLogUpdate)
                onProgress(1f)
            }
            is JSONArray -> {
                uploadMultipleDocuments(sanitizedData, collectionName, db, onProgress, onLogUpdate)
            }
        }
        
        onStatusUpdate("Upload completed!")
        onLogUpdate("🎉 Upload completed successfully!")
        
    } catch (e: Exception) {
        onLogUpdate("❌ Error: ${e.message}")
        onLogUpdate("❌ Error Type: ${e.javaClass.simpleName}")
        onLogUpdate("❌ Stack Trace: ${e.stackTrace.take(3).joinToString("\n") { it.toString() }}")

        // More specific error handling
        when {
            e.message?.contains("INVALID_ARGUMENT") == true -> {
                onLogUpdate("💡 This is likely a Firestore validation error")
                onLogUpdate("💡 Check your JSON for invalid field names or values")
            }
            e.message?.contains("numeric") == true -> {
                onLogUpdate("💡 Numeric value issue detected")
                onLogUpdate("💡 Try converting large numbers to strings in your JSON")
            }
            e.message?.contains("permission") == true -> {
                onLogUpdate("💡 Permission denied - check Firestore security rules")
            }
            else -> {
                onLogUpdate("💡 Unknown error - check the full error message above")
            }
        }

        onStatusUpdate("Upload failed - check logs")
    } finally {
        onUploadStateChange(false)
    }
}

private suspend fun uploadSingleDocument(
    jsonObject: JSONObject,
    collectionName: String,
    db: FirebaseFirestore,
    onLogUpdate: (String) -> Unit
) {
    try {
        onLogUpdate("🔄 Processing single document...")

        val docData = jsonObjectToMap(jsonObject)
        val docId = generateDocId(docData["title"]?.toString() ?: "doc_${System.currentTimeMillis()}")

        onLogUpdate("📝 Generated document ID: $docId")

        docData["id"] = docId
        docData["uploadedAt"] = FieldValue.serverTimestamp()

        // Convert tags string to array if needed
        docData["tags"]?.let { tags ->
            if (tags is String) {
                docData["tags"] = tags.split(",").map { it.trim() }
                onLogUpdate("🏷️ Converted tags string to array")
            }
        }

        // Log the data being uploaded (first 200 chars)
        val dataPreview = docData.toString().take(200)
        onLogUpdate("📋 Data preview: $dataPreview...")

        // Validate each field before upload
        docData.forEach { (key, value) ->
            when (value) {
                is Number -> onLogUpdate("🔢 Field '$key': ${value.javaClass.simpleName} = $value")
                is String -> onLogUpdate("📝 Field '$key': String (${value.length} chars)")
                is List<*> -> onLogUpdate("📋 Field '$key': List (${value.size} items)")
                is Map<*, *> -> onLogUpdate("🗂️ Field '$key': Map (${value.size} entries)")
                else -> onLogUpdate("❓ Field '$key': ${value.javaClass.simpleName}")
            }
        }

        onLogUpdate("🚀 Attempting Firestore upload...")
        db.collection(collectionName).document(docId).set(docData).await()
        onLogUpdate("✅ Successfully uploaded: ${docData["title"]}")

    } catch (e: Exception) {
        onLogUpdate("❌ Error in uploadSingleDocument: ${e.message}")
        onLogUpdate("❌ Error details: ${e.javaClass.simpleName}")
        throw e // Re-throw to be caught by main handler
    }
}

private suspend fun uploadMultipleDocuments(
    jsonArray: JSONArray,
    collectionName: String,
    db: FirebaseFirestore,
    onProgress: (Float) -> Unit,
    onLogUpdate: (String) -> Unit
) {
    val totalDocs = jsonArray.length()
    val batchSize = 500
    var uploadedCount = 0
    
    for (i in 0 until totalDocs step batchSize) {
        val batch = db.batch()
        val endIndex = minOf(i + batchSize, totalDocs)
        
        for (j in i until endIndex) {
            val jsonObject = jsonArray.getJSONObject(j)
            val docData = jsonObjectToMap(jsonObject)
            val docId = generateDocId(docData["title"]?.toString() ?: "doc_${System.currentTimeMillis()}_$j")
            
            docData["id"] = docId
            docData["uploadedAt"] = FieldValue.serverTimestamp()
            
            // Convert tags string to array if needed
            docData["tags"]?.let { tags ->
                if (tags is String) {
                    docData["tags"] = tags.split(",").map { it.trim() }
                }
            }
            
            val docRef = db.collection(collectionName).document(docId)
            batch.set(docRef, docData)
        }
        
        batch.commit().await()
        uploadedCount += (endIndex - i)
        
        val progress = uploadedCount.toFloat() / totalDocs
        onProgress(progress)
        onLogUpdate("📦 Uploaded batch: $uploadedCount/$totalDocs documents")
    }
}

private fun jsonObjectToMap(jsonObject: JSONObject): MutableMap<String, Any> {
    val map = mutableMapOf<String, Any>()
    val keys = jsonObject.keys()
    while (keys.hasNext()) {
        val key = keys.next()
        val value = jsonObject.get(key)

        // Handle different value types and sanitize problematic values
        val sanitizedValue = when (value) {
            is Number -> sanitizeNumber(value)
            is String -> value
            is Boolean -> value
            is JSONObject -> jsonObjectToMap(value) // Recursive for nested objects
            is JSONArray -> jsonArrayToList(value)
            else -> value.toString()
        }

        map[key] = sanitizedValue
    }
    return map
}

private fun jsonArrayToList(jsonArray: JSONArray): List<Any> {
    val list = mutableListOf<Any>()
    for (i in 0 until jsonArray.length()) {
        val value = jsonArray.get(i)
        val sanitizedValue = when (value) {
            is Number -> sanitizeNumber(value)
            is String -> value
            is Boolean -> value
            is JSONObject -> jsonObjectToMap(value)
            is JSONArray -> jsonArrayToList(value)
            else -> value.toString()
        }
        list.add(sanitizedValue)
    }
    return list
}

private fun sanitizeNumber(number: Number): Any {
    return when {
        // Check for NaN or Infinity
        number is Double && (number.isNaN() || number.isInfinite()) -> {
            number.toString() // Convert to string
        }
        number is Float && (number.isNaN() || number.isInfinite()) -> {
            number.toString() // Convert to string
        }
        // Check for very large numbers that might cause issues
        number is Long && (number > 9223372036854775000L || number < -9223372036854775000L) -> {
            number.toString() // Convert to string
        }
        number is Int -> number.toLong() // Convert Int to Long for consistency
        number is Double -> {
            // Check if it's actually an integer value
            if (number % 1.0 == 0.0 && number >= Long.MIN_VALUE && number <= Long.MAX_VALUE) {
                number.toLong()
            } else {
                number
            }
        }
        number is Float -> {
            // Check if it's actually an integer value
            if (number % 1.0f == 0.0f && number >= Long.MIN_VALUE && number <= Long.MAX_VALUE) {
                number.toLong()
            } else {
                number.toDouble()
            }
        }
        else -> number
    }
}

private fun validateAndSanitizeObject(jsonObject: JSONObject, onLogUpdate: (String) -> Unit): JSONObject {
    val sanitizedObject = JSONObject()
    val keys = jsonObject.keys()

    while (keys.hasNext()) {
        val key = keys.next()
        try {
            val value = jsonObject.get(key)
            val sanitizedValue = sanitizeValue(value, key, onLogUpdate)
            sanitizedObject.put(key, sanitizedValue)
        } catch (e: Exception) {
            onLogUpdate("⚠️ Warning: Skipping field '$key' due to error: ${e.message}")
        }
    }

    return sanitizedObject
}

private fun validateAndSanitizeArray(jsonArray: JSONArray, onLogUpdate: (String) -> Unit): JSONArray {
    val sanitizedArray = JSONArray()

    for (i in 0 until jsonArray.length()) {
        try {
            val item = jsonArray.get(i)
            if (item is JSONObject) {
                val sanitizedObject = validateAndSanitizeObject(item, onLogUpdate)
                sanitizedArray.put(sanitizedObject)
            } else {
                val sanitizedValue = sanitizeValue(item, "array_item_$i", onLogUpdate)
                sanitizedArray.put(sanitizedValue)
            }
        } catch (e: Exception) {
            onLogUpdate("⚠️ Warning: Skipping array item $i due to error: ${e.message}")
        }
    }

    return sanitizedArray
}

private fun sanitizeValue(value: Any, fieldName: String, onLogUpdate: (String) -> Unit): Any {
    return when (value) {
        is Number -> {
            val sanitized = sanitizeNumber(value)
            if (sanitized != value) {
                onLogUpdate("🔧 Sanitized numeric value in field '$fieldName': $value → $sanitized")
            }
            sanitized
        }
        is String -> {
            // Check for very long strings that might cause issues
            if (value.length > 1048487) { // Firestore limit is ~1MB per field
                onLogUpdate("⚠️ Warning: Truncating long string in field '$fieldName' (${value.length} chars)")
                value.take(1048487)
            } else {
                value
            }
        }
        is Boolean -> value
        is JSONObject -> validateAndSanitizeObject(value, onLogUpdate)
        is JSONArray -> validateAndSanitizeArray(value, onLogUpdate)
        else -> value.toString()
    }
}

private suspend fun uploadTestDocument(
    collectionName: String,
    db: FirebaseFirestore,
    onProgress: (Float) -> Unit,
    onStatusUpdate: (String) -> Unit,
    onLogUpdate: (String) -> Unit,
    onUploadStateChange: (Boolean) -> Unit
) {
    try {
        onUploadStateChange(true)
        onStatusUpdate("Testing Firestore connection...")
        onLogUpdate("🧪 Starting test upload...")

        // Create a simple test document with safe values
        val testData = mapOf(
            "id" to "test_${System.currentTimeMillis()}",
            "title" to "Test Document",
            "content" to "This is a test document to verify Firestore connectivity.",
            "author" to "System Test",
            "category" to "test",
            "language" to "English",
            "tags" to listOf("test", "system"),
            "uploadedAt" to FieldValue.serverTimestamp(),
            "testNumber" to 42L,
            "testBoolean" to true
        )

        onLogUpdate("📋 Test document created with safe values")
        onLogUpdate("🚀 Uploading test document...")

        val docId = "test_${System.currentTimeMillis()}"
        db.collection(collectionName).document(docId).set(testData).await()

        onProgress(1f)
        onLogUpdate("✅ Test upload successful!")
        onLogUpdate("📄 Document ID: $docId")
        onStatusUpdate("Test completed successfully")

    } catch (e: Exception) {
        onLogUpdate("❌ Test upload failed: ${e.message}")
        onLogUpdate("❌ Error type: ${e.javaClass.simpleName}")
        onStatusUpdate("Test failed")

        // Specific error analysis
        when {
            e.message?.contains("PERMISSION_DENIED") == true -> {
                onLogUpdate("💡 Permission denied - check Firestore security rules")
                onLogUpdate("💡 Make sure your rules allow writes to this collection")
            }
            e.message?.contains("INVALID_ARGUMENT") == true -> {
                onLogUpdate("💡 Invalid argument - there might be an issue with field names or values")
            }
            e.message?.contains("UNAUTHENTICATED") == true -> {
                onLogUpdate("💡 Authentication required - make sure you're signed in")
            }
            else -> {
                onLogUpdate("💡 Check your internet connection and Firebase configuration")
            }
        }
    } finally {
        onUploadStateChange(false)
    }
}

private fun generateDocId(title: String): String {
    return title
        .lowercase()
        .replace(Regex("[^\\w\\s]"), "") // Remove special characters
        .replace(Regex("\\s+"), "_")     // Replace spaces with underscores
        .take(50)                       // Limit length
}
