package com.example.arham.data;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\n\u0018\u0000 J2\u00020\u0001:\u0001JB\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001e\u0010$\u001a\u00020\n2\u0006\u0010%\u001a\u00020&2\u0006\u0010\'\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010(J\u001e\u0010)\u001a\b\u0012\u0004\u0012\u00020&0\u00052\u0006\u0010*\u001a\u00020&2\u0006\u0010+\u001a\u00020&H\u0002J\u0010\u0010,\u001a\u00020&2\u0006\u0010*\u001a\u00020&H\u0002J\u0018\u0010-\u001a\u0004\u0018\u00010\u00062\u0006\u0010.\u001a\u00020&H\u0086@\u00a2\u0006\u0002\u0010/J\u0010\u00100\u001a\u00020&2\u0006\u0010*\u001a\u00020&H\u0002J\u0010\u00101\u001a\u00020&2\u0006\u0010*\u001a\u00020&H\u0002J\u0010\u00102\u001a\u00020&2\u0006\u0010*\u001a\u00020&H\u0002J\u0010\u00103\u001a\u00020&2\u0006\u0010*\u001a\u00020&H\u0002J\u0010\u00104\u001a\u00020&2\u0006\u0010*\u001a\u00020&H\u0002J\u001c\u00105\u001a\b\u0012\u0004\u0012\u0002060\u00052\u0006\u0010%\u001a\u00020&H\u0086@\u00a2\u0006\u0002\u0010/J\u000e\u00107\u001a\u000208H\u0086@\u00a2\u0006\u0002\u00109J\u001e\u0010:\u001a\u00020\n2\u0006\u0010%\u001a\u00020&2\u0006\u0010;\u001a\u00020&H\u0086@\u00a2\u0006\u0002\u0010<J\u000e\u0010=\u001a\u000208H\u0082@\u00a2\u0006\u0002\u00109J\b\u0010>\u001a\u000208H\u0002J\u001e\u0010?\u001a\u00020\n2\u0006\u0010%\u001a\u00020&2\u0006\u0010;\u001a\u00020&H\u0086@\u00a2\u0006\u0002\u0010<J\u001c\u0010@\u001a\b\u0012\u0004\u0012\u00020A0\u00052\u0006\u0010B\u001a\u00020&H\u0086@\u00a2\u0006\u0002\u0010/J\u000e\u0010C\u001a\u000208H\u0082@\u00a2\u0006\u0002\u00109J\u000e\u0010D\u001a\u000208H\u0082@\u00a2\u0006\u0002\u00109J\u000e\u0010E\u001a\u000208H\u0082@\u00a2\u0006\u0002\u00109J\u000e\u0010F\u001a\u000208H\u0082@\u00a2\u0006\u0002\u00109J\u000e\u0010G\u001a\u000208H\u0082@\u00a2\u0006\u0002\u00109J\u000e\u0010H\u001a\u000208H\u0082@\u00a2\u0006\u0002\u00109J\u000e\u0010I\u001a\u000208H\u0082@\u00a2\u0006\u0002\u00109R\u001a\u0010\u0003\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\n0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\n0\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u001d\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0013R\u001d\u0010\u0016\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0013R\u0017\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\n0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0013R\u0017\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\n0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0013R\u001d\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0013R\u001d\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0013R\u001d\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u0013R\u000e\u0010 \u001a\u00020!X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\"\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u0013\u00a8\u0006K"}, d2 = {"Lcom/example/arham/data/DatabaseManager;", "", "()V", "_allContent", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "Lcom/example/arham/data/models/SpiritualContent;", "_bhikshuContent", "_geetContent", "_isInitialized", "", "_isLoading", "_kaluContent", "_mahaprajnaContent", "_mangalContent", "_tulsiContent", "allContent", "Lkotlinx/coroutines/flow/StateFlow;", "getAllContent", "()Lkotlinx/coroutines/flow/StateFlow;", "bhikshuContent", "getBhikshuContent", "geetContent", "getGeetContent", "isInitialized", "isLoading", "kaluContent", "getKaluContent", "mahaprajnaContent", "getMahaprajnaContent", "mangalContent", "getMangalContent", "repository", "Lcom/example/arham/data/repository/FirestoreRepository;", "tulsiContent", "getTulsiContent", "addBookmark", "userId", "", "content", "(Ljava/lang/String;Lcom/example/arham/data/models/SpiritualContent;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateSearchKeywords", "title", "author", "getBhajanFullContent", "getContentById", "id", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getGeetFullContent", "getKaluFullContent", "getMahaprajnaFullContent", "getMangalFullContent", "getTulsiFullContent", "getUserBookmarks", "Lcom/example/arham/data/models/UserBookmark;", "initializeDatabase", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isBookmarked", "contentId", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "loadAllContent", "loadFallbackContent", "removeBookmark", "searchContent", "Lcom/example/arham/data/repository/SearchResult;", "query", "seedBhikshuContent", "seedDatabase", "seedGeetContent", "seedKaluContent", "seedMahaprajnaContent", "seedMangalContent", "seedTulsiContent", "Companion", "app_debug"})
public final class DatabaseManager {
    @org.jetbrains.annotations.NotNull()
    private final com.example.arham.data.repository.FirestoreRepository repository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> _bhikshuContent = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> bhikshuContent = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> _tulsiContent = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> tulsiContent = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> _kaluContent = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> kaluContent = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> _mahaprajnaContent = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> mahaprajnaContent = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> _geetContent = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> geetContent = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> _mangalContent = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> mangalContent = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> _allContent = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> allContent = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isInitialized = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isInitialized = null;
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.arham.data.DatabaseManager INSTANCE;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.arham.data.DatabaseManager.Companion Companion = null;
    
    private DatabaseManager() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> getBhikshuContent() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> getTulsiContent() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> getKaluContent() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> getMahaprajnaContent() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> getGeetContent() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> getMangalContent() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.arham.data.models.SpiritualContent>> getAllContent() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isInitialized() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object initializeDatabase(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object seedDatabase(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object seedBhikshuContent(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object seedTulsiContent(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object seedKaluContent(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object seedMahaprajnaContent(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object seedGeetContent(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object seedMangalContent(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final java.lang.Object loadAllContent(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final void loadFallbackContent() {
    }
    
    private final java.util.List<java.lang.String> generateSearchKeywords(java.lang.String title, java.lang.String author) {
        return null;
    }
    
    private final java.lang.String getBhajanFullContent(java.lang.String title) {
        return null;
    }
    
    private final java.lang.String getTulsiFullContent(java.lang.String title) {
        return null;
    }
    
    private final java.lang.String getKaluFullContent(java.lang.String title) {
        return null;
    }
    
    private final java.lang.String getMahaprajnaFullContent(java.lang.String title) {
        return null;
    }
    
    private final java.lang.String getGeetFullContent(java.lang.String title) {
        return null;
    }
    
    private final java.lang.String getMangalFullContent(java.lang.String title) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object searchContent(@org.jetbrains.annotations.NotNull()
    java.lang.String query, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.arham.data.repository.SearchResult>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getContentById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.arham.data.models.SpiritualContent> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object addBookmark(@org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    com.example.arham.data.models.SpiritualContent content, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object removeBookmark(@org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    java.lang.String contentId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getUserBookmarks(@org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.arham.data.models.UserBookmark>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object isBookmarked(@org.jetbrains.annotations.NotNull()
    java.lang.String userId, @org.jetbrains.annotations.NotNull()
    java.lang.String contentId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\u0005\u001a\u00020\u0004R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/example/arham/data/DatabaseManager$Companion;", "", "()V", "INSTANCE", "Lcom/example/arham/data/DatabaseManager;", "getInstance", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.arham.data.DatabaseManager getInstance() {
            return null;
        }
    }
}