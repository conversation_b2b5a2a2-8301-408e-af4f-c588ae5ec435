@echo off
echo 🔄 ArhamApp Data Upload Setup
echo ================================

echo.
echo Step 1: Installing Python dependencies...
pip install pandas openpyxl

echo.
echo Step 2: Installing Node.js dependencies...
npm install xlsx uuid firebase-admin

echo.
echo ✅ Setup completed!
echo.
echo 📋 Next steps:
echo 1. Place your Excel file in this directory
echo 2. Run: python excel_to_json_converter.py your_file.xlsx
echo 3. Download Firebase service account key
echo 4. Run: node upload_to_firestore.js output.json collection_name
echo.
pause
