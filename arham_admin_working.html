<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArhamApp Admin - Working Version</title>
    
    <!-- SheetJS for Excel file handling -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: #f8f9fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .tabs {
            display: flex;
            background: white;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .tab {
            flex: 1;
            padding: 15px 20px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
        }
        
        .tab.active {
            background: #667eea;
            color: white;
        }
        
        .tab:hover {
            background: #5a6fd8;
            color: white;
        }
        
        .tab-content {
            display: none;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background: #e0a800;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 6px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.success {
            background: #28a745;
        }
        
        .notification.error {
            background: #dc3545;
        }
        
        .notification.info {
            background: #17a2b8;
        }
        
        .notification.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        .data-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e1e5e9;
            border-radius: 6px;
            padding: 10px;
        }
        
        .data-item {
            padding: 10px;
            border-bottom: 1px solid #e1e5e9;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .data-item:last-child {
            border-bottom: none;
        }
        
        .data-item:hover {
            background: #f8f9fa;
        }
        
        .item-actions {
            display: flex;
            gap: 5px;
        }
        
        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }
        
        .firebase-config {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #dc3545;
        }
        
        .status-indicator.connected {
            background: #28a745;
        }
        
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .grid-2 {
                grid-template-columns: 1fr;
            }
            
            .tabs {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕉️ ArhamApp Admin Panel</h1>
            <p>Manage your spiritual content database</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showTab('firebase')">🔥 Firebase Setup</button>
            <button class="tab" onclick="showTab('content')">📝 Add Content</button>
            <button class="tab" onclick="showTab('data')">📊 View Data</button>
            <button class="tab" onclick="showTab('excel')">📤 Excel Import/Export</button>
        </div>

        <!-- Firebase Setup Tab -->
        <div id="firebase" class="tab-content active">
            <h2>🔥 Firebase Configuration</h2>
            
            <div class="firebase-config">
                <div class="connection-status">
                    <div class="status-indicator" id="statusIndicator"></div>
                    <span id="connectionStatus">Not Connected</span>
                </div>
                
                <div class="form-group">
                    <label for="apiKey">Firebase API Key:</label>
                    <input type="text" id="apiKey" class="form-input" placeholder="Enter your Firebase API Key">
                </div>
                
                <div class="form-group">
                    <label for="projectId">Project ID:</label>
                    <input type="text" id="projectId" class="form-input" placeholder="Enter your Project ID">
                </div>
                
                <button class="btn btn-primary" onclick="connectFirebase()">🔗 Connect to Firebase</button>
                <button class="btn btn-warning" onclick="testConnection()">🧪 Test Connection</button>
                <button class="btn btn-danger" onclick="clearConfig()">🗑️ Clear Config</button>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalItems">0</div>
                    <div class="stat-label">Total Items</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="lastSync">Never</div>
                    <div class="stat-label">Last Sync</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="syncStatus">❌</div>
                    <div class="stat-label">Sync Status</div>
                </div>
            </div>
        </div>

        <!-- Add Content Tab -->
        <div id="content" class="tab-content">
            <h2>📝 Add New Content</h2>
            
            <div class="grid-2">
                <div>
                    <div class="form-group">
                        <label for="itemTitle">Title (Hindi/English):</label>
                        <input type="text" id="itemTitle" class="form-input" placeholder="भिक्षु म्हारै प्रगट्या जी">
                    </div>
                    
                    <div class="form-group">
                        <label for="itemType">Content Type:</label>
                        <select id="itemType" class="form-select">
                            <option value="list">📋 List (Has Children)</option>
                            <option value="content">📄 Content (Final Item)</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="parentSelect">Parent Item:</label>
                        <select id="parentSelect" class="form-select">
                            <option value="">🏠 Root Level</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="itemAuthor">Author (Optional):</label>
                        <input type="text" id="itemAuthor" class="form-input" placeholder="आचार्य तुलसी">
                    </div>
                    
                    <div class="form-group">
                        <label for="itemKeywords">Keywords (Comma separated):</label>
                        <input type="text" id="itemKeywords" class="form-input" placeholder="भिक्षु, स्तुति, प्रार्थना">
                    </div>
                </div>
                
                <div>
                    <div class="form-group">
                        <label for="itemContent">Content (For final items only):</label>
                        <textarea id="itemContent" class="form-textarea" placeholder="Enter your spiritual content in Hindi..."></textarea>
                    </div>
                    
                    <button class="btn btn-primary" onclick="addItem()">➕ Add Item</button>
                    <button class="btn btn-warning" onclick="clearForm()">🗑️ Clear Form</button>
                </div>
            </div>
        </div>

        <!-- View Data Tab -->
        <div id="data" class="tab-content">
            <h2>📊 Current Data</h2>
            
            <div class="form-group">
                <input type="text" id="searchInput" class="form-input" placeholder="🔍 Search items..." onkeyup="searchItems()">
            </div>
            
            <div class="data-list" id="dataList">
                <div style="text-align: center; padding: 40px; color: #666;">
                    No data yet. Add some items to get started!
                </div>
            </div>
            
            <button class="btn btn-success" onclick="refreshData()">🔄 Refresh Data</button>
            <button class="btn btn-warning" onclick="debugData()">🐛 Debug Data</button>
            <button class="btn btn-primary" onclick="testAddItems()">🧪 Test Add 5 Items</button>
            <button class="btn btn-danger" onclick="clearAllData()">🗑️ Clear All Data</button>
        </div>

        <!-- Excel Import/Export Tab -->
        <div id="excel" class="tab-content">
            <h2>📤 Excel Import/Export</h2>
            
            <div class="grid-2">
                <div>
                    <h3>📥 Import from Excel</h3>
                    <p>Upload an Excel file with your spiritual content data.</p>
                    
                    <div class="form-group">
                        <label for="excelFile">Choose Excel File:</label>
                        <input type="file" id="excelFile" accept=".xlsx,.xls" onchange="handleExcelUpload(event)">
                    </div>
                    
                    <button class="btn btn-primary" onclick="downloadTemplate()">📥 Download Template</button>
                </div>
                
                <div>
                    <h3>📤 Export Data</h3>
                    <p>Download your current data for backup or app use.</p>
                    
                    <button class="btn btn-success" onclick="exportForApp()">📱 Export for App</button>
                    <button class="btn btn-warning" onclick="exportBackup()">💾 Create Backup</button>
                    <button class="btn btn-danger" onclick="clearDatabase()">🗑️ Clear Database</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/9.22.0/firebase-app.js';
        import { getFirestore, collection, doc, setDoc, getDoc, getDocs, deleteDoc, query, where, writeBatch } from 'https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore.js';
        
        // Make Firebase functions globally available
        window.initializeApp = initializeApp;
        window.getFirestore = getFirestore;
        window.collection = collection;
        window.doc = doc;
        window.setDoc = setDoc;
        window.getDoc = getDoc;
        window.getDocs = getDocs;
        window.deleteDoc = deleteDoc;
        window.query = query;
        window.where = where;
        window.writeBatch = writeBatch;
    </script>

    <script>
        // Global variables
        let db = null;
        let isConnected = false;
        let spiritualData = [];

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        // Tab switching
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // Test basic functionality
        function testBasicFunctionality() {
            showNotification('✅ Admin panel is working!', 'success');
            console.log('Basic functionality test passed');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded successfully');
            testBasicFunctionality();
            loadLocalData();
        });

        // Load data from localStorage
        function loadLocalData() {
            const saved = localStorage.getItem('arhamSpiritualReading');
            if (saved) {
                spiritualData = JSON.parse(saved);
                updateStats();
                renderDataList();
                updateParentSelect();
            }
        }

        // Save data to localStorage
        function saveLocalData() {
            console.log('Saving to localStorage:', spiritualData.length, 'items');
            localStorage.setItem('arhamSpiritualReading', JSON.stringify(spiritualData));
            updateStats();
            renderDataList();
            updateParentSelect();
            console.log('Saved to localStorage successfully');
        }

        // Update statistics
        function updateStats() {
            document.getElementById('totalItems').textContent = spiritualData.length;
            document.getElementById('lastSync').textContent = new Date().toLocaleTimeString();
            document.getElementById('syncStatus').textContent = isConnected ? '✅' : '❌';
        }

        // Render data list
        function renderDataList() {
            const dataList = document.getElementById('dataList');
            if (spiritualData.length === 0) {
                dataList.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;">No data yet. Add some items to get started!</div>';
                return;
            }

            let html = '';
            spiritualData.forEach((item, index) => {
                const indent = '&nbsp;'.repeat(item.level * 4);
                const icon = item.contentType === 'list' ? '📋' : '📄';
                html += `
                    <div class="data-item">
                        <div>
                            ${indent}${icon} <strong>${item.title}</strong>
                            ${item.author ? `<br>${indent}&nbsp;&nbsp;&nbsp;&nbsp;👤 ${item.author}` : ''}
                            ${item.keywords && item.keywords.length > 0 ? `<br>${indent}&nbsp;&nbsp;&nbsp;&nbsp;🏷️ ${item.keywords.join(', ')}` : ''}
                        </div>
                        <div class="item-actions">
                            <button class="btn btn-sm btn-danger" onclick="deleteItem(${index})">🗑️</button>
                        </div>
                    </div>
                `;
            });
            dataList.innerHTML = html;
        }

        // Update parent select options
        function updateParentSelect() {
            const select = document.getElementById('parentSelect');
            select.innerHTML = '<option value="">🏠 Root Level</option>';
            
            spiritualData.forEach(item => {
                if (item.contentType === 'list') {
                    const indent = '&nbsp;'.repeat(item.level * 2);
                    select.innerHTML += `<option value="${item.id}">${indent}📋 ${item.title}</option>`;
                }
            });
        }

        // Add new item
        function addItem() {
            const title = document.getElementById('itemTitle').value.trim();
            const type = document.getElementById('itemType').value;
            const parentId = document.getElementById('parentSelect').value;
            const author = document.getElementById('itemAuthor').value.trim();
            const keywords = document.getElementById('itemKeywords').value.trim();
            const content = document.getElementById('itemContent').value.trim();

            if (!title) {
                showNotification('Please enter a title!', 'error');
                return;
            }

            if (type === 'content' && !content) {
                showNotification('Please enter content for content items!', 'error');
                return;
            }

            // Check for duplicates
            const duplicate = spiritualData.find(item => 
                item.title.toLowerCase() === title.toLowerCase() && 
                item.parentId === parentId
            );
            if (duplicate) {
                showNotification('Duplicate item already exists!', 'error');
                return;
            }

            // Calculate level
            let level = 0;
            if (parentId) {
                const parent = spiritualData.find(item => item.id === parentId);
                if (parent) {
                    level = parent.level + 1;
                }
            }

            // Generate ID
            const itemId = type === 'content' ? 
                'content_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9) :
                'item_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            const newItem = {
                id: itemId,
                title: title,
                level: level,
                parentId: parentId || null,
                contentType: type,
                hasChildren: type === 'list',
                content: type === 'content' ? content : '',
                author: author || null,
                keywords: keywords ? keywords.split(',').map(k => k.trim()).filter(k => k) : [],
                searchText: (title + ' ' + (content || '') + ' ' + (author || '') + ' ' + (keywords || '')).toLowerCase(),
                createdAt: new Date(),
                updatedAt: new Date()
            };

            spiritualData.push(newItem);
            saveLocalData();
            clearForm();
            showNotification('✅ Item added successfully!', 'success');

            // Upload to Firebase if connected
            if (isConnected && db) {
                uploadToFirebase(newItem);
            }
        }

        // Clear form
        function clearForm() {
            document.getElementById('itemTitle').value = '';
            document.getElementById('itemType').value = 'list';
            document.getElementById('parentSelect').value = '';
            document.getElementById('itemAuthor').value = '';
            document.getElementById('itemKeywords').value = '';
            document.getElementById('itemContent').value = '';
        }

        // Delete item
        function deleteItem(index) {
            if (confirm('Are you sure you want to delete this item?')) {
                const item = spiritualData[index];
                spiritualData.splice(index, 1);
                saveLocalData();
                showNotification('Item deleted successfully!', 'success');

                // Delete from Firebase if connected
                if (isConnected && db) {
                    deleteFromFirebase(item.id);
                }
            }
        }

        // Search items
        function searchItems() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const items = document.querySelectorAll('.data-item');

            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // Refresh data
        function refreshData() {
            if (isConnected && db) {
                syncWithFirebase();
            } else {
                loadLocalData();
                showNotification('Data refreshed from local storage', 'info');
            }
        }

        // Debug function to check data
        function debugData() {
            console.log('=== DEBUG DATA ===');
            console.log('spiritualData.length:', spiritualData.length);
            console.log('spiritualData:', spiritualData);
            console.log('localStorage data:', localStorage.getItem('arhamSpiritualReading'));
            console.log('isConnected:', isConnected);
            console.log('db:', db);

            showNotification(`Debug: ${spiritualData.length} items in memory`, 'info');

            // Also check Firebase if connected
            if (isConnected && db) {
                checkFirebaseData();
            }
        }

        async function checkFirebaseData() {
            try {
                const collectionRef = window.collection(db, 'spiritual_reading');
                const snapshot = await window.getDocs(collectionRef);
                console.log('Firebase documents count:', snapshot.size);
                console.log('Firebase documents:', snapshot.docs.map(doc => ({ id: doc.id, data: doc.data() })));
                showNotification(`Firebase has ${snapshot.size} documents`, 'info');
            } catch (error) {
                console.error('Error checking Firebase:', error);
            }
        }

        // Clear all data
        function clearAllData() {
            if (confirm('Are you sure you want to clear all data? This cannot be undone!')) {
                spiritualData = [];
                saveLocalData();
                showNotification('All data cleared!', 'warning');
            }
        }

        // Test function to add sample items
        function testAddItems() {
            const testItems = [
                {
                    id: 'readcard_root',
                    title: 'दैनिक स्वाध्याय',
                    level: 0,
                    parentId: null,
                    contentType: 'list',
                    hasChildren: true,
                    content: '',
                    author: null,
                    keywords: [],
                    searchText: 'दैनिक स्वाध्याय',
                    createdAt: new Date(),
                    updatedAt: new Date()
                },
                {
                    id: 'item_test_1',
                    title: 'गीत/ढाल संग्रह',
                    level: 1,
                    parentId: 'readcard_root',
                    contentType: 'list',
                    hasChildren: true,
                    content: '',
                    author: null,
                    keywords: ['गीत', 'ढाल'],
                    searchText: 'गीत ढाल संग्रह',
                    createdAt: new Date(),
                    updatedAt: new Date()
                },
                {
                    id: 'item_test_2',
                    title: 'भिक्षु-स्तुति',
                    level: 2,
                    parentId: 'item_test_1',
                    contentType: 'list',
                    hasChildren: true,
                    content: '',
                    author: null,
                    keywords: ['भिक्षु', 'स्तुति'],
                    searchText: 'भिक्षु स्तुति',
                    createdAt: new Date(),
                    updatedAt: new Date()
                },
                {
                    id: 'content_test_1',
                    title: 'भिक्षु म्हारै प्रगट्या जी',
                    level: 3,
                    parentId: 'item_test_2',
                    contentType: 'content',
                    hasChildren: false,
                    content: 'भिक्षु म्हारै प्रगट्या जी, सुख सागर अवतार...',
                    author: 'आचार्य तुलसी',
                    keywords: ['भिक्षु', 'स्तुति', 'प्रार्थना'],
                    searchText: 'भिक्षु म्हारै प्रगट्या जी सुख सागर अवतार आचार्य तुलसी भिक्षु स्तुति प्रार्थना',
                    createdAt: new Date(),
                    updatedAt: new Date()
                },
                {
                    id: 'content_test_2',
                    title: 'तुलसी स्तुति',
                    level: 3,
                    parentId: 'item_test_2',
                    contentType: 'content',
                    hasChildren: false,
                    content: 'तुलसी महाराज की जय हो...',
                    author: 'आचार्य तुलसी',
                    keywords: ['तुलसी', 'स्तुति', 'महाराज'],
                    searchText: 'तुलसी स्तुति महाराज की जय हो आचार्य तुलसी तुलसी स्तुति महाराज',
                    createdAt: new Date(),
                    updatedAt: new Date()
                }
            ];

            // Clear existing data
            spiritualData = [];

            // Add test items
            testItems.forEach(item => {
                spiritualData.push(item);
            });

            saveLocalData();
            showNotification(`✅ Added ${testItems.length} test items!`, 'success');
            console.log('Test items added:', spiritualData);
        }

        // Firebase operations
        async function connectFirebase() {
            const apiKey = document.getElementById('apiKey').value.trim();
            const projectId = document.getElementById('projectId').value.trim();

            if (!apiKey || !projectId) {
                showNotification('Please enter API Key and Project ID!', 'error');
                return;
            }

            try {
                showNotification('🔄 Connecting to Firebase...', 'info');

                const firebaseConfig = {
                    apiKey: apiKey,
                    authDomain: `${projectId}.firebaseapp.com`,
                    projectId: projectId,
                    storageBucket: `${projectId}.appspot.com`,
                    messagingSenderId: "123456789",
                    appId: "1:123456789:web:abcdef"
                };

                const app = window.initializeApp(firebaseConfig);
                db = window.getFirestore(app);

                // Test connection
                await testConnection();

                if (isConnected) {
                    // Save config
                    localStorage.setItem('firebaseConfig', JSON.stringify(firebaseConfig));

                    // Update UI
                    document.getElementById('statusIndicator').classList.add('connected');
                    document.getElementById('connectionStatus').textContent = 'Connected';

                    // Ensure root document exists
                    await ensureRootDocument();

                    // Sync with Firebase
                    await syncWithFirebase();

                    showNotification('🎉 Connected to Firebase successfully!', 'success');
                }

            } catch (error) {
                console.error('Firebase connection error:', error);
                showNotification('❌ Connection failed: ' + error.message, 'error');
                isConnected = false;
                updateConnectionStatus();
            }
        }

        async function testConnection() {
            try {
                showNotification('🧪 Testing connection...', 'info');

                if (!db) {
                    throw new Error('Database not initialized');
                }

                // Try to read from a test collection
                const testRef = window.collection(db, 'spiritual_reading');
                await window.getDocs(testRef);

                isConnected = true;
                updateConnectionStatus();
                showNotification('✅ Connection test successful!', 'success');

            } catch (error) {
                isConnected = false;
                updateConnectionStatus();
                showNotification('❌ Connection test failed: ' + error.message, 'error');
            }
        }

        function clearConfig() {
            localStorage.removeItem('firebaseConfig');
            document.getElementById('apiKey').value = '';
            document.getElementById('projectId').value = '';
            document.getElementById('statusIndicator').classList.remove('connected');
            document.getElementById('connectionStatus').textContent = 'Not Connected';
            isConnected = false;
            db = null;
            updateStats();
            showNotification('Config cleared!', 'warning');
        }

        function updateConnectionStatus() {
            const indicator = document.getElementById('statusIndicator');
            const status = document.getElementById('connectionStatus');

            if (isConnected) {
                indicator.classList.add('connected');
                status.textContent = 'Connected';
            } else {
                indicator.classList.remove('connected');
                status.textContent = 'Not Connected';
            }
            updateStats();
        }

        async function uploadToFirebase(item) {
            try {
                const docRef = window.doc(db, 'spiritual_reading', item.id);
                await window.setDoc(docRef, item);
                console.log('Uploaded to Firebase:', item.id);
            } catch (error) {
                console.error('Upload error:', error);
                showNotification('⚠️ Failed to sync to Firebase: ' + error.message, 'warning');
            }
        }

        async function deleteFromFirebase(itemId) {
            try {
                const docRef = window.doc(db, 'spiritual_reading', itemId);
                await window.deleteDoc(docRef);
                console.log('Deleted from Firebase:', itemId);
            } catch (error) {
                console.error('Delete error:', error);
                showNotification('⚠️ Failed to delete from Firebase: ' + error.message, 'warning');
            }
        }

        async function syncWithFirebase() {
            try {
                showNotification('🔄 Syncing with Firebase...', 'info');

                const collectionRef = window.collection(db, 'spiritual_reading');
                const snapshot = await window.getDocs(collectionRef);

                // Clear local data and reload from Firebase
                spiritualData = [];

                snapshot.docs.forEach(doc => {
                    const data = doc.data();
                    spiritualData.push({
                        id: doc.id,
                        ...data
                    });
                });

                // Sort by level and title
                spiritualData.sort((a, b) => {
                    if (a.level !== b.level) return a.level - b.level;
                    return a.title.localeCompare(b.title);
                });

                saveLocalData();
                showNotification(`✅ Synced ${spiritualData.length} items from Firebase`, 'success');

            } catch (error) {
                console.error('Sync error:', error);
                showNotification('❌ Sync failed: ' + error.message, 'error');
            }
        }

        async function ensureRootDocument() {
            try {
                const rootDocRef = window.doc(db, 'spiritual_reading', 'readcard_root');
                const rootDoc = await window.getDoc(rootDocRef);

                if (!rootDoc.exists()) {
                    console.log('Creating root document: readcard_root');
                    const rootData = {
                        title: 'दैनिक स्वाध्याय',
                        parentId: null,
                        level: 0,
                        contentType: 'list',
                        hasChildren: true,
                        content: '',
                        author: null,
                        keywords: [],
                        searchText: 'दैनिक स्वाध्याय',
                        createdAt: new Date(),
                        updatedAt: new Date()
                    };
                    await window.setDoc(rootDocRef, rootData);
                    console.log('Root document created successfully');
                }
            } catch (error) {
                console.error('Error ensuring root document:', error);
            }
        }

        function downloadTemplate() {
            showNotification('📥 Downloading template...', 'info');

            const templateData = [
                {
                    'Document ID': 'readcard_root',
                    'Parent ID': '',
                    'Level': 0,
                    'Title': 'दैनिक स्वाध्याय',
                    'Content Type': 'list',
                    'Content': '',
                    'Keywords': '',
                    'Author': '',
                    'Has Children': 'true'
                },
                {
                    'Document ID': 'item_example_1',
                    'Parent ID': 'readcard_root',
                    'Level': 1,
                    'Title': 'गीत/ढाल संग्रह',
                    'Content Type': 'list',
                    'Content': '',
                    'Keywords': 'गीत, ढाल, संग्रह',
                    'Author': '',
                    'Has Children': 'true'
                },
                {
                    'Document ID': 'content_example_1',
                    'Parent ID': 'item_example_1',
                    'Level': 2,
                    'Title': 'भिक्षु म्हारै प्रगट्या जी',
                    'Content Type': 'content',
                    'Content': 'Your spiritual content in Hindi goes here...',
                    'Keywords': 'भिक्षु, स्तुति, प्रार्थना, गीत',
                    'Author': 'आचार्य तुलसी',
                    'Has Children': 'false'
                }
            ];

            // Convert to Excel format using SheetJS
            const ws = XLSX.utils.json_to_sheet(templateData);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "ArhamApp Data");

            // Download as Excel file
            XLSX.writeFile(wb, 'arham_data_template.xlsx');

            showNotification('📥 Excel template downloaded!', 'success');
        }

        async function handleExcelUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            try {
                showNotification('📤 Reading Excel file...', 'info');

                // Read Excel file
                const arrayBuffer = await file.arrayBuffer();
                const workbook = XLSX.read(arrayBuffer);
                const worksheet = workbook.Sheets[workbook.SheetNames[0]];
                const jsonData = XLSX.utils.sheet_to_json(worksheet);

                console.log('📊 Excel Data:', jsonData);
                showNotification(`📋 Found ${jsonData.length} rows in Excel`, 'info');

                // Clear existing data first
                spiritualData = [];

                let successCount = 0;
                let errorCount = 0;

                // Process each row one by one
                for (let i = 0; i < jsonData.length; i++) {
                    const row = jsonData[i];

                    try {
                        // Show progress
                        if (i % 10 === 0) {
                            showNotification(`📤 Processing item ${i + 1}/${jsonData.length}...`, 'info');
                        }

                        // Skip if missing required fields
                        if (!row['Document ID'] || !row['Title']) {
                            console.log(`❌ Skipping row ${i + 1}: Missing required fields`);
                            errorCount++;
                            continue;
                        }

                        // Create item
                        const newItem = {
                            id: String(row['Document ID']).trim(),
                            title: String(row['Title']).trim(),
                            level: parseInt(row['Level']) || 0,
                            parentId: row['Parent ID'] ? String(row['Parent ID']).trim() : null,
                            contentType: row['Content Type'] || 'list',
                            hasChildren: String(row['Has Children']).toLowerCase() === 'true',
                            content: row['Content'] || '',
                            keywords: row['Keywords'] ? String(row['Keywords']).split(',').map(k => k.trim()) : [],
                            author: row['Author'] || null,
                            searchText: (row['Title'] + ' ' + (row['Content'] || '') + ' ' + (row['Author'] || '')).toLowerCase(),
                            createdAt: new Date(),
                            updatedAt: new Date()
                        };

                        // Add to local array
                        spiritualData.push(newItem);
                        console.log(`✅ Added: ${newItem.id} - ${newItem.title}`);
                        successCount++;

                        // Upload to Firebase if connected
                        if (isConnected && db) {
                            try {
                                const docRef = window.doc(db, 'spiritual_reading', newItem.id);
                                await window.setDoc(docRef, newItem);
                                console.log(`🔥 Firebase: ${newItem.id}`);
                            } catch (firebaseError) {
                                console.error(`Firebase error for ${newItem.id}:`, firebaseError);
                            }
                        }

                        // Small delay to prevent overwhelming
                        if (i % 5 === 0) {
                            await new Promise(resolve => setTimeout(resolve, 50));
                        }

                    } catch (itemError) {
                        console.error(`Error processing row ${i + 1}:`, itemError);
                        errorCount++;
                    }
                }

                // Save to localStorage
                localStorage.setItem('arhamSpiritualReading', JSON.stringify(spiritualData));

                // Update UI
                renderDataList();
                updateStats();
                updateParentSelect();

                // Show results
                console.log(`📊 Final Results: ${successCount} success, ${errorCount} errors, Total: ${spiritualData.length}`);
                showNotification(`✅ Upload Complete! Added ${successCount} items. Total: ${spiritualData.length}`, 'success');

                // Clear file input
                event.target.value = '';

            } catch (error) {
                console.error('Excel upload error:', error);
                showNotification('❌ Excel upload failed: ' + error.message, 'error');
            }
        }

        function exportForApp() {
            if (spiritualData.length === 0) {
                showNotification('No data to export!', 'error');
                return;
            }

            showNotification('📱 Exporting for app...', 'info');

            const exportData = {
                spiritual_reading: spiritualData.map(item => ({
                    id: item.id,
                    title: item.title,
                    content: item.content,
                    parentId: item.parentId,
                    level: item.level,
                    contentType: item.contentType,
                    hasChildren: item.hasChildren,
                    author: item.author,
                    keywords: item.keywords,
                    searchText: item.searchText,
                    createdAt: item.createdAt,
                    updatedAt: item.updatedAt
                })),
                metadata: {
                    exportDate: new Date().toISOString(),
                    totalItems: spiritualData.length,
                    version: "1.0"
                }
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `arham_app_data_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showNotification('📱 App data exported successfully!', 'success');
        }

        function exportBackup() {
            if (spiritualData.length === 0) {
                showNotification('No data to backup!', 'error');
                return;
            }

            showNotification('💾 Creating backup...', 'info');

            const backupData = {
                data: spiritualData,
                backup_info: {
                    created: new Date().toISOString(),
                    total_items: spiritualData.length,
                    version: "1.0"
                }
            };

            const blob = new Blob([JSON.stringify(backupData, null, 2)], { type: 'application/json' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `arham_backup_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showNotification('💾 Backup created successfully!', 'success');
        }

        async function clearDatabase() {
            if (!isConnected) {
                showNotification('Please connect to Firebase first!', 'error');
                return;
            }

            const confirmed = confirm('⚠️ WARNING: This will delete ALL data in the Firebase database!\n\nThis action cannot be undone. Are you sure?');
            if (!confirmed) return;

            const doubleConfirm = confirm('🚨 FINAL WARNING: You are about to delete ALL your spiritual content data!\n\nType "DELETE" in the next prompt to confirm.');
            if (!doubleConfirm) return;

            const typeConfirm = prompt('Type "DELETE" to confirm deletion:');
            if (typeConfirm !== 'DELETE') {
                showNotification('❌ Deletion cancelled - incorrect confirmation text.', 'info');
                return;
            }

            try {
                showNotification('🗑️ Clearing database...', 'info');

                const collectionRef = window.collection(db, 'spiritual_reading');
                const snapshot = await window.getDocs(collectionRef);

                if (snapshot.empty) {
                    showNotification('ℹ️ Database is already empty!', 'info');
                    return;
                }

                // Process in batches (Firebase batch limit is 500)
                const batchSize = 400;
                let deletedCount = 0;

                for (let i = 0; i < snapshot.docs.length; i += batchSize) {
                    const batch = window.writeBatch(db);
                    const docsToDelete = snapshot.docs.slice(i, i + batchSize);

                    docsToDelete.forEach(doc => {
                        batch.delete(doc.ref);
                    });

                    await batch.commit();
                    deletedCount += docsToDelete.length;

                    showNotification(`🗑️ Deleted ${deletedCount}/${snapshot.size} documents...`, 'info');
                }

                // Clear local data
                spiritualData = [];
                saveLocalData();

                showNotification(`✅ Successfully cleared ${deletedCount} documents from database!`, 'success');

            } catch (error) {
                console.error('Database clear error:', error);
                showNotification('❌ Error clearing database: ' + error.message, 'error');
            }
        }

        // Load saved Firebase config on page load
        document.addEventListener('DOMContentLoaded', function() {
            const savedConfig = localStorage.getItem('firebaseConfig');
            if (savedConfig) {
                try {
                    const config = JSON.parse(savedConfig);
                    document.getElementById('apiKey').value = config.apiKey;
                    document.getElementById('projectId').value = config.projectId;
                } catch (error) {
                    console.error('Error loading saved config:', error);
                }
            }
        });
    </script>
</body>
</html>
