package com.example.arham.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.example.arham.ui.theme.*



@Composable
fun FullScreenGlassBackground(
    blurRadius: Float = 35f,
    isDarkTheme: Boolean,
    onToggleTheme: () -> Unit,
    content: @Composable () -> Unit
) {
    // Determine the background gradient based on the theme
    val backgroundBrush = if (isDarkTheme) {
        Brush.verticalGradient(
            colors = listOf(Color(0xFF28004D), Color(0xFF000000)) // Darker gradient
        )
    } else {
        Brush.verticalGradient(
            colors = listOf(Color(0xFFBB86FC), Color(0xFF6A1B9A)) // Lighter gradient
        )
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            // 1. Apply the background content (gradient) that will be blurred
            .background(backgroundBrush)
            // 2. Apply enhanced blur effect
            .blur(radius = (blurRadius / 2).dp)
            // 3. Apply a full-screen semi-transparent overlay to give the "glass" tint
            .background(
                if (isDarkTheme) DarkGlassSurfaceColor.copy(alpha = 0.15f)
                else LightGlassSurfaceColor.copy(alpha = 0.20f)
            )
    ) {
        content()
    }
}

@Composable
fun GlassCard(
    modifier: Modifier = Modifier,
    blurRadius: Float = 25f,
    cornerRadius: Dp = 24.dp,
    surfaceAlpha: Float = 0.25f,
    isDarkTheme: Boolean,
    content: @Composable () -> Unit
) {
    val currentGlassSurfaceColor = if (isDarkTheme) DarkGlassSurfaceColor else LightGlassSurfaceColor
    val currentLightGradientColor = if (isDarkTheme) Color.White.copy(alpha = 0.05f) else Color.White.copy(alpha = 0.1f)

    Box(
        modifier = modifier
            .blur(radius = (blurRadius / 3).dp)
            .clip(RoundedCornerShape(cornerRadius))
            .background(currentGlassSurfaceColor.copy(alpha = surfaceAlpha))
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        currentLightGradientColor,
                        Color.Transparent
                    ),
                    startY = 0f,
                    endY = 200f
                )
            )
    ) {
        content()
    }
}

@Composable
fun GlassmorphismOverlay(
    modifier: Modifier = Modifier,
    blurRadius: Float = 50f,
    isDarkTheme: Boolean,
    content: @Composable () -> Unit
) {
    // True background blur glassmorphism overlay
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(
                if (isDarkTheme) {
                    Color.Black.copy(alpha = 0.1f) // Very low opacity to let background show
                } else {
                    Color.White.copy(alpha = 0.1f) // Very low opacity to let background show
                }
            )
    ) {
        content()
    }
}
