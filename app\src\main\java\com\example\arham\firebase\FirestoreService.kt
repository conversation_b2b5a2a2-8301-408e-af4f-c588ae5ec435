package com.example.arham.firebase

import com.example.arham.data.models.Bookmark
import com.example.arham.data.models.ReadingProgress
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import kotlinx.coroutines.tasks.await

class FirestoreService {
    
    private val firestore = FirebaseManager.firestore
    private val auth = FirebaseManager.auth
    
    companion object {
        private const val USERS_COLLECTION = "users"
        private const val BOOKMARKS_COLLECTION = "bookmarks"
        private const val READING_PROGRESS_COLLECTION = "reading_progress"
        private const val USER_PREFERENCES_COLLECTION = "user_preferences"
    }
    
    /**
     * Save user profile data
     */
    suspend fun saveUserProfile(userData: Map<String, Any>) {
        val userId = getCurrentUserId() ?: throw Exception("User not authenticated")
        firestore.collection(USERS_COLLECTION)
            .document(userId)
            .set(userData)
            .await()
    }
    
    /**
     * Get user profile data
     */
    suspend fun getUserProfile(): Map<String, Any>? {
        val userId = getCurrentUserId() ?: return null
        val document = firestore.collection(USERS_COLLECTION)
            .document(userId)
            .get()
            .await()
        return document.data
    }
    
    /**
     * Save bookmark to Firestore
     */
    suspend fun saveBookmark(bookmark: Bookmark) {
        val userId = getCurrentUserId() ?: throw Exception("User not authenticated")
        val bookmarkData = mapOf(
            "id" to bookmark.id,
            "bookId" to bookmark.bookId,
            "bookTitle" to bookmark.bookTitle,
            "author" to bookmark.author,
            "highlightedText" to bookmark.highlightedText,
            "context" to bookmark.context,
            "pageNumber" to bookmark.pageNumber,
            "timestamp" to bookmark.timestamp,
            "notes" to bookmark.notes,
            "userId" to userId
        )
        
        firestore.collection(BOOKMARKS_COLLECTION)
            .document(bookmark.id)
            .set(bookmarkData)
            .await()
    }
    
    /**
     * Get user's bookmarks
     */
    suspend fun getUserBookmarks(): List<Bookmark> {
        val userId = getCurrentUserId() ?: return emptyList()
        val querySnapshot = firestore.collection(BOOKMARKS_COLLECTION)
            .whereEqualTo("userId", userId)
            .orderBy("timestamp", Query.Direction.DESCENDING)
            .get()
            .await()
        
        return querySnapshot.documents.mapNotNull { document ->
            try {
                Bookmark(
                    id = document.getString("id") ?: "",
                    bookId = document.getString("bookId") ?: "",
                    bookTitle = document.getString("bookTitle") ?: "",
                    author = document.getString("author") ?: "",
                    highlightedText = document.getString("highlightedText") ?: "",
                    context = document.getString("context") ?: "",
                    pageNumber = document.getLong("pageNumber")?.toInt() ?: 1,
                    timestamp = document.getLong("timestamp") ?: 0L,
                    notes = document.getString("notes") ?: ""
                )
            } catch (e: Exception) {
                null
            }
        }
    }
    
    /**
     * Delete bookmark
     */
    suspend fun deleteBookmark(bookmarkId: String) {
        firestore.collection(BOOKMARKS_COLLECTION)
            .document(bookmarkId)
            .delete()
            .await()
    }
    
    /**
     * Save reading progress
     */
    suspend fun saveReadingProgress(progress: ReadingProgress) {
        val userId = getCurrentUserId() ?: throw Exception("User not authenticated")
        val progressData = mapOf(
            "bookId" to progress.bookId,
            "progress" to progress.progress,
            "lastReadPosition" to progress.lastReadPosition,
            "totalPages" to progress.totalPages,
            "lastReadTime" to progress.lastReadTime,
            "userId" to userId
        )
        
        firestore.collection(READING_PROGRESS_COLLECTION)
            .document("${userId}_${progress.bookId}")
            .set(progressData)
            .await()
    }
    
    /**
     * Get reading progress for a book
     */
    suspend fun getReadingProgress(bookId: String): ReadingProgress? {
        val userId = getCurrentUserId() ?: return null
        val document = firestore.collection(READING_PROGRESS_COLLECTION)
            .document("${userId}_${bookId}")
            .get()
            .await()
        
        return if (document.exists()) {
            ReadingProgress(
                bookId = document.getString("bookId") ?: bookId,
                progress = document.getDouble("progress")?.toFloat() ?: 0f,
                lastReadPosition = document.getLong("lastReadPosition")?.toInt() ?: 0,
                totalPages = document.getLong("totalPages")?.toInt() ?: 1,
                lastReadTime = document.getLong("lastReadTime") ?: 0L
            )
        } else null
    }
    
    /**
     * Get all reading progress for user
     */
    suspend fun getAllReadingProgress(): List<ReadingProgress> {
        val userId = getCurrentUserId() ?: return emptyList()
        val querySnapshot = firestore.collection(READING_PROGRESS_COLLECTION)
            .whereEqualTo("userId", userId)
            .get()
            .await()
        
        return querySnapshot.documents.mapNotNull { document ->
            try {
                ReadingProgress(
                    bookId = document.getString("bookId") ?: "",
                    progress = document.getDouble("progress")?.toFloat() ?: 0f,
                    lastReadPosition = document.getLong("lastReadPosition")?.toInt() ?: 0,
                    totalPages = document.getLong("totalPages")?.toInt() ?: 1,
                    lastReadTime = document.getLong("lastReadTime") ?: 0L
                )
            } catch (e: Exception) {
                null
            }
        }
    }
    
    /**
     * Save user preferences
     */
    suspend fun saveUserPreferences(preferences: Map<String, Any>) {
        val userId = getCurrentUserId() ?: throw Exception("User not authenticated")
        firestore.collection(USER_PREFERENCES_COLLECTION)
            .document(userId)
            .set(preferences)
            .await()
    }
    
    /**
     * Get user preferences
     */
    suspend fun getUserPreferences(): Map<String, Any>? {
        val userId = getCurrentUserId() ?: return null
        val document = firestore.collection(USER_PREFERENCES_COLLECTION)
            .document(userId)
            .get()
            .await()
        return document.data
    }
    
    /**
     * Get current user ID
     */
    private fun getCurrentUserId(): String? {
        return auth.currentUser?.uid
    }
}
