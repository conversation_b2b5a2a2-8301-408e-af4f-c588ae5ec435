<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArhamApp Advanced Admin - Future Ready CMS</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        :root {
            --primary-color: #4ECDC4;
            --secondary-color: #FF6B6B;
            --accent-color: #667eea;
            --success-color: #56ab2f;
            --warning-color: #ffa726;
            --danger-color: #ff4757;
            --dark-color: #2c3e50;
            --light-color: #ecf0f1;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            --shadow: 0 10px 30px rgba(0,0,0,0.1);
            --border-radius: 15px;
        }
        
        body {
            font-family: '<PERSON><PERSON><PERSON>', <PERSON><PERSON><PERSON>, Geneva, Verdana, sans-serif;
            background: var(--gradient-primary);
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .admin-wrapper {
            display: flex;
            min-height: 100vh;
        }
        
        /* Advanced Sidebar */
        .sidebar {
            width: 320px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            overflow-y: auto;
            position: relative;
        }
        
        .sidebar.collapsed {
            width: 80px;
        }
        
        .sidebar-header {
            padding: 25px;
            background: var(--gradient-secondary);
            color: white;
            text-align: center;
            position: relative;
        }
        
        .sidebar-toggle {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .sidebar-toggle:hover {
            background: rgba(255,255,255,0.3);
            transform: rotate(180deg);
        }
        
        .logo-section h1 {
            font-size: 2em;
            margin-bottom: 5px;
            transition: all 0.3s;
        }
        
        .logo-section p {
            opacity: 0.9;
            transition: all 0.3s;
        }
        
        .sidebar.collapsed .logo-section h1 {
            font-size: 1.2em;
        }
        
        .sidebar.collapsed .logo-section p {
            display: none;
        }
        
        /* Navigation Menu */
        .nav-section {
            padding: 20px 0;
        }
        
        .nav-title {
            padding: 10px 25px;
            font-size: 12px;
            font-weight: 600;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s;
        }
        
        .sidebar.collapsed .nav-title {
            display: none;
        }
        
        .nav-menu {
            list-style: none;
        }
        
        .nav-item {
            margin: 5px 15px;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            text-decoration: none;
            color: #333;
            border-radius: 12px;
            transition: all 0.3s;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }
        
        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--gradient-secondary);
            transition: all 0.3s;
            z-index: -1;
        }
        
        .nav-link:hover::before,
        .nav-link.active::before {
            left: 0;
        }
        
        .nav-link:hover,
        .nav-link.active {
            color: white;
            transform: translateX(5px);
        }
        
        .nav-icon {
            margin-right: 15px;
            font-size: 1.3em;
            min-width: 20px;
            transition: all 0.3s;
        }
        
        .nav-text {
            transition: all 0.3s;
        }
        
        .sidebar.collapsed .nav-text {
            display: none;
        }
        
        .nav-badge {
            margin-left: auto;
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .sidebar.collapsed .nav-badge {
            display: none;
        }
        
        /* Main Content Area */
        .main-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
            transition: all 0.3s;
        }
        
        /* Header */
        .content-header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 25px 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .header-left h1 {
            font-size: 2.2em;
            color: var(--dark-color);
            margin-bottom: 5px;
        }
        
        .header-left p {
            color: #666;
            font-size: 1.1em;
        }
        
        .header-right {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .search-box {
            position: relative;
        }
        
        .search-input {
            padding: 12px 45px 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 14px;
            width: 250px;
            transition: all 0.3s;
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
        }
        
        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }
        
        /* Dashboard Grid */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .dashboard-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--shadow);
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }
        
        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(--gradient-secondary);
        }
        
        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .card-icon {
            font-size: 2.5em;
            margin-right: 15px;
            background: var(--gradient-secondary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .card-info h3 {
            font-size: 1.3em;
            color: var(--dark-color);
            margin-bottom: 5px;
        }
        
        .card-info p {
            color: #666;
            font-size: 0.9em;
        }
        
        .card-value {
            font-size: 3em;
            font-weight: bold;
            background: var(--gradient-secondary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }
        
        .card-trend {
            display: flex;
            align-items: center;
            font-size: 0.9em;
            color: var(--success-color);
        }
        
        .trend-icon {
            margin-right: 5px;
        }
        
        /* Content Sections */
        .content-section {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow);
            display: none;
            animation: fadeIn 0.3s ease;
        }
        
        .content-section.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .section-title {
            font-size: 1.8em;
            color: var(--dark-color);
            display: flex;
            align-items: center;
        }
        
        .section-title .icon {
            margin-right: 10px;
            font-size: 1.2em;
        }
        
        /* Advanced Form Styles */
        .form-container {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }
        
        .form-main {
            background: #f8f9fa;
            border-radius: var(--border-radius);
            padding: 25px;
        }
        
        .form-sidebar {
            background: #f8f9fa;
            border-radius: var(--border-radius);
            padding: 25px;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-label {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 8px;
            font-size: 14px;
            display: flex;
            align-items: center;
        }
        
        .form-label .required {
            color: var(--danger-color);
            margin-left: 3px;
        }
        
        .form-input, .form-select, .form-textarea {
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 14px;
            transition: all 0.3s;
            background: white;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 120px;
            font-family: inherit;
        }
        
        /* Advanced Buttons */
        .btn {
            background: var(--gradient-secondary);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin: 5px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .btn-primary { background: linear-gradient(45deg, var(--primary-color), #44A08D); }
        .btn-success { background: linear-gradient(45deg, var(--success-color), #a8e6cf); }
        .btn-danger { background: linear-gradient(45deg, var(--danger-color), #ff3838); }
        .btn-warning { background: linear-gradient(45deg, var(--warning-color), #ff9800); }
        .btn-info { background: linear-gradient(45deg, var(--accent-color), #764ba2); }
        .btn-secondary { background: linear-gradient(45deg, #6c757d, #495057); }
        
        .btn-outline {
            background: transparent;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
        }
        
        .btn-outline:hover {
            background: var(--primary-color);
            color: white;
        }
        
        /* Advanced Table */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow);
        }
        
        .data-table th {
            background: var(--gradient-secondary);
            color: white;
            padding: 18px 15px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .data-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s;
        }
        
        .data-table tr:hover {
            background: #f8f9fa;
            transform: scale(1.01);
        }
        
        .data-table tr:last-child td {
            border-bottom: none;
        }
        
        /* Status Badges */
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-draft { background: #e2e3e5; color: #383d41; }
        
        /* Responsive Design */
        @media (max-width: 1200px) {
            .form-container {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .admin-wrapper {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: auto;
            }
            
            .sidebar.collapsed {
                width: 100%;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .content-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .search-input {
                width: 100%;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
        
        /* Loading States */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Notifications */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            transform: translateX(400px);
            transition: all 0.3s;
            box-shadow: var(--shadow);
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.success { background: var(--gradient-secondary); }
        .notification.error { background: linear-gradient(45deg, var(--danger-color), #ff3838); }
        .notification.info { background: linear-gradient(45deg, var(--accent-color), #764ba2); }
        .notification.warning { background: linear-gradient(45deg, var(--warning-color), #ff9800); }
        
        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--gradient-secondary);
            border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="admin-wrapper">
        <!-- Advanced Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
                <div class="logo-section">
                    <h1>🕉️ ArhamApp</h1>
                    <p>Advanced Admin CMS</p>
                </div>
            </div>

            <!-- Core Content Management -->
            <div class="nav-section">
                <div class="nav-title">Content Management</div>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link active" onclick="showSection('dashboard')">
                            <span class="nav-icon">📊</span>
                            <span class="nav-text">Dashboard</span>
                            <span class="nav-badge" id="dashboardBadge">0</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('content-manager')">
                            <span class="nav-icon">📝</span>
                            <span class="nav-text">Content Manager</span>
                            <span class="nav-badge" id="contentBadge">0</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('media-library')">
                            <span class="nav-icon">🖼️</span>
                            <span class="nav-text">Media Library</span>
                            <span class="nav-badge" id="mediaBadge">0</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('categories')">
                            <span class="nav-icon">📂</span>
                            <span class="nav-text">Categories</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- User Features -->
            <div class="nav-section">
                <div class="nav-title">User Features</div>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('user-management')">
                            <span class="nav-icon">👥</span>
                            <span class="nav-text">User Management</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('notifications')">
                            <span class="nav-icon">🔔</span>
                            <span class="nav-text">Notifications</span>
                            <span class="nav-badge" id="notificationBadge">0</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('favorites')">
                            <span class="nav-icon">⭐</span>
                            <span class="nav-text">Favorites</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('bookmarks')">
                            <span class="nav-icon">🔖</span>
                            <span class="nav-text">Bookmarks</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- App Features -->
            <div class="nav-section">
                <div class="nav-title">App Features</div>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('audio-manager')">
                            <span class="nav-icon">🎵</span>
                            <span class="nav-text">Audio Manager</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('video-manager')">
                            <span class="nav-icon">🎥</span>
                            <span class="nav-text">Video Manager</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('offline-content')">
                            <span class="nav-icon">📱</span>
                            <span class="nav-text">Offline Content</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('search-config')">
                            <span class="nav-icon">🔍</span>
                            <span class="nav-text">Search Config</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Analytics & Tools -->
            <div class="nav-section">
                <div class="nav-title">Analytics & Tools</div>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('analytics')">
                            <span class="nav-icon">📈</span>
                            <span class="nav-text">Analytics</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('api-manager')">
                            <span class="nav-icon">🔌</span>
                            <span class="nav-text">API Manager</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('backup-restore')">
                            <span class="nav-icon">💾</span>
                            <span class="nav-text">Backup & Restore</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('import-export')">
                            <span class="nav-icon">🔄</span>
                            <span class="nav-text">Import/Export</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Settings -->
            <div class="nav-section">
                <div class="nav-title">Configuration</div>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('app-settings')">
                            <span class="nav-icon">⚙️</span>
                            <span class="nav-text">App Settings</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('theme-config')">
                            <span class="nav-icon">🎨</span>
                            <span class="nav-text">Theme Config</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" onclick="showSection('security')">
                            <span class="nav-icon">🔒</span>
                            <span class="nav-text">Security</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Header -->
            <div class="content-header">
                <div class="header-left">
                    <h1 id="pageTitle">Dashboard</h1>
                    <p id="pageSubtitle">ArhamApp Content Management Overview</p>
                </div>
                <div class="header-right">
                    <div class="search-box">
                        <input type="text" class="search-input" placeholder="Search anything..." id="globalSearch">
                        <span class="search-icon">🔍</span>
                    </div>
                    <button class="btn btn-primary" onclick="showSection('content-manager')">
                        ➕ Quick Add
                    </button>
                    <button class="btn btn-success" onclick="exportAllData()">
                        📱 Export for App
                    </button>
                </div>
            </div>

            <!-- Dashboard Section -->
            <div id="dashboard" class="content-section active">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon">📖</div>
                            <div class="card-info">
                                <h3>Total Content</h3>
                                <p>Spiritual content items</p>
                            </div>
                        </div>
                        <div class="card-value" id="totalContent">0</div>
                        <div class="card-trend">
                            <span class="trend-icon">📈</span>
                            <span>+12% from last month</span>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon">👥</div>
                            <div class="card-info">
                                <h3>Active Users</h3>
                                <p>App users</p>
                            </div>
                        </div>
                        <div class="card-value" id="activeUsers">0</div>
                        <div class="card-trend">
                            <span class="trend-icon">📈</span>
                            <span>+8% from last week</span>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon">📂</div>
                            <div class="card-info">
                                <h3>Categories</h3>
                                <p>Content categories</p>
                            </div>
                        </div>
                        <div class="card-value" id="totalCategories">0</div>
                        <div class="card-trend">
                            <span class="trend-icon">📈</span>
                            <span>+2 new categories</span>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon">🎵</div>
                            <div class="card-info">
                                <h3>Media Files</h3>
                                <p>Audio & Video</p>
                            </div>
                        </div>
                        <div class="card-value" id="totalMedia">0</div>
                        <div class="card-trend">
                            <span class="trend-icon">📈</span>
                            <span>+5 new files</span>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon">⭐</div>
                            <div class="card-info">
                                <h3>Favorites</h3>
                                <p>User favorites</p>
                            </div>
                        </div>
                        <div class="card-value" id="totalFavorites">0</div>
                        <div class="card-trend">
                            <span class="trend-icon">📈</span>
                            <span>+15% engagement</span>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-icon">📱</div>
                            <div class="card-info">
                                <h3>App Downloads</h3>
                                <p>Total downloads</p>
                            </div>
                        </div>
                        <div class="card-value" id="appDownloads">0</div>
                        <div class="card-trend">
                            <span class="trend-icon">📈</span>
                            <span>+25% this month</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="content-section active">
                    <div class="section-header">
                        <h2 class="section-title">
                            <span class="icon">⚡</span>
                            Quick Actions
                        </h2>
                    </div>
                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <h3>📝 Content Management</h3>
                            <p>Add, edit, and manage spiritual content</p>
                            <div style="margin-top: 15px;">
                                <button class="btn btn-primary" onclick="showSection('content-manager')">
                                    ➕ Add Content
                                </button>
                                <button class="btn btn-outline" onclick="showSection('content-manager')">
                                    📝 Manage All
                                </button>
                            </div>
                        </div>

                        <div class="dashboard-card">
                            <h3>🎵 Media Upload</h3>
                            <p>Upload audio, video, and images</p>
                            <div style="margin-top: 15px;">
                                <button class="btn btn-success" onclick="showSection('media-library')">
                                    📤 Upload Media
                                </button>
                                <button class="btn btn-outline" onclick="showSection('media-library')">
                                    🖼️ View Library
                                </button>
                            </div>
                        </div>

                        <div class="dashboard-card">
                            <h3>👥 User Management</h3>
                            <p>Manage app users and permissions</p>
                            <div style="margin-top: 15px;">
                                <button class="btn btn-info" onclick="showSection('user-management')">
                                    👥 Manage Users
                                </button>
                                <button class="btn btn-outline" onclick="showSection('analytics')">
                                    📈 View Analytics
                                </button>
                            </div>
                        </div>

                        <div class="dashboard-card">
                            <h3>🔄 Data Transfer</h3>
                            <p>Import/Export and backup data</p>
                            <div style="margin-top: 15px;">
                                <button class="btn btn-warning" onclick="showSection('import-export')">
                                    🔄 Import/Export
                                </button>
                                <button class="btn btn-outline" onclick="showSection('backup-restore')">
                                    💾 Backup
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="content-section active">
                    <div class="section-header">
                        <h2 class="section-title">
                            <span class="icon">📋</span>
                            Recent Activity
                        </h2>
                        <button class="btn btn-outline" onclick="showSection('content-manager')">
                            View All
                        </button>
                    </div>
                    <div id="recentActivity">
                        <p style="text-align: center; color: #666; padding: 40px;">
                            No recent activity. Start by adding some content!
                        </p>
                    </div>
                </div>
            </div>
