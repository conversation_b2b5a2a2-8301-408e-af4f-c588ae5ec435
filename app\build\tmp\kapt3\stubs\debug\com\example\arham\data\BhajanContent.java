package com.example.arham.data;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0006\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0004J\u000e\u0010\u0006\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0004J\u000e\u0010\u0007\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0004J\u0010\u0010\b\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0004H\u0002J\u0010\u0010\t\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0004H\u0002\u00a8\u0006\n"}, d2 = {"Lcom/example/arham/data/BhajanContent;", "", "()V", "getBhajanAuthor", "", "title", "getBhajanCategory", "getBhajanContent", "getStaticCategory", "getStaticContent", "app_debug"})
public final class BhajanContent {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.arham.data.BhajanContent INSTANCE = null;
    
    private BhajanContent() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getBhajanContent(@org.jetbrains.annotations.NotNull()
    java.lang.String title) {
        return null;
    }
    
    private final java.lang.String getStaticContent(java.lang.String title) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getBhajanAuthor(@org.jetbrains.annotations.NotNull()
    java.lang.String title) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getBhajanCategory(@org.jetbrains.annotations.NotNull()
    java.lang.String title) {
        return null;
    }
    
    private final java.lang.String getStaticCategory(java.lang.String title) {
        return null;
    }
}