#!/usr/bin/env python3
"""
ArhamApp Auto-Detect Hierarchical Converter
Automatically detects structure and converts to JSON
"""

import pandas as pd
import json
import uuid
from datetime import datetime
import sys
import os

def auto_convert_arham_data(file_path):
    """
    Auto-detect structure and convert ArhamApp data
    """
    
    try:
        print("🔄 Auto-detecting ArhamApp data structure...")
        
        # Read all sheets
        excel_file = pd.ExcelFile(file_path)
        sheet_names = excel_file.sheet_names
        
        print(f"📋 Found {len(sheet_names)} sheets: {sheet_names}")
        
        # Read and analyze each sheet
        sheets_data = {}
        sheets_analysis = {}
        
        for i, sheet_name in enumerate(sheet_names):
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            sheets_data[sheet_name] = df
            
            # Analyze sheet structure
            analysis = analyze_sheet_structure(df, i, sheet_name)
            sheets_analysis[sheet_name] = analysis
            
            print(f"✅ Sheet {i+1} '{sheet_name}': {len(df)} rows, {len(df.columns)} columns")
            print(f"   Columns: {list(df.columns)}")
            print(f"   Detected as: {analysis['level_type']}")
            print()
        
        # Build hierarchical structure
        hierarchy = build_auto_hierarchy(sheets_data, sheets_analysis, sheet_names)
        
        # Save outputs
        save_multiple_formats(hierarchy, sheets_data)
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def analyze_sheet_structure(df, sheet_index, sheet_name):
    """
    Analyze sheet structure to determine its role in hierarchy
    """
    
    analysis = {
        'level_index': sheet_index,
        'level_type': '',
        'main_column': '',
        'content_columns': [],
        'reference_columns': [],
        'row_count': len(df),
        'column_count': len(df.columns)
    }
    
    # Determine level type based on sheet index and name
    level_types = [
        'explore_screen',  # Sheet 1
        'section',         # Sheet 2  
        'category',        # Sheet 3
        'author',          # Sheet 4
        'title'           # Sheet 5 (content)
    ]
    
    if sheet_index < len(level_types):
        analysis['level_type'] = level_types[sheet_index]
    else:
        analysis['level_type'] = f'level_{sheet_index}'
    
    # Identify main column (usually first column)
    if len(df.columns) > 0:
        analysis['main_column'] = df.columns[0]
    
    # Identify content columns
    content_keywords = ['content', 'text', 'lyrics', 'description', 'meaning', 'translation']
    for col in df.columns:
        col_lower = col.lower()
        if any(keyword in col_lower for keyword in content_keywords):
            analysis['content_columns'].append(col)
    
    # Identify potential reference columns
    ref_keywords = ['parent', 'category', 'section', 'author', 'type', 'group']
    for col in df.columns:
        col_lower = col.lower()
        if any(keyword in col_lower for keyword in ref_keywords):
            analysis['reference_columns'].append(col)
    
    return analysis

def build_auto_hierarchy(sheets_data, sheets_analysis, sheet_names):
    """
    Build hierarchy automatically based on detected structure
    """
    
    hierarchy = {
        "arhamApp": {
            "metadata": {
                "totalSheets": len(sheet_names),
                "structure": [analysis['level_type'] for analysis in sheets_analysis.values()],
                "createdAt": int(datetime.now().timestamp() * 1000),
                "autoDetected": True
            },
            "navigation": {},
            "content": {}
        }
    }
    
    # Process each sheet level by level
    for i, sheet_name in enumerate(sheet_names):
        df = sheets_data[sheet_name]
        analysis = sheets_analysis[sheet_name]
        
        print(f"🔄 Processing {analysis['level_type']}: {sheet_name}")
        
        # Process based on level type
        if analysis['level_type'] == 'explore_screen':
            process_explore_screens(hierarchy, df, analysis)
        elif analysis['level_type'] == 'section':
            process_sections(hierarchy, df, analysis)
        elif analysis['level_type'] == 'category':
            process_categories(hierarchy, df, analysis)
        elif analysis['level_type'] == 'author':
            process_authors(hierarchy, df, analysis)
        elif analysis['level_type'] == 'title':
            process_titles(hierarchy, df, analysis)
    
    return hierarchy

def process_explore_screens(hierarchy, df, analysis):
    """Process explore screen level"""
    
    main_col = analysis['main_column']
    
    for _, row in df.iterrows():
        if pd.notna(row[main_col]):
            screen_name = str(row[main_col]).strip()
            
            screen_data = {
                "id": generate_clean_id(screen_name),
                "name": screen_name,
                "level": 0,
                "type": "explore_screen",
                "sections": {},
                "metadata": extract_row_metadata(row, df.columns, main_col)
            }
            
            hierarchy["arhamApp"]["navigation"][screen_name] = screen_data

def process_sections(hierarchy, df, analysis):
    """Process section level"""
    
    main_col = analysis['main_column']
    
    for _, row in df.iterrows():
        if pd.notna(row[main_col]):
            section_name = str(row[main_col]).strip()
            
            section_data = {
                "id": generate_clean_id(section_name),
                "name": section_name,
                "level": 1,
                "type": "section",
                "categories": {},
                "metadata": extract_row_metadata(row, df.columns, main_col)
            }
            
            # Try to find parent explore screen
            parent_screen = find_or_create_parent(hierarchy["arhamApp"]["navigation"], "explore_screen")
            if parent_screen:
                hierarchy["arhamApp"]["navigation"][parent_screen]["sections"][section_name] = section_data

def process_categories(hierarchy, df, analysis):
    """Process category level"""
    
    main_col = analysis['main_column']
    
    for _, row in df.iterrows():
        if pd.notna(row[main_col]):
            category_name = str(row[main_col]).strip()
            
            category_data = {
                "id": generate_clean_id(category_name),
                "name": category_name,
                "level": 2,
                "type": "category",
                "authors": {},
                "metadata": extract_row_metadata(row, df.columns, main_col)
            }
            
            # Find parent section
            parent_screen, parent_section = find_parent_section(hierarchy)
            if parent_screen and parent_section:
                nav = hierarchy["arhamApp"]["navigation"]
                nav[parent_screen]["sections"][parent_section]["categories"][category_name] = category_data

def process_authors(hierarchy, df, analysis):
    """Process author level"""
    
    main_col = analysis['main_column']
    
    for _, row in df.iterrows():
        if pd.notna(row[main_col]):
            author_name = str(row[main_col]).strip()
            
            author_data = {
                "id": generate_clean_id(author_name),
                "name": author_name,
                "level": 3,
                "type": "author",
                "titles": {},
                "metadata": extract_row_metadata(row, df.columns, main_col)
            }
            
            # Find parent category
            parent_path = find_parent_category(hierarchy)
            if parent_path:
                screen, section, category = parent_path
                nav = hierarchy["arhamApp"]["navigation"]
                nav[screen]["sections"][section]["categories"][category]["authors"][author_name] = author_data

def process_titles(hierarchy, df, analysis):
    """Process title/content level"""
    
    main_col = analysis['main_column']
    
    for _, row in df.iterrows():
        if pd.notna(row[main_col]):
            title_name = str(row[main_col]).strip()
            
            # Extract content from content columns
            content_data = {}
            for col in analysis['content_columns']:
                if col in row and pd.notna(row[col]):
                    content_data[col.lower().replace(' ', '_')] = str(row[col]).strip()
            
            # If no specific content columns, use all data
            if not content_data:
                content_data = extract_row_metadata(row, df.columns, main_col)
            
            title_data = {
                "id": generate_clean_id(title_name),
                "name": title_name,
                "level": 4,
                "type": "title",
                "content": content_data,
                "metadata": {
                    "isLeaf": True,
                    "createdAt": int(datetime.now().timestamp() * 1000)
                }
            }
            
            # Find parent author
            parent_path = find_parent_author(hierarchy)
            if parent_path:
                screen, section, category, author = parent_path
                nav = hierarchy["arhamApp"]["navigation"]
                nav[screen]["sections"][section]["categories"][category]["authors"][author]["titles"][title_name] = title_data
            
            # Also add to flat content for easy access
            hierarchy["arhamApp"]["content"][title_data["id"]] = title_data

def generate_clean_id(name):
    """Generate clean ID from name"""
    import re
    # Remove special characters and create clean ID
    clean_name = re.sub(r'[^\w\s-]', '', name.lower())
    clean_name = re.sub(r'[-\s]+', '_', clean_name)
    return f"{clean_name}_{str(uuid.uuid4())[:8]}"

def extract_row_metadata(row, columns, main_col):
    """Extract metadata from row, excluding main column"""
    metadata = {}
    
    for col in columns:
        if col != main_col and col in row and pd.notna(row[col]):
            value = str(row[col]).strip()
            if value:
                field_name = col.lower().replace(' ', '_').replace('/', '_')
                metadata[field_name] = value
    
    metadata["createdAt"] = int(datetime.now().timestamp() * 1000)
    return metadata

def find_or_create_parent(navigation, parent_type):
    """Find or create parent of specified type"""
    for name, data in navigation.items():
        if data.get("type") == parent_type:
            return name
    
    # Create default parent if none found
    if parent_type == "explore_screen":
        default_name = "Default Explore Screen"
        navigation[default_name] = {
            "id": generate_clean_id(default_name),
            "name": default_name,
            "level": 0,
            "type": "explore_screen",
            "sections": {},
            "metadata": {"auto_created": True}
        }
        return default_name
    
    return None

def find_parent_section(hierarchy):
    """Find first available parent section"""
    nav = hierarchy["arhamApp"]["navigation"]
    for screen_name, screen_data in nav.items():
        if screen_data.get("sections"):
            section_name = list(screen_data["sections"].keys())[0]
            return screen_name, section_name
        else:
            # Create default section
            default_section = "Default Section"
            screen_data["sections"][default_section] = {
                "id": generate_clean_id(default_section),
                "name": default_section,
                "level": 1,
                "type": "section",
                "categories": {},
                "metadata": {"auto_created": True}
            }
            return screen_name, default_section
    return None, None

def find_parent_category(hierarchy):
    """Find first available parent category"""
    nav = hierarchy["arhamApp"]["navigation"]
    for screen_name, screen_data in nav.items():
        for section_name, section_data in screen_data.get("sections", {}).items():
            if section_data.get("categories"):
                category_name = list(section_data["categories"].keys())[0]
                return screen_name, section_name, category_name
    return None

def find_parent_author(hierarchy):
    """Find first available parent author"""
    nav = hierarchy["arhamApp"]["navigation"]
    for screen_name, screen_data in nav.items():
        for section_name, section_data in screen_data.get("sections", {}).items():
            for category_name, category_data in section_data.get("categories", {}).items():
                if category_data.get("authors"):
                    author_name = list(category_data["authors"].keys())[0]
                    return screen_name, section_name, category_name, author_name
    return None

def save_multiple_formats(hierarchy, sheets_data):
    """Save data in multiple formats"""
    
    # 1. Full hierarchical JSON
    with open("arham_hierarchical_full.json", 'w', encoding='utf-8') as f:
        json.dump(hierarchy, f, indent=2, ensure_ascii=False)
    print("✅ Hierarchical JSON saved: arham_hierarchical_full.json")
    
    # 2. Navigation-only JSON
    nav_only = {"navigation": hierarchy["arhamApp"]["navigation"]}
    with open("arham_navigation_only.json", 'w', encoding='utf-8') as f:
        json.dump(nav_only, f, indent=2, ensure_ascii=False)
    print("✅ Navigation JSON saved: arham_navigation_only.json")
    
    # 3. Content-only JSON
    content_only = {"content": hierarchy["arhamApp"]["content"]}
    with open("arham_content_only.json", 'w', encoding='utf-8') as f:
        json.dump(content_only, f, indent=2, ensure_ascii=False)
    print("✅ Content JSON saved: arham_content_only.json")
    
    # 4. Flat Firestore format
    flat_data = create_firestore_flat_format(hierarchy)
    with open("arham_firestore_flat.json", 'w', encoding='utf-8') as f:
        json.dump(flat_data, f, indent=2, ensure_ascii=False)
    print("✅ Firestore flat JSON saved: arham_firestore_flat.json")
    
    # 5. Raw sheets data
    raw_data = {}
    for sheet_name, df in sheets_data.items():
        raw_data[sheet_name] = df.to_dict('records')
    
    with open("arham_raw_sheets.json", 'w', encoding='utf-8') as f:
        json.dump(raw_data, f, indent=2, ensure_ascii=False)
    print("✅ Raw sheets JSON saved: arham_raw_sheets.json")

def create_firestore_flat_format(hierarchy):
    """Create flat format suitable for Firestore"""
    
    flat_items = []
    
    # Extract all content items
    for content_id, content_data in hierarchy["arhamApp"]["content"].items():
        flat_item = {
            "id": content_id,
            "title": content_data["name"],
            "content": json.dumps(content_data.get("content", {})),
            "level": content_data["level"],
            "type": content_data["type"],
            "metadata": content_data.get("metadata", {}),
            "createdAt": int(datetime.now().timestamp() * 1000),
            "updatedAt": int(datetime.now().timestamp() * 1000)
        }
        flat_items.append(flat_item)
    
    return flat_items

if __name__ == "__main__":
    print("🔄 ArhamApp Auto-Detect Converter")
    print("=" * 50)
    
    if len(sys.argv) < 2:
        print("Usage: python arham_auto_detect_converter.py <excel_file_path>")
        print("Example: python arham_auto_detect_converter.py arham_data.xlsx")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        sys.exit(1)
    
    success = auto_convert_arham_data(file_path)
    
    if success:
        print("\n🎉 Auto-conversion completed successfully!")
        print("\n📁 Generated files:")
        print("1. arham_hierarchical_full.json - Complete hierarchy")
        print("2. arham_navigation_only.json - Navigation structure")
        print("3. arham_content_only.json - Content only")
        print("4. arham_firestore_flat.json - Firestore upload format")
        print("5. arham_raw_sheets.json - Raw Excel data")
        print("\n📋 Next steps:")
        print("1. Review generated JSON files")
        print("2. Upload arham_firestore_flat.json to Firestore")
        print("3. Use arham_navigation_only.json for app navigation")
    else:
        print("❌ Auto-conversion failed!")
