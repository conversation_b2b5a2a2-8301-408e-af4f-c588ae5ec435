<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArhamApp Live Admin - Direct Integration</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .header h1 {
            font-size: 3em;
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.3em;
            color: #666;
            margin-bottom: 20px;
        }
        
        .connection-status {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .status-connected {
            background: #d4edda;
            color: #155724;
        }
        
        .status-disconnected {
            background: #f8d7da;
            color: #721c24;
        }
        
        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .panel {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            transition: all 0.3s;
        }
        
        .panel:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 50px rgba(0,0,0,0.15);
        }
        
        .panel-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .panel-icon {
            font-size: 2.5em;
            margin-right: 15px;
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .panel-title {
            font-size: 1.5em;
            color: #333;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            font-size: 14px;
            transition: all 0.3s;
            background: white;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #4ECDC4;
            box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 120px;
            font-family: inherit;
        }
        
        .btn {
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin: 5px;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: all 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .btn-success { background: linear-gradient(45deg, #56ab2f, #a8e6cf); }
        .btn-danger { background: linear-gradient(45deg, #ff4757, #ff3838); }
        .btn-warning { background: linear-gradient(45deg, #ffa726, #ff9800); }
        .btn-info { background: linear-gradient(45deg, #667eea, #764ba2); }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .live-preview {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            border-left: 5px solid #4ECDC4;
        }
        
        .preview-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s;
        }
        
        .preview-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .preview-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .preview-meta {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }
        
        .preview-content {
            color: #555;
            line-height: 1.5;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-weight: 600;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            transform: translateX(400px);
            transition: all 0.3s;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.success { background: linear-gradient(45deg, #56ab2f, #a8e6cf); }
        .notification.error { background: linear-gradient(45deg, #ff4757, #ff3838); }
        .notification.info { background: linear-gradient(45deg, #667eea, #764ba2); }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4ECDC4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .firebase-config {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .config-step {
            margin-bottom: 15px;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #4ECDC4;
        }
        
        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
            
            .admin-container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Header -->
        <div class="header">
            <h1>🕉️ ArhamApp Live Admin</h1>
            <p>Direct Integration - No Upload Required!</p>
            <div class="connection-status" id="connectionStatus">
                <span id="statusIcon">🔴</span>
                <span id="statusText">Disconnected</span>
            </div>
        </div>
        
        <!-- Stats Overview -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalContent">0</div>
                <div class="stat-label">Total Content</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="liveUsers">0</div>
                <div class="stat-label">Live Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="todayAdded">0</div>
                <div class="stat-label">Added Today</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="syncStatus">✅</div>
                <div class="stat-label">Sync Status</div>
            </div>
        </div>
        
        <!-- Main Panels -->
        <div class="main-grid">
            <!-- Live Content Manager -->
            <div class="panel">
                <div class="panel-header">
                    <div class="panel-icon">📝</div>
                    <div class="panel-title">Live Content Manager</div>
                </div>
                
                <form id="liveContentForm">
                    <div class="form-group">
                        <label class="form-label">📖 Title/Name *</label>
                        <input type="text" class="form-input" id="contentTitle" placeholder="जैसे: गायत्री मंत्र, हनुमान चालीसा" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">📂 Category *</label>
                        <select class="form-select" id="contentCategory" required>
                            <option value="">Select Category</option>
                            <option value="explorescreen">🏠 Explore Screen</option>
                            <option value="section">📑 Section</option>
                            <option value="category">📂 Category</option>
                            <option value="author">👤 Author</option>
                            <option value="title">📖 Title</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">👤 Author</label>
                        <input type="text" class="form-input" id="contentAuthor" placeholder="आचार्य तुलसी, महावीर स्वामी">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">📄 Content *</label>
                        <textarea class="form-textarea" id="contentText" placeholder="यहाँ मंत्र, श्लोक, भजन या content लिखें..." required></textarea>
                    </div>
                    
                    <button type="button" class="btn btn-success" onclick="addContentLive()">
                        ⚡ Add to App Instantly
                    </button>
                    <button type="button" class="btn btn-info" onclick="previewContent()">
                        👀 Preview
                    </button>
                </form>
            </div>
            
            <!-- Firebase Integration -->
            <div class="panel">
                <div class="panel-header">
                    <div class="panel-icon">🔥</div>
                    <div class="panel-title">Firebase Integration</div>
                </div>
                
                <div class="firebase-config">
                    <h4>🚀 One-Time Setup (बस एक बार करना है)</h4>
                    
                    <div class="config-step">
                        <strong>Step 1:</strong> Firebase Console में जाएं
                        <button class="btn btn-info" onclick="openFirebaseConsole()">
                            🔗 Open Firebase Console
                        </button>
                    </div>
                    
                    <div class="config-step">
                        <strong>Step 2:</strong> Project ID enter करें
                        <input type="text" class="form-input" id="firebaseProjectId" placeholder="your-project-id">
                    </div>
                    
                    <div class="config-step">
                        <strong>Step 3:</strong> API Key enter करें
                        <input type="text" class="form-input" id="firebaseApiKey" placeholder="your-api-key">
                    </div>
                    
                    <button class="btn btn-success" onclick="connectFirebase()">
                        🔗 Connect to Firebase
                    </button>
                    <button class="btn btn-warning" onclick="testConnection()">
                        🧪 Test Connection
                    </button>
                </div>
                
                <div class="form-group">
                    <label class="form-label">📊 Collection Name</label>
                    <input type="text" class="form-input" id="collectionName" value="spiritual_content" placeholder="Collection name">
                </div>
                
                <div style="margin-top: 20px;">
                    <button class="btn btn-info" onclick="syncAllData()">
                        🔄 Sync All Data
                    </button>
                    <button class="btn btn-warning" onclick="backupData()">
                        💾 Create Backup
                    </button>
                </div>
            </div>
        </div>

        <!-- Live Preview Panel -->
        <div class="panel" style="grid-column: 1 / -1;">
            <div class="panel-header">
                <div class="panel-icon">📱</div>
                <div class="panel-title">Live App Preview</div>
            </div>

            <div class="live-preview" id="livePreview">
                <p style="text-align: center; color: #666; padding: 40px;">
                    Content will appear here as you add it. Your app users will see this instantly!
                </p>
            </div>
        </div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification"></div>

    <script type="module">
        // Firebase imports
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore, collection, addDoc, getDocs, onSnapshot, query, orderBy } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';

        // Global variables
        let firebaseApp = null;
        let db = null;
        let isConnected = false;
        let liveData = [];
        let unsubscribe = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadSavedConfig();
            updateStats();
        });

        // Firebase connection functions
        window.connectFirebase = async function() {
            const projectId = document.getElementById('firebaseProjectId').value.trim();
            const apiKey = document.getElementById('firebaseApiKey').value.trim();

            if (!projectId || !apiKey) {
                showNotification('Please enter Project ID and API Key!', 'error');
                return;
            }

            try {
                const firebaseConfig = {
                    apiKey: apiKey,
                    authDomain: `${projectId}.firebaseapp.com`,
                    projectId: projectId,
                    storageBucket: `${projectId}.appspot.com`,
                    messagingSenderId: "123456789",
                    appId: "1:123456789:web:abcdef123456"
                };

                firebaseApp = initializeApp(firebaseConfig);
                db = getFirestore(firebaseApp);

                // Save config
                localStorage.setItem('arhamFirebaseConfig', JSON.stringify({
                    projectId: projectId,
                    apiKey: apiKey
                }));

                // Test connection
                await testConnection();

                if (isConnected) {
                    setupLiveSync();
                    showNotification('🎉 Connected to Firebase successfully!', 'success');
                }

            } catch (error) {
                console.error('Firebase connection error:', error);
                showNotification('❌ Connection failed: ' + error.message, 'error');
            }
        };

        window.testConnection = async function() {
            if (!db) {
                showNotification('Please connect to Firebase first!', 'error');
                return;
            }

            try {
                const collectionName = document.getElementById('collectionName').value || 'spiritual_content';
                const testQuery = query(collection(db, collectionName));
                await getDocs(testQuery);

                isConnected = true;
                updateConnectionStatus(true);
                showNotification('✅ Connection test successful!', 'success');

            } catch (error) {
                isConnected = false;
                updateConnectionStatus(false);
                showNotification('❌ Connection test failed: ' + error.message, 'error');
            }
        };

        function updateConnectionStatus(connected) {
            const statusIcon = document.getElementById('statusIcon');
            const statusText = document.getElementById('statusText');
            const connectionStatus = document.getElementById('connectionStatus');

            if (connected) {
                statusIcon.textContent = '🟢';
                statusText.textContent = 'Connected & Live';
                connectionStatus.className = 'connection-status status-connected';
            } else {
                statusIcon.textContent = '🔴';
                statusText.textContent = 'Disconnected';
                connectionStatus.className = 'connection-status status-disconnected';
            }
        }

        function setupLiveSync() {
            if (!db) return;

            const collectionName = document.getElementById('collectionName').value || 'spiritual_content';
            const q = query(collection(db, collectionName), orderBy('createdAt', 'desc'));

            // Real-time listener
            unsubscribe = onSnapshot(q, (snapshot) => {
                liveData = [];
                snapshot.forEach((doc) => {
                    liveData.push({ id: doc.id, ...doc.data() });
                });

                updateLivePreview();
                updateStats();
                showNotification('📱 App data synced!', 'info');
            });
        }

        // Content management functions
        window.addContentLive = async function() {
            if (!isConnected) {
                showNotification('Please connect to Firebase first!', 'error');
                return;
            }

            const title = document.getElementById('contentTitle').value.trim();
            const category = document.getElementById('contentCategory').value;
            const author = document.getElementById('contentAuthor').value.trim();
            const content = document.getElementById('contentText').value.trim();

            if (!title || !category || !content) {
                showNotification('Please fill in all required fields!', 'error');
                return;
            }

            try {
                const newItem = {
                    title: title,
                    category: category,
                    author: author || 'Unknown',
                    content: content,
                    language: 'hindi',
                    type: 'spiritual',
                    createdAt: Date.now(),
                    updatedAt: Date.now(),
                    isActive: true
                };

                const collectionName = document.getElementById('collectionName').value || 'spiritual_content';
                await addDoc(collection(db, collectionName), newItem);

                // Clear form
                document.getElementById('liveContentForm').reset();

                showNotification('🎉 Content added to app instantly!', 'success');

                // Update today's count
                const todayCount = parseInt(document.getElementById('todayAdded').textContent) + 1;
                document.getElementById('todayAdded').textContent = todayCount;

            } catch (error) {
                console.error('Error adding content:', error);
                showNotification('❌ Failed to add content: ' + error.message, 'error');
            }
        };

        window.previewContent = function() {
            const title = document.getElementById('contentTitle').value.trim();
            const content = document.getElementById('contentText').value.trim();
            const author = document.getElementById('contentAuthor').value.trim();

            if (!title || !content) {
                showNotification('Please enter title and content to preview!', 'error');
                return;
            }

            const preview = `
                <div style="background: white; padding: 20px; border-radius: 15px; box-shadow: 0 5px 20px rgba(0,0,0,0.1); margin: 20px 0; border-left: 5px solid #4ECDC4;">
                    <h3 style="color: #333; margin-bottom: 10px; font-size: 1.3em;">📖 ${title}</h3>
                    <p style="color: #666; font-size: 0.9em; margin-bottom: 15px;">👤 ${author || 'Unknown Author'}</p>
                    <div style="color: #555; line-height: 1.6; background: #f8f9fa; padding: 15px; border-radius: 10px;">${content}</div>
                    <div style="margin-top: 15px; font-size: 0.8em; color: #999;">📱 This is how it will appear in your app</div>
                </div>
            `;

            const previewWindow = window.open('', '_blank', 'width=600,height=500');
            previewWindow.document.write(`
                <html>
                    <head>
                        <title>ArhamApp Preview</title>
                        <style>
                            body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
                            h2 { text-align: center; color: #333; margin-bottom: 30px; }
                        </style>
                    </head>
                    <body>
                        <h2>📱 ArhamApp Live Preview</h2>
                        ${preview}
                    </body>
                </html>
            `);
        };

        function updateLivePreview() {
            const previewContainer = document.getElementById('livePreview');

            if (liveData.length === 0) {
                previewContainer.innerHTML = '<p style="text-align: center; color: #666; padding: 40px;">No content yet. Add some content to see it here instantly!</p>';
                return;
            }

            let html = '<h4 style="margin-bottom: 20px; color: #333;">📱 Live App Content (Real-time)</h4>';

            liveData.slice(0, 5).forEach(item => {
                html += `
                    <div class="preview-item">
                        <div class="preview-title">📖 ${item.title}</div>
                        <div class="preview-meta">👤 ${item.author} • 📂 ${item.category} • 🕒 ${new Date(item.createdAt).toLocaleString('hi-IN')}</div>
                        <div class="preview-content">${item.content.substring(0, 100)}${item.content.length > 100 ? '...' : ''}</div>
                    </div>
                `;
            });

            if (liveData.length > 5) {
                html += `<p style="text-align: center; color: #666; margin-top: 15px;">और ${liveData.length - 5} items...</p>`;
            }

            previewContainer.innerHTML = html;
        }

        function updateStats() {
            document.getElementById('totalContent').textContent = liveData.length;
            document.getElementById('liveUsers').textContent = Math.floor(Math.random() * 50) + 10; // Simulated

            // Count today's additions
            const today = new Date().toDateString();
            const todayItems = liveData.filter(item => {
                const itemDate = new Date(item.createdAt).toDateString();
                return itemDate === today;
            });
            document.getElementById('todayAdded').textContent = todayItems.length;

            document.getElementById('syncStatus').textContent = isConnected ? '✅' : '❌';
        }
