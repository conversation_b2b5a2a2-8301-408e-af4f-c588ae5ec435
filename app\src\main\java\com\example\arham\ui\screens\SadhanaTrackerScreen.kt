package com.example.arham.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items

import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.arham.data.models.*
import com.example.arham.ui.theme.eczarFamily
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SadhanaTrackerScreen(navController: NavController, isDarkMode: Boolean) {
    var viewMode by remember { mutableStateOf(HabitViewMode.WEEK_VIEW) }
    var showBottomSheet by remember { mutableStateOf(false) }
    val bottomSheetState = rememberModalBottomSheetState()
    
    // Sample data - in real app this would come from repository
    val habits = remember {
        mutableStateListOf(
            Habit(
                id = "1",
                name = "Mala",
                category = HabitCategory.MEDITATION,
                color = Color(0xFF4A90E2)
            ),
            Habit(
                id = "2", 
                name = "Samayik",
                category = HabitCategory.MEDITATION,
                color = Color(0xFFD2691E)
            )
        )
    }
    
    val completions = remember {
        mutableStateListOf<HabitCompletion>().apply {
            // Sample completions for Mala
            addAll(listOf(
                HabitCompletion("1", LocalDate.now().minusDays(4)),
                HabitCompletion("1", LocalDate.now().minusDays(3)),
                HabitCompletion("1", LocalDate.now().minusDays(2)),
                HabitCompletion("1", LocalDate.now().minusDays(1))
            ))
            // Sample completions for Samayik
            addAll(listOf(
                HabitCompletion("2", LocalDate.now().minusDays(6)),
                HabitCompletion("2", LocalDate.now().minusDays(5)),
                HabitCompletion("2", LocalDate.now().minusDays(4)),
                HabitCompletion("2", LocalDate.now().minusDays(3)),
                HabitCompletion("2", LocalDate.now().minusDays(2)),
                HabitCompletion("2", LocalDate.now().minusDays(1)),
                HabitCompletion("2", LocalDate.now())
            ))
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // Top section with view toggle
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.Center
            ) {
                // Week View / Grid View Toggle
                Row(
                    modifier = Modifier
                        .background(
                            color = MaterialTheme.colorScheme.surfaceVariant,
                            shape = RoundedCornerShape(25.dp)
                        )
                        .padding(4.dp)
                ) {
                    // Week View Button
                    Box(
                        modifier = Modifier
                            .background(
                                color = if (viewMode == HabitViewMode.WEEK_VIEW) 
                                    MaterialTheme.colorScheme.surface else Color.Transparent,
                                shape = RoundedCornerShape(20.dp)
                            )
                            .clickable { viewMode = HabitViewMode.WEEK_VIEW }
                            .padding(horizontal = 20.dp, vertical = 8.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "Week View",
                            fontSize = 14.sp,
                            fontWeight = if (viewMode == HabitViewMode.WEEK_VIEW) FontWeight.Medium else FontWeight.Normal,
                            color = if (viewMode == HabitViewMode.WEEK_VIEW) 
                                MaterialTheme.colorScheme.onSurface else MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                    
                    // Grid View Button
                    Box(
                        modifier = Modifier
                            .background(
                                color = if (viewMode == HabitViewMode.GRID_VIEW) 
                                    MaterialTheme.colorScheme.surface else Color.Transparent,
                                shape = RoundedCornerShape(20.dp)
                            )
                            .clickable { viewMode = HabitViewMode.GRID_VIEW }
                            .padding(horizontal = 20.dp, vertical = 8.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "Grid View",
                            fontSize = 14.sp,
                            fontWeight = if (viewMode == HabitViewMode.GRID_VIEW) FontWeight.Medium else FontWeight.Normal,
                            color = if (viewMode == HabitViewMode.GRID_VIEW) 
                                MaterialTheme.colorScheme.onSurface else MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }

            // Habits List - Full width cards like screenshot
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 24.dp),
                contentPadding = PaddingValues(bottom = 100.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                items(habits) { habit ->
                    HabitCard(
                        habit = habit,
                        completions = completions.filter { it.habitId == habit.id },
                        viewMode = viewMode,
                        onDayToggle = { date ->
                            val existingCompletion = completions.find {
                                it.habitId == habit.id && it.date == date
                            }
                            if (existingCompletion != null) {
                                completions.remove(existingCompletion)
                            } else {
                                completions.add(HabitCompletion(habit.id, date))
                            }
                        },
                        onHabitClick = {
                            // No action - card is not clickable
                        },
                        onOptionsClick = {
                            showBottomSheet = true
                        }
                    )
                }
            }
        }

        // Floating Action Button
        FloatingActionButton(
            onClick = {
                navController.navigate("addhabit")
            },
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(24.dp),
            containerColor = MaterialTheme.colorScheme.surface,
            contentColor = MaterialTheme.colorScheme.onSurface
        ) {
            Icon(
                Icons.Default.Add,
                contentDescription = "Add Habit",
                modifier = Modifier.size(24.dp)
            )
        }
    }

    // Options Bottom Sheet
    if (showBottomSheet) {
        ModalBottomSheet(
            onDismissRequest = { showBottomSheet = false },
            sheetState = bottomSheetState,
            containerColor = MaterialTheme.colorScheme.surface
        ) {
            OptionsBottomSheet(
                onDismiss = { showBottomSheet = false },
                onDeleteHabit = { /* Handle delete */ },
                onHabitDetails = { /* Handle details */ },
                onReorderHabits = { /* Handle reorder */ }
            )
        }
    }
}

@Composable
fun HabitCard(
    habit: Habit,
    completions: List<HabitCompletion>,
    viewMode: HabitViewMode,
    onDayToggle: (LocalDate) -> Unit,
    onHabitClick: () -> Unit,
    onOptionsClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(18.dp)
        ) {
            // Header with habit name and options
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Category icon - just colored square without emoji
                    Box(
                        modifier = Modifier
                            .size(32.dp)
                            .background(
                                color = habit.color,
                                shape = RoundedCornerShape(8.dp)
                            )
                    )

                    Spacer(modifier = Modifier.width(12.dp))

                    Text(
                        text = habit.name,
                        fontSize = 30.sp,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }

                IconButton(
                    onClick = onOptionsClick,
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        Icons.Default.MoreVert,
                        contentDescription = "Options",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Day labels
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                val dayLabels = listOf("Thu", "Fri", "Sat", "Sun", "Mon", "Tue", "Wed")
                dayLabels.forEach { day ->
                    Text(
                        text = day,
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.weight(1f)
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Day circles with numbers
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                (6 downTo 0).forEach { daysAgo ->
                    val date = LocalDate.now().minusDays(daysAgo.toLong())
                    val isCompleted = completions.any { it.date == date && it.isCompleted }

                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .aspectRatio(1f)
                            .padding(horizontal = 3.dp)
                            .background(
                                color = if (isCompleted) habit.color else MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                                shape = CircleShape
                            )
                            .clickable { onDayToggle(date) },
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = String.format("%02d", date.dayOfMonth),
                            fontSize = 13.sp,
                            fontWeight = FontWeight.Medium,
                            color = if (isCompleted) Color.White else MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
        }
    }
}

// Helper function to calculate current streak
private fun calculateCurrentStreak(completions: List<HabitCompletion>): Int {
    val sortedCompletions = completions.filter { it.isCompleted }.sortedByDescending { it.date }
    if (sortedCompletions.isEmpty()) return 0

    var streak = 0
    var currentDate = LocalDate.now()

    for (completion in sortedCompletions) {
        if (completion.date == currentDate || completion.date == currentDate.minusDays(1)) {
            streak++
            currentDate = completion.date.minusDays(1)
        } else {
            break
        }
    }

    return streak
}

@Composable
fun WeekViewContent(
    habit: Habit,
    completions: List<HabitCompletion>,
    onDayToggle: (LocalDate) -> Unit
) {
    Column {
        // Day labels
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            val dayLabels = listOf("Thu", "Fri", "Sat", "Sun", "Mon", "Tue", "Wed")
            dayLabels.forEach { day ->
                Text(
                    text = day,
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.weight(1f),
                    textAlign = androidx.compose.ui.text.style.TextAlign.Center
                )
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Completion circles
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            (6 downTo 0).forEach { daysAgo ->
                val date = LocalDate.now().minusDays(daysAgo.toLong())
                val isCompleted = completions.any { it.date == date && it.isCompleted }

                Box(
                    modifier = Modifier
                        .size(32.dp)
                        .background(
                            color = if (isCompleted) habit.color else MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                            shape = CircleShape
                        )
                        .clickable { onDayToggle(date) },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = String.format("%02d", date.dayOfMonth),
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = if (isCompleted) Color.White else MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

@Composable
fun GridViewContent(
    habit: Habit,
    completions: List<HabitCompletion>
) {
    // Year grid showing completion pattern
    Column {
        // Grid of small squares representing days
        repeat(10) { row ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(2.dp)
            ) {
                repeat(37) { col ->
                    val dayIndex = row * 37 + col
                    val date = LocalDate.now().minusDays(dayIndex.toLong())
                    val isCompleted = completions.any { it.date == date && it.isCompleted }

                    Box(
                        modifier = Modifier
                            .size(6.dp)
                            .background(
                                color = if (isCompleted) habit.color else MaterialTheme.colorScheme.outline.copy(alpha = 0.2f),
                                shape = RoundedCornerShape(1.dp)
                            )
                    )
                }
            }
            if (row < 9) Spacer(modifier = Modifier.height(2.dp))
        }
    }
}

@Composable
fun OptionsBottomSheet(
    onDismiss: () -> Unit,
    onDeleteHabit: () -> Unit,
    onHabitDetails: () -> Unit,
    onReorderHabits: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 27.dp, vertical = 16.dp)
    ) {
        // Handle bar
        Box(
            modifier = Modifier
                .width(40.dp)
                .height(4.dp)
                .background(
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.4f),
                    shape = RoundedCornerShape(2.dp)
                )
                .align(Alignment.CenterHorizontally)
        )

        Spacer(modifier = Modifier.height(20.dp))

        Text(
            text = "Options",
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.align(Alignment.CenterHorizontally)
        )

        Spacer(modifier = Modifier.height(20.dp))

        // Delete habit
        OptionItem(
            title = "Delete habit",
            onClick = {
                onDeleteHabit()
                onDismiss()
            }
        )

        // Habit Details & Analytics
        OptionItem(
            title = "Habit Details & Analytics",
            subtitle = "Get detailed analytics for the habit. Track your streak, progress and consistency.",
            onClick = {
                onHabitDetails()
                onDismiss()
            }
        )



        // Reorder Habits
        OptionItem(
            title = "Reorder Habits",
            onClick = {
                onReorderHabits()
                onDismiss()
            }
        )

        Spacer(modifier = Modifier.height(20.dp))
    }
}

@Composable
fun OptionItem(
    title: String,
    subtitle: String? = null,
    onClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 12.dp)
    ) {
        Text(
            text = title,
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurface
        )

        if (subtitle != null) {
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = subtitle,
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                lineHeight = 20.sp
            )
        }
    }
}
