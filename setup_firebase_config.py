#!/usr/bin/env python3
"""
Firebase Configuration Setup for ArhamApp
"""

import re
import os

def setup_firebase_config():
    """
    Interactive setup for Firebase configuration
    """
    
    print("🔥 Firebase Configuration Setup")
    print("=" * 50)
    print("1. Go to https://console.firebase.google.com/")
    print("2. Select your project")
    print("3. Go to Project Settings > General")
    print("4. In 'Your apps' section, add a Web app")
    print("5. Copy the config object")
    print()
    
    print("Enter your Firebase configuration:")
    print("(You can paste the entire config object or enter values one by one)")
    print()
    
    # Try to get full config first
    full_config = input("Paste full Firebase config (or press Enter to enter manually): ").strip()
    
    if full_config:
        # Extract values from pasted config
        config = extract_config_from_text(full_config)
    else:
        # Manual entry
        config = {}
        config['apiKey'] = input("API Key: ").strip()
        config['authDomain'] = input("Auth Domain: ").strip()
        config['projectId'] = input("Project ID: ").strip()
        config['storageBucket'] = input("Storage Bucket: ").strip()
        config['messagingSenderId'] = input("Messaging Sender ID: ").strip()
        config['appId'] = input("App ID: ").strip()
    
    # Update HTML file
    update_html_config(config)
    
    print("\n✅ Configuration updated successfully!")
    print("🌐 You can now open firestore_web_upload.html in your browser")

def extract_config_from_text(text):
    """
    Extract Firebase config from pasted text
    """
    
    config = {}
    
    # Patterns to match Firebase config values
    patterns = {
        'apiKey': r'apiKey["\s]*:["\s]*([^"]+)',
        'authDomain': r'authDomain["\s]*:["\s]*([^"]+)',
        'projectId': r'projectId["\s]*:["\s]*([^"]+)',
        'storageBucket': r'storageBucket["\s]*:["\s]*([^"]+)',
        'messagingSenderId': r'messagingSenderId["\s]*:["\s]*([^"]+)',
        'appId': r'appId["\s]*:["\s]*([^"]+)'
    }
    
    for key, pattern in patterns.items():
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            config[key] = match.group(1).strip('"')
    
    return config

def update_html_config(config):
    """
    Update HTML file with Firebase configuration
    """
    
    html_file = "firestore_web_upload.html"
    
    if not os.path.exists(html_file):
        print(f"❌ HTML file not found: {html_file}")
        return
    
    # Read HTML file
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Create new config string
    config_str = f'''const firebaseConfig = {{
            apiKey: "{config.get('apiKey', 'your-api-key')}",
            authDomain: "{config.get('authDomain', 'your-project.firebaseapp.com')}",
            projectId: "{config.get('projectId', 'your-project-id')}",
            storageBucket: "{config.get('storageBucket', 'your-project.appspot.com')}",
            messagingSenderId: "{config.get('messagingSenderId', '123456789')}",
            appId: "{config.get('appId', 'your-app-id')}"
        }};'''
    
    # Replace config in HTML
    pattern = r'const firebaseConfig = \{[^}]+\};'
    updated_content = re.sub(pattern, config_str, content, flags=re.DOTALL)
    
    # Write updated HTML
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    print(f"✅ Updated {html_file} with your Firebase configuration")

def create_quick_upload_script():
    """
    Create a quick upload script
    """
    
    script_content = '''<!DOCTYPE html>
<html>
<head>
    <title>Quick Firebase Upload</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .log { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 Quick Firebase Upload</h1>
        <input type="file" id="fileInput" accept=".json">
        <button onclick="uploadFile()">Upload to Firestore</button>
        <div id="log" class="log"></div>
    </div>
    
    <script type="module">
        // Add your Firebase config here
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore, collection, doc, setDoc } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        
        const firebaseConfig = {
            // Your config here
        };
        
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);
        
        window.uploadFile = async function() {
            const fileInput = document.getElementById('fileInput');
            const log = document.getElementById('log');
            
            if (!fileInput.files[0]) {
                log.innerHTML += 'Please select a file<br>';
                return;
            }
            
            const file = fileInput.files[0];
            const reader = new FileReader();
            
            reader.onload = async function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    log.innerHTML += `Found ${data.length} items<br>`;
                    
                    for (let i = 0; i < data.length; i++) {
                        const item = data[i];
                        await setDoc(doc(db, 'spiritual_content', item.id), item);
                        
                        if (i % 10 === 0) {
                            log.innerHTML += `Uploaded ${i + 1}/${data.length}<br>`;
                            log.scrollTop = log.scrollHeight;
                        }
                    }
                    
                    log.innerHTML += 'Upload completed!<br>';
                } catch (error) {
                    log.innerHTML += `Error: ${error.message}<br>`;
                }
            };
            
            reader.readAsText(file);
        };
    </script>
</body>
</html>'''
    
    with open('quick_upload.html', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ Created quick_upload.html")

if __name__ == "__main__":
    setup_firebase_config()
    
    create_quick = input("\nCreate a quick upload HTML file? (y/n): ").strip().lower()
    if create_quick == 'y':
        create_quick_upload_script()
    
    print("\n🎉 Setup completed!")
    print("Next steps:")
    print("1. Open firestore_web_upload.html in your browser")
    print("2. Drag and drop your arham_firestore_data.json file")
    print("3. Click 'Upload to Firestore'")
    
    input("Press Enter to exit...")
