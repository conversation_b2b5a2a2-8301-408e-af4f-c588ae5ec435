package com.example.arham.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.example.arham.firebase.FirebaseManager
import com.example.arham.ui.screens.AuthScreen

@Composable
fun AuthWrapper(
    navController: NavController,
    isDarkMode: Boolean,
    content: @Composable () -> Unit
) {
    var isCheckingAuth by remember { mutableStateOf(true) }
    var isUserAuthenticated by remember { mutableStateOf(false) }
    
    LaunchedEffect(Unit) {
        // Set up authentication state listener
        val authStateListener = com.google.firebase.auth.FirebaseAuth.AuthStateListener { auth ->
            isUserAuthenticated = auth.currentUser != null
            isCheckingAuth = false
        }
        
        // Add the listener
        FirebaseManager.auth.addAuthStateListener(authStateListener)
        
        // Initial check
        val currentUser = FirebaseManager.auth.currentUser
        isUserAuthenticated = currentUser != null
        isCheckingAuth = false
    }
    
    when {
        isCheckingAuth -> {
            // Show loading screen while checking authentication
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator(
                        color = MaterialTheme.colorScheme.primary
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "Loading...",
                        color = MaterialTheme.colorScheme.onBackground
                    )
                }
            }
        }
        
        !isUserAuthenticated -> {
            // Show authentication screen
            AuthScreen(navController, isDarkMode)
        }
        
        else -> {
            // Show main app content
            content()
        }
    }
}
