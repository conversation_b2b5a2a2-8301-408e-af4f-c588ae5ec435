package com.example.arham.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.DarkMode
import androidx.compose.material.icons.filled.LightMode
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.arham.ui.theme.eczarFamily


@Composable
fun TodayScreen(navController: NavController, isDarkMode: Boolean, onToggleTheme: () -> Unit) {
    // Create gradient brush for the logo
    val brush = Brush.linearGradient(
        colors = listOf(
            MaterialTheme.colorScheme.primary,
            MaterialTheme.colorScheme.secondary
        )
    )

    var showStatusViewer by remember { mutableStateOf(false) }
    var selectedStatusIndex by remember { mutableStateOf(0) }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            // Top Bar with Logo and Theme Toggle (KEEPING EXACTLY AS IS)
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // App Logo
                Text(
                    text = "अर्हम्",
                    style = MaterialTheme.typography.titleLarge.copy(
                        brush = brush
                    )
                )

                // Theme Toggle Button
                IconButton(
                    onClick = onToggleTheme,
                    modifier = Modifier
                        .size(40.dp)
                        .background(
                            color = MaterialTheme.colorScheme.surfaceVariant,
                            shape = CircleShape
                        )
                ) {
                    Icon(
                        imageVector = if (isDarkMode) Icons.Default.LightMode else Icons.Default.DarkMode,
                        contentDescription = if (isDarkMode) "Switch to Light Mode" else "Switch to Dark Mode",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            // Story-style circles section
            StoryCirclesSection(
                onStatusClick = { index ->
                    selectedStatusIndex = index
                    showStatusViewer = true
                }
            )

            Spacer(modifier = Modifier.height(24.dp))

            // Sadhana Tracker Card
            SadhanaTrackerCard(navController = navController)

            Spacer(modifier = Modifier.height(24.dp))

            // Check Back Tomorrow Card
            CheckBackTomorrowCard()

            Spacer(modifier = Modifier.height(24.dp))

            // Verse of the Day Section
            VerseOfTheDaySection()

            Spacer(modifier = Modifier.height(16.dp))



            // View ReadScreen Data Button
            Button(
                onClick = { navController.navigate("read") },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 24.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Text("📖 View ReadScreen Data", fontFamily = eczarFamily)
            }

            // Bottom padding for navigation bar
            Spacer(modifier = Modifier.height(100.dp))
        }

        // Status Viewer as full-screen overlay
        if (showStatusViewer) {
            StatusViewer(
                initialIndex = selectedStatusIndex,
                onClose = { showStatusViewer = false }
            )
        }
    }
}

@Composable
fun StoryCirclesSection(onStatusClick: (Int) -> Unit) {
    val statusItems = listOf(
        StatusItem(
            title = "Today",
            isToday = true,
            stories = listOf(
                StatusStory("आज का विचार", "सत्य, अहिंसा और करुणा के मार्ग पर चलना ही जीवन की सच्ची सफलता है।", Color(0xFFFF6B6B)),
                StatusStory("प्रेरणा", "हर दिन एक नया अवसर है अपने आप को बेहतर बनाने का।", Color(0xFFFF8E53)),
                StatusStory("शांति", "मन की शांति सबसे बड़ा खजाना है।", Color(0xFFFF6B9D))
            )
        ),
        StatusItem(
            title = "Jul 8",
            isToday = false,
            stories = listOf(
                StatusStory("ध्यान", "ध्यान से मन को शुद्ध करें और आत्मा की शांति पाएं।", Color(0xFF4ECDC4)),
                StatusStory("सेवा", "दूसरों की सेवा करना सबसे बड़ा धर्म है।", Color(0xFF45B7D1))
            )
        ),
        StatusItem(
            title = "Jul 7",
            isToday = false,
            stories = listOf(
                StatusStory("ज्ञान", "ज्ञान ही वह प्रकाश है जो अंधकार को मिटाता है।", Color(0xFF96CEB4)),
                StatusStory("विनम्रता", "विनम्रता सबसे बड़ा गुण है।", Color(0xFFFECA57)),
                StatusStory("धैर्य", "धैर्य रखने से हर समस्या का समाधान मिलता है।", Color(0xFFE17055))
            )
        ),
        StatusItem(
            title = "Jul 6",
            isToday = false,
            stories = listOf(
                StatusStory("प्रेम", "प्रेम ही जीवन का आधार है।", Color(0xFF74B9FF)),
                StatusStory("क्षमा", "क्षमा करना दिव्य गुण है।", Color(0xFDA085))
            )
        ),
        StatusItem(
            title = "Jul 5",
            isToday = false,
            stories = listOf(
                StatusStory("संयम", "संयम से जीवन में संतुलन आता है।", Color(0xFF00B894)),
                StatusStory("सत्य", "सत्य बोलना सबसे बड़ा तप है।", Color(0xFF6C5CE7))
            )
        )
    )

    LazyRow(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        items(statusItems.size) { index ->
            StatusCircle(
                status = statusItems[index],
                onClick = {
                    println("Status circle clicked: ${statusItems[index].title}") // Debug log
                    onStatusClick(index)
                }
            )
        }
    }
}

@Composable
fun StatusCircle(status: StatusItem, onClick: () -> Unit) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .width(80.dp)
            .clickable { onClick() }
    ) {
        Box(
            modifier = Modifier
                .size(70.dp)
                .background(
                    brush = if (status.isToday) {
                        Brush.radialGradient(
                            colors = listOf(
                                Color(0xFFFF6B6B),
                                Color(0xFFFF8E53),
                                Color(0xFFFF6B9D)
                            )
                        )
                    } else {
                        // Create gradient from first story color
                        val firstColor = status.stories.firstOrNull()?.backgroundColor ?: Color.Gray
                        Brush.radialGradient(
                            colors = listOf(firstColor, firstColor.copy(alpha = 0.8f))
                        )
                    },
                    shape = CircleShape
                )
                .border(
                    width = if (status.isToday) 3.dp else 2.dp,
                    color = if (status.isToday) Color.White else MaterialTheme.colorScheme.primary,
                    shape = CircleShape
                ),
            contentAlignment = Alignment.Center
        ) {
            if (status.isToday) {
                Icon(
                    imageVector = Icons.Default.PlayArrow,
                    contentDescription = "Today",
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            } else {
                // Show story count
                Text(
                    text = "${status.stories.size}",
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    fontFamily = eczarFamily
                )
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = status.title,
            fontSize = 12.sp,
            fontFamily = eczarFamily,
            color = MaterialTheme.colorScheme.onBackground,
            fontWeight = if (status.isToday) FontWeight.Bold else FontWeight.Normal
        )
    }
}

@Composable
fun SadhanaTrackerCard(navController: NavController) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .clickable { navController.navigate("sadhanatracker") },
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = "साधना ट्रैकर",
                    fontSize = 24.sp,
                    fontFamily = eczarFamily,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface,
                    lineHeight = 28.sp
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = "अपनी दैनिक साधना का\nरिकॉर्ड रखें",
                    fontSize = 14.sp,
                    fontFamily = eczarFamily,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    lineHeight = 18.sp
                )
            }

            // Progress indicators showing recent activity
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Row(
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    repeat(5) { index ->
                        Box(
                            modifier = Modifier
                                .size(12.dp)
                                .background(
                                    color = if (index < 3) MaterialTheme.colorScheme.primary
                                           else MaterialTheme.colorScheme.outline.copy(alpha = 0.3f),
                                    shape = CircleShape
                                )
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = "3/5 दिन",
                    fontSize = 12.sp,
                    fontFamily = eczarFamily,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
        }
    }
}

@Composable
fun CheckBackTomorrowCard() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = "CHECK BACK\nTOMORROW",
                    fontSize = 24.sp,
                    fontFamily = eczarFamily,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface,
                    lineHeight = 28.sp
                )

                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = "We're busy preparing a\nnew experience for you",
                    fontSize = 14.sp,
                    fontFamily = eczarFamily,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    lineHeight = 18.sp
                )
            }

            // Placeholder for image/video thumbnail
            Box(
                modifier = Modifier
                    .size(80.dp)
                    .background(
                        color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(12.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.PlayArrow,
                    contentDescription = "Play",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(32.dp)
                )
            }
        }
    }
}

@Composable
fun VerseOfTheDaySection() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp)
        ) {
            Text(
                text = "Verse of the Day",
                fontSize = 14.sp,
                fontFamily = eczarFamily,
                color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f),
                fontWeight = FontWeight.Normal
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = "आचार्य श्री तुलसी 6:1-3",
                fontSize = 20.sp,
                fontFamily = eczarFamily,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "\"हे बालकों! अपने माता-पिता की आज्ञा का पालन करो, क्योंकि यह उचित है। अपने पिता और माता का सम्मान करो\" - यह पहली आज्ञा है जिसके साथ प्रतिज्ञा है - \"जिससे तुम्हारा कल्याण हो और तुम पृथ्वी पर दीर्घायु हो।\"",
                fontSize = 16.sp,
                fontFamily = eczarFamily,
                color = MaterialTheme.colorScheme.onSurface,
                lineHeight = 24.sp
            )
        }
    }
}

@Composable
fun StatusViewer(
    initialIndex: Int,
    onClose: () -> Unit
) {
    var currentStatusIndex by remember { mutableStateOf(initialIndex) }
    var currentStoryIndex by remember { mutableStateOf(0) }

    // Define status data inside the viewer
    val statusItems = listOf(
        StatusItem(
            title = "Today",
            isToday = true,
            stories = listOf(
                StatusStory("आज का विचार", "सत्य, अहिंसा और करुणा के मार्ग पर चलना ही जीवन की सच्ची सफलता है।", Color(0xFFFF6B6B)),
                StatusStory("प्रेरणा", "हर दिन एक नया अवसर है अपने आप को बेहतर बनाने का।", Color(0xFFFF8E53)),
                StatusStory("शांति", "मन की शांति सबसे बड़ा खजाना है।", Color(0xFFFF6B9D))
            )
        ),
        StatusItem(
            title = "Jul 8",
            isToday = false,
            stories = listOf(
                StatusStory("ध्यान", "ध्यान से मन को शुद्ध करें और आत्मा की शांति पाएं।", Color(0xFF4ECDC4)),
                StatusStory("सेवा", "दूसरों की सेवा करना सबसे बड़ा धर्म है।", Color(0xFF45B7D1))
            )
        ),
        StatusItem(
            title = "Jul 7",
            isToday = false,
            stories = listOf(
                StatusStory("ज्ञान", "ज्ञान ही वह प्रकाश है जो अंधकार को मिटाता है।", Color(0xFF96CEB4)),
                StatusStory("विनम्रता", "विनम्रता सबसे बड़ा गुण है।", Color(0xFFFECA57)),
                StatusStory("धैर्य", "धैर्य रखने से हर समस्या का समाधान मिलता है।", Color(0xFFE17055))
            )
        ),
        StatusItem(
            title = "Jul 6",
            isToday = false,
            stories = listOf(
                StatusStory("प्रेम", "प्रेम ही जीवन का आधार है।", Color(0xFF74B9FF)),
                StatusStory("क्षमा", "क्षमा करना दिव्य गुण है।", Color(0xFDA085))
            )
        ),
        StatusItem(
            title = "Jul 5",
            isToday = false,
            stories = listOf(
                StatusStory("संयम", "संयम से जीवन में संतुलन आता है।", Color(0xFF00B894)),
                StatusStory("सत्य", "सत्य बोलना सबसे बड़ा तप है।", Color(0xFF6C5CE7))
            )
        )
    )

    val currentStatus = statusItems[currentStatusIndex]
    val currentStory = currentStatus.stories[currentStoryIndex]

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        // Background with story color
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            currentStory.backgroundColor.copy(alpha = 0.3f),
                            Color.Black.copy(alpha = 0.8f)
                        )
                    )
                )
        )

        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // Progress indicators at very top of screen
            StatusProgressIndicators(
                statusItems = statusItems,
                currentStatusIndex = currentStatusIndex,
                currentStoryIndex = currentStoryIndex,
                onClose = onClose
            )

            // Status content
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(24.dp),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = currentStory.title,
                        fontSize = 32.sp,
                        fontFamily = eczarFamily,
                        fontWeight = FontWeight.Bold,
                        color = Color.White,
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.height(24.dp))

                    Text(
                        text = currentStory.content,
                        fontSize = 18.sp,
                        fontFamily = eczarFamily,
                        color = Color.White.copy(alpha = 0.9f),
                        textAlign = TextAlign.Center,
                        lineHeight = 26.sp
                    )
                }

                // Navigation areas (invisible)
                Row(
                    modifier = Modifier.fillMaxSize()
                ) {
                    // Left half - previous story/status
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxHeight()
                            .clickable {
                                if (currentStoryIndex > 0) {
                                    currentStoryIndex--
                                } else if (currentStatusIndex > 0) {
                                    currentStatusIndex--
                                    currentStoryIndex = statusItems[currentStatusIndex].stories.size - 1
                                }
                            }
                    )

                    // Right half - next story/status
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxHeight()
                            .clickable {
                                if (currentStoryIndex < currentStatus.stories.size - 1) {
                                    currentStoryIndex++
                                } else if (currentStatusIndex < statusItems.size - 1) {
                                    currentStatusIndex++
                                    currentStoryIndex = 0
                                } else {
                                    onClose()
                                }
                            }
                    )
                }
            }
        }
    }
}

@Composable
fun StatusProgressIndicators(
    statusItems: List<StatusItem>,
    currentStatusIndex: Int,
    currentStoryIndex: Int,
    onClose: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 16.dp, start = 16.dp, end = 16.dp, bottom = 8.dp) // Top padding for status bar area
    ) {
        // Progress bars at very top
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            statusItems[currentStatusIndex].stories.forEachIndexed { index, _ ->
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .height(3.dp)
                        .background(
                            color = if (index <= currentStoryIndex) Color.White else Color.White.copy(alpha = 0.3f),
                            shape = RoundedCornerShape(2.dp)
                        )
                )
            }
        }

        Spacer(modifier = Modifier.height(12.dp))

        // Status title and close button row
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = statusItems[currentStatusIndex].title,
                fontSize = 16.sp,
                fontFamily = eczarFamily,
                fontWeight = FontWeight.Bold,
                color = Color.White
            )

            IconButton(
                onClick = {
                    println("Close button clicked") // Debug log
                    onClose()
                },
                modifier = Modifier
                    .size(32.dp)
                    .background(Color.Black.copy(alpha = 0.5f), CircleShape)
            ) {
                Text(
                    text = "✕",
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

// Data classes for status functionality
data class StatusItem(
    val title: String,
    val isToday: Boolean = false,
    val stories: List<StatusStory>
)

data class StatusStory(
    val title: String,
    val content: String,
    val backgroundColor: Color
)
