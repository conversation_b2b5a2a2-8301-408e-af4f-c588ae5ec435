package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.example.arham.ArhamApplication",
    rootPackage = "com.example.arham",
    originatingRoot = "com.example.arham.ArhamApplication",
    originatingRootPackage = "com.example.arham",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "ArhamApplication",
    originatingRootSimpleNames = "ArhamApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_example_arham_ArhamApplication {
}
