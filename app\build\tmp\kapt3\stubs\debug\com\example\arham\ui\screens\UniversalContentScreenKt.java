package com.example.arham.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00008\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a \u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u0007H\u0007\u001a\u0018\u0010\b\u001a\u00020\u00012\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\t\u001a\u00020\nH\u0007\u001a\u0016\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\n0\f2\u0006\u0010\t\u001a\u00020\nH\u0002\u001a8\u0010\r\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\t\u001a\u00020\n2\u0018\u0010\u0010\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00030\f\u0012\u0004\u0012\u00020\u00010\u0011H\u0082@\u00a2\u0006\u0002\u0010\u0012\u00a8\u0006\u0013"}, d2 = {"ContentListItem", "", "contentItem", "Lcom/example/arham/ui/screens/ContentItem;", "lazyListState", "Landroidx/compose/foundation/lazy/LazyListState;", "navController", "Landroidx/navigation/NavController;", "UniversalContentScreen", "category", "", "getStaticContentForCategory", "", "loadStructuredContent", "db", "Lcom/google/firebase/firestore/FirebaseFirestore;", "onResult", "Lkotlin/Function1;", "(Lcom/google/firebase/firestore/FirebaseFirestore;Ljava/lang/String;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class UniversalContentScreenKt {
    
    @kotlin.OptIn(markerClass = {androidx.compose.foundation.ExperimentalFoundationApi.class})
    @androidx.compose.runtime.Composable()
    public static final void UniversalContentScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    java.lang.String category) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void ContentListItem(@org.jetbrains.annotations.NotNull()
    com.example.arham.ui.screens.ContentItem contentItem, @org.jetbrains.annotations.NotNull()
    androidx.compose.foundation.lazy.LazyListState lazyListState, @org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController) {
    }
    
    private static final java.util.List<java.lang.String> getStaticContentForCategory(java.lang.String category) {
        return null;
    }
    
    private static final java.lang.Object loadStructuredContent(com.google.firebase.firestore.FirebaseFirestore db, java.lang.String category, kotlin.jvm.functions.Function1<? super java.util.List<com.example.arham.ui.screens.ContentItem>, kotlin.Unit> onResult, kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}