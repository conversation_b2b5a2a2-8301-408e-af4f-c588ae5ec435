package com.example.arham.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// Glassmorphism Colors for Light Theme
val LightGlassContentColor = Color(0xFF2C2C2C)
val LightGlassSurfaceColor = Color(0xFFFFFFFF)
val LightButtonColor = Color(0xFF6200EE)
val LightButtonTextColor = Color(0xFFFFFFFF)

// Glassmorphism Colors for Dark Theme
val DarkGlassContentColor = Color(0xFFE0E0E0)
val DarkGlassSurfaceColor = Color(0xFF1C1C1C)
val DarkButtonColor = Color(0xFFBB86FC)
val DarkButtonTextColor = Color(0xFF000000)
