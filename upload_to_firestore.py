#!/usr/bin/env python3
"""
Upload ArhamApp Data to Firestore
"""

import json
import firebase_admin
from firebase_admin import credentials, firestore
import sys
import os

def upload_to_firestore(json_file_path, collection_name="spiritual_content"):
    """
    Upload JSON data to Firestore
    """
    
    try:
        # Initialize Firebase Admin SDK
        # You need to download service account key from Firebase Console
        cred = credentials.Certificate("firebase-service-account-key.json")
        firebase_admin.initialize_app(cred)
        
        # Get Firestore client
        db = firestore.client()
        
        print("🔄 Reading JSON file...")
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📊 Found {len(data)} items to upload")
        
        # Upload in batches (Firestore limit: 500 operations per batch)
        batch_size = 500
        total_uploaded = 0
        
        for i in range(0, len(data), batch_size):
            batch = db.batch()
            batch_data = data[i:i + batch_size]
            
            print(f"🔄 Uploading batch {i//batch_size + 1} ({len(batch_data)} items)...")
            
            for item in batch_data:
                doc_ref = db.collection(collection_name).document(item['id'])
                batch.set(doc_ref, item)
            
            # Commit batch
            batch.commit()
            total_uploaded += len(batch_data)
            
            print(f"✅ Uploaded {total_uploaded}/{len(data)} items")
        
        print(f"🎉 Successfully uploaded {total_uploaded} items to Firestore!")
        print(f"📁 Collection: {collection_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ Upload failed: {str(e)}")
        return False

def setup_firebase():
    """
    Setup instructions for Firebase
    """
    
    print("🔧 Firebase Setup Instructions:")
    print("=" * 50)
    print("1. Go to Firebase Console: https://console.firebase.google.com/")
    print("2. Select your project")
    print("3. Go to Project Settings > Service Accounts")
    print("4. Click 'Generate new private key'")
    print("5. Download the JSON file")
    print("6. Rename it to 'firebase-service-account-key.json'")
    print("7. Place it in the same directory as this script")
    print("8. Run this script again")
    print()

if __name__ == "__main__":
    print("🔥 ArhamApp Firestore Upload Tool")
    print("=" * 50)
    
    # Check if service account key exists
    if not os.path.exists("firebase-service-account-key.json"):
        print("❌ Firebase service account key not found!")
        setup_firebase()
        sys.exit(1)
    
    # Check if JSON file exists
    json_files = [f for f in os.listdir('.') if f.endswith('.json') and 'firestore' in f.lower()]
    
    if not json_files:
        print("❌ No Firestore JSON files found!")
        print("Looking for files with 'firestore' in the name...")
        sys.exit(1)
    
    print("📁 Found JSON files:")
    for i, file in enumerate(json_files):
        print(f"  {i+1}. {file}")
    
    # Use first firestore file or let user choose
    if len(json_files) == 1:
        json_file = json_files[0]
        print(f"Using: {json_file}")
    else:
        try:
            choice = int(input(f"Choose file (1-{len(json_files)}): ")) - 1
            json_file = json_files[choice]
        except (ValueError, IndexError):
            print("Invalid choice, using first file")
            json_file = json_files[0]
    
    # Get collection name
    collection_name = input("Enter Firestore collection name (default: spiritual_content): ").strip()
    if not collection_name:
        collection_name = "spiritual_content"
    
    # Upload to Firestore
    success = upload_to_firestore(json_file, collection_name)
    
    if success:
        print("\n✅ Upload completed successfully!")
        print(f"🔗 Check your data at: https://console.firebase.google.com/project/YOUR_PROJECT/firestore")
    else:
        print("\n❌ Upload failed!")
    
    input("Press Enter to exit...")
