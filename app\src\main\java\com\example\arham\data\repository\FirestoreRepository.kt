package com.example.arham.data.repository

import com.example.arham.data.models.*
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.tasks.await

class FirestoreRepository {
    private val db = FirebaseFirestore.getInstance()
    
    // Collections
    private val contentCollection = db.collection("spiritual_content")
    private val authorsCollection = db.collection("authors")
    private val bookmarksCollection = db.collection("bookmarks")
    private val searchIndexCollection = db.collection("search_index")
    private val userPreferencesCollection = db.collection("user_preferences")
    private val sadhanaCollection = db.collection("sadhana_entries")
    private val collectionsCollection = db.collection("content_collections")
    private val analyticsCollection = db.collection("content_analytics")
    
    // ==================== CONTENT OPERATIONS ====================

    suspend fun addContent(content: SpiritualContent): Bo<PERSON>an {
        return try {
            contentCollection.document(content.id).set(content).await()
            true
        } catch (e: Exception) {
            false
        }
    }

    suspend fun getAllContent(): List<SpiritualContent> {
        return try {
            contentCollection
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .get()
                .await()
                .toObjects(SpiritualContent::class.java)
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    suspend fun getContentByCategory(category: ContentCategory): List<SpiritualContent> {
        return try {
            contentCollection
                .whereEqualTo("category", category)
                .orderBy("title")
                .get()
                .await()
                .toObjects(SpiritualContent::class.java)
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    suspend fun getContentByType(type: ContentType): List<SpiritualContent> {
        return try {
            contentCollection
                .whereEqualTo("type", type)
                .orderBy("title")
                .get()
                .await()
                .toObjects(SpiritualContent::class.java)
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    suspend fun getContentByAuthor(author: String): List<SpiritualContent> {
        return try {
            contentCollection
                .whereEqualTo("author", author)
                .orderBy("title")
                .get()
                .await()
                .toObjects(SpiritualContent::class.java)
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    suspend fun getPopularContent(): List<SpiritualContent> {
        return try {
            contentCollection
                .whereEqualTo("isPopular", true)
                .orderBy("viewCount", Query.Direction.DESCENDING)
                .limit(20)
                .get()
                .await()
                .toObjects(SpiritualContent::class.java)
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    suspend fun getContentById(contentId: String): SpiritualContent? {
        return try {
            contentCollection
                .document(contentId)
                .get()
                .await()
                .toObject(SpiritualContent::class.java)
        } catch (e: Exception) {
            null
        }
    }
    
    // ==================== SEARCH OPERATIONS ====================
    
    suspend fun globalSearch(query: String): List<SearchResult> {
        if (query.isBlank()) return emptyList()
        
        val searchTerms = query.lowercase().split(" ").filter { it.isNotBlank() }
        val results = mutableListOf<SearchResult>()
        
        try {
            // Search in search index
            val searchResults = searchIndexCollection
                .whereArrayContainsAny("keywords", searchTerms)
                .orderBy("popularity", Query.Direction.DESCENDING)
                .limit(50)
                .get()
                .await()
                .toObjects(SearchIndex::class.java)
            
            // Convert to SearchResult
            searchResults.forEach { index ->
                val content = getContentById(index.contentId)
                content?.let {
                    results.add(
                        SearchResult(
                            contentId = it.id,
                            title = it.title,
                            author = it.author,
                            category = it.category,
                            type = it.type,
                            snippet = getSnippet(it.content, query),
                            relevanceScore = calculateRelevance(it, query)
                        )
                    )
                }
            }
            
            // Sort by relevance
            return results.sortedByDescending { it.relevanceScore }
            
        } catch (e: Exception) {
            return emptyList()
        }
    }
    
    suspend fun searchByAuthor(authorName: String): List<SpiritualContent> {
        return try {
            contentCollection
                .whereGreaterThanOrEqualTo("author", authorName)
                .whereLessThanOrEqualTo("author", authorName + "\uf8ff")
                .orderBy("author")
                .orderBy("title")
                .get()
                .await()
                .toObjects(SpiritualContent::class.java)
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    suspend fun searchByTitle(title: String): List<SpiritualContent> {
        return try {
            contentCollection
                .whereGreaterThanOrEqualTo("title", title)
                .whereLessThanOrEqualTo("title", title + "\uf8ff")
                .orderBy("title")
                .get()
                .await()
                .toObjects(SpiritualContent::class.java)
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    // ==================== BOOKMARK OPERATIONS ====================
    
    suspend fun addBookmark(userId: String, content: SpiritualContent): Boolean {
        return try {
            val bookmark = UserBookmark(
                userId = userId,
                contentId = content.id,
                contentTitle = content.title,
                contentType = content.type,
                category = content.category
            )
            bookmarksCollection.add(bookmark).await()
            true
        } catch (e: Exception) {
            false
        }
    }
    
    suspend fun removeBookmark(userId: String, contentId: String): Boolean {
        return try {
            val bookmarks = bookmarksCollection
                .whereEqualTo("userId", userId)
                .whereEqualTo("contentId", contentId)
                .get()
                .await()
            
            bookmarks.documents.forEach { it.reference.delete() }
            true
        } catch (e: Exception) {
            false
        }
    }
    
    suspend fun getUserBookmarks(userId: String): List<UserBookmark> {
        return try {
            bookmarksCollection
                .whereEqualTo("userId", userId)
                .orderBy("bookmarkedAt", Query.Direction.DESCENDING)
                .get()
                .await()
                .toObjects(UserBookmark::class.java)
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    suspend fun isBookmarked(userId: String, contentId: String): Boolean {
        return try {
            val result = bookmarksCollection
                .whereEqualTo("userId", userId)
                .whereEqualTo("contentId", contentId)
                .limit(1)
                .get()
                .await()
            !result.isEmpty
        } catch (e: Exception) {
            false
        }
    }
    
    // ==================== ANALYTICS ====================
    
    suspend fun incrementViewCount(contentId: String) {
        try {
            val docRef = analyticsCollection.document(contentId)
            db.runTransaction { transaction ->
                val snapshot = transaction.get(docRef)
                val currentViews = snapshot.getLong("views") ?: 0
                transaction.update(docRef, "views", currentViews + 1)
            }.await()
        } catch (e: Exception) {
            // Handle error
        }
    }
    
    // ==================== HELPER FUNCTIONS ====================
    
    private fun getSnippet(content: String, query: String): String {
        val queryLower = query.lowercase()
        val contentLower = content.lowercase()
        val index = contentLower.indexOf(queryLower)
        
        return if (index != -1) {
            val start = maxOf(0, index - 50)
            val end = minOf(content.length, index + query.length + 50)
            "..." + content.substring(start, end) + "..."
        } else {
            content.take(100) + if (content.length > 100) "..." else ""
        }
    }
    
    private fun calculateRelevance(content: SpiritualContent, query: String): Float {
        val queryLower = query.lowercase()
        var score = 0f
        
        // Title match (highest weight)
        if (content.title.lowercase().contains(queryLower)) score += 10f
        
        // Author match
        if (content.author.lowercase().contains(queryLower)) score += 8f
        
        // Tags match
        content.tags.forEach { tag ->
            if (tag.lowercase().contains(queryLower)) score += 5f
        }
        
        // Content match (lowest weight)
        if (content.content.lowercase().contains(queryLower)) score += 2f
        
        // Popularity boost
        if (content.isPopular) score += 3f
        
        // View count boost
        score += (content.viewCount / 1000f)
        
        return score
    }
}

// Search Result Model
data class SearchResult(
    val contentId: String,
    val title: String,
    val author: String,
    val category: ContentCategory,
    val type: ContentType,
    val snippet: String,
    val relevanceScore: Float
)
