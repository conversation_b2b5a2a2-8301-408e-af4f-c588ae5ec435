<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArhamApp Simple Admin - Data Entry</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
            color: #333;
        }
        
        .stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 15px;
        }
        
        .stat {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #666;
        }
        
        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .panel {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .panel h2 {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
            color: #333;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0056b3;
        }
        
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
        
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        
        .btn-warning { background: #ffc107; color: #333; }
        .btn-warning:hover { background: #e0a800; }
        
        .data-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .data-item {
            padding: 10px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .data-item:hover {
            background: #f8f9fa;
        }
        
        .data-item:last-child {
            border-bottom: none;
        }
        
        .item-info {
            flex: 1;
        }
        
        .item-title {
            font-weight: bold;
            margin-bottom: 3px;
        }
        
        .item-meta {
            font-size: 0.8em;
            color: #666;
        }
        
        .item-actions {
            display: flex;
            gap: 5px;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.success { background: #28a745; }
        .notification.error { background: #dc3545; }
        .notification.info { background: #007bff; }
        
        .tree-view {
            font-family: monospace;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .tree-item {
            margin: 2px 0;
            cursor: pointer;
            padding: 2px 5px;
            border-radius: 3px;
        }
        
        .tree-item:hover {
            background: #e9ecef;
        }
        
        .level-0 { margin-left: 0px; font-weight: bold; }
        .level-1 { margin-left: 20px; }
        .level-2 { margin-left: 40px; }
        .level-3 { margin-left: 60px; }
        
        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
            
            .stats {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🕉️ ArhamApp Simple Admin</h1>
            <p>Easy Data Entry for Spiritual Reading Content</p>
            
            <div class="stats">
                <div class="stat">
                    <div class="stat-number" id="totalItems">0</div>
                    <div class="stat-label">Total Items</div>
                </div>
                <div class="stat">
                    <div class="stat-number" id="totalLists">0</div>
                    <div class="stat-label">Lists</div>
                </div>
                <div class="stat">
                    <div class="stat-number" id="totalContent">0</div>
                    <div class="stat-label">Content</div>
                </div>
            </div>
        </div>
        
        <!-- Main Grid -->
        <div class="main-grid">
            <!-- Add Content Panel -->
            <div class="panel">
                <h2>📝 Add New Item</h2>
                
                <form id="contentForm">
                    <div class="form-group">
                        <label class="form-label">Title/Name *</label>
                        <input type="text" class="form-input" id="itemTitle" placeholder="जैसे: गीत/ढाल संग्रह, रु रु मैं संवारियो" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Parent Item</label>
                        <select class="form-select" id="parentSelect">
                            <option value="">Root Level (No Parent)</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Type *</label>
                        <select class="form-select" id="itemType" required>
                            <option value="list">📂 List (Has Children)</option>
                            <option value="content">📄 Content (Final Item)</option>
                        </select>
                    </div>
                    
                    <div class="form-group" id="contentGroup" style="display: none;">
                        <label class="form-label">Content Text</label>
                        <textarea class="form-textarea" id="contentText" placeholder="यहाँ मंत्र, श्लोक, भजन या content लिखें..."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Author</label>
                        <input type="text" class="form-input" id="itemAuthor" placeholder="आचार्य तुलसी, महावीर स्वामी">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Order</label>
                        <input type="number" class="form-input" id="itemOrder" value="1" min="1">
                    </div>
                    
                    <button type="button" class="btn btn-success" onclick="addItem()">
                        ✅ Add Item
                    </button>
                    <button type="button" class="btn" onclick="clearForm()">
                        🗑️ Clear
                    </button>
                </form>
            </div>
            
            <!-- Data Management Panel -->
            <div class="panel">
                <h2>📊 Data Management</h2>
                
                <div style="margin-bottom: 15px;">
                    <button class="btn btn-success" onclick="exportData()">
                        📱 Export for App
                    </button>
                    <button class="btn btn-warning" onclick="backupData()">
                        💾 Backup
                    </button>
                    <button class="btn btn-danger" onclick="clearAllData()">
                        🗑️ Clear All
                    </button>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Search Items</label>
                    <input type="text" class="form-input" id="searchInput" placeholder="Search by title..." onkeyup="searchItems()">
                </div>
                
                <div class="data-list" id="dataList">
                    <div style="text-align: center; padding: 40px; color: #666;">
                        No data yet. Add some items to get started!
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Tree View Panel -->
        <div class="panel" style="margin-top: 20px;">
            <h2>🌳 Hierarchy View</h2>
            <div class="tree-view" id="treeView">
                <div style="text-align: center; padding: 20px; color: #666;">
                    Tree structure will appear here as you add items
                </div>
            </div>
        </div>
    </div>
    
    <!-- Notification -->
    <div id="notification" class="notification"></div>

    <script>
        // Data storage
        let spiritualData = [];

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            updateStats();
            updateParentSelect();
            updateDataList();
            updateTreeView();

            // Show/hide content field based on type
            document.getElementById('itemType').addEventListener('change', function() {
                const contentGroup = document.getElementById('contentGroup');
                if (this.value === 'content') {
                    contentGroup.style.display = 'block';
                } else {
                    contentGroup.style.display = 'none';
                }
            });
        });

        // Load data from localStorage
        function loadData() {
            const saved = localStorage.getItem('arhamSpiritualData');
            if (saved) {
                spiritualData = JSON.parse(saved);
            }
        }

        // Save data to localStorage
        function saveData() {
            localStorage.setItem('arhamSpiritualData', JSON.stringify(spiritualData));
            updateStats();
            updateParentSelect();
            updateDataList();
            updateTreeView();
        }

        // Add new item
        function addItem() {
            const title = document.getElementById('itemTitle').value.trim();
            const parentId = document.getElementById('parentSelect').value || null;
            const type = document.getElementById('itemType').value;
            const content = document.getElementById('contentText').value.trim();
            const author = document.getElementById('itemAuthor').value.trim();
            const order = parseInt(document.getElementById('itemOrder').value) || 1;

            if (!title || !type) {
                showNotification('Please fill in title and type!', 'error');
                return;
            }

            if (type === 'content' && !content) {
                showNotification('Please add content text for content items!', 'error');
                return;
            }

            // Calculate level
            let level = 0;
            if (parentId) {
                const parent = spiritualData.find(item => item.id === parentId);
                if (parent) {
                    level = parent.level + 1;
                }
            }

            const newItem = {
                id: 'item_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                title: title,
                level: level,
                parentId: parentId,
                hasChildren: type === 'list',
                childrenCount: 0,
                contentType: type,
                order: order,
                content: type === 'content' ? content : null,
                metadata: {
                    author: author || null,
                    language: 'hindi',
                    isActive: true,
                    createdAt: Date.now(),
                    updatedAt: Date.now()
                }
            };

            spiritualData.push(newItem);

            // Update parent's children count
            if (parentId) {
                const parent = spiritualData.find(item => item.id === parentId);
                if (parent) {
                    parent.childrenCount = spiritualData.filter(item => item.parentId === parentId).length;
                }
            }

            saveData();
            clearForm();
            showNotification('Item added successfully!', 'success');
        }

        // Clear form
        function clearForm() {
            document.getElementById('contentForm').reset();
            document.getElementById('contentGroup').style.display = 'none';
            document.getElementById('itemTitle').focus();
        }

        // Update statistics
        function updateStats() {
            const totalItems = spiritualData.length;
            const totalLists = spiritualData.filter(item => item.contentType === 'list').length;
            const totalContent = spiritualData.filter(item => item.contentType === 'content').length;

            document.getElementById('totalItems').textContent = totalItems;
            document.getElementById('totalLists').textContent = totalLists;
            document.getElementById('totalContent').textContent = totalContent;
        }

        // Update parent select dropdown
        function updateParentSelect() {
            const select = document.getElementById('parentSelect');
            const currentOptions = select.innerHTML;

            // Clear and add default option
            select.innerHTML = '<option value="">Root Level (No Parent)</option>';

            // Add list items as potential parents
            const listItems = spiritualData
                .filter(item => item.contentType === 'list')
                .sort((a, b) => a.level - b.level || a.order - b.order);

            listItems.forEach(item => {
                const indent = '  '.repeat(item.level);
                const option = document.createElement('option');
                option.value = item.id;
                option.textContent = `${indent}${item.title}`;
                select.appendChild(option);
            });
        }

        // Update data list
        function updateDataList() {
            const container = document.getElementById('dataList');

            if (spiritualData.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 40px; color: #666;">No data yet. Add some items to get started!</div>';
                return;
            }

            // Sort by level and order
            const sortedData = [...spiritualData].sort((a, b) => {
                if (a.level !== b.level) return a.level - b.level;
                return a.order - b.order;
            });

            let html = '';
            sortedData.forEach(item => {
                const indent = '  '.repeat(item.level);
                const typeIcon = item.contentType === 'list' ? '📂' : '📄';
                const contentPreview = item.content ? item.content.substring(0, 50) + '...' : '';

                html += `
                    <div class="data-item">
                        <div class="item-info">
                            <div class="item-title">${indent}${typeIcon} ${item.title}</div>
                            <div class="item-meta">
                                Level: ${item.level} | Type: ${item.contentType} |
                                ${item.metadata.author ? 'Author: ' + item.metadata.author : 'No author'}
                                ${contentPreview ? '<br>' + contentPreview : ''}
                            </div>
                        </div>
                        <div class="item-actions">
                            <button class="btn" onclick="editItem('${item.id}')" style="padding: 5px 10px; font-size: 12px;">✏️</button>
                            <button class="btn btn-danger" onclick="deleteItem('${item.id}')" style="padding: 5px 10px; font-size: 12px;">🗑️</button>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // Update tree view
        function updateTreeView() {
            const container = document.getElementById('treeView');

            if (spiritualData.length === 0) {
                container.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">Tree structure will appear here as you add items</div>';
                return;
            }

            // Build tree structure
            const tree = buildTree();
            let html = '';

            function renderTree(items, level = 0) {
                items.forEach(item => {
                    const indent = '  '.repeat(level);
                    const icon = item.contentType === 'list' ? '📂' : '📄';
                    const children = item.children || [];

                    html += `<div class="tree-item level-${level}" onclick="selectTreeItem('${item.id}')">${indent}${icon} ${item.title}</div>`;

                    if (children.length > 0) {
                        renderTree(children, level + 1);
                    }
                });
            }

            renderTree(tree);
            container.innerHTML = html;
        }

        // Build tree structure from flat data
        function buildTree() {
            const itemMap = {};
            const tree = [];

            // Create map of all items
            spiritualData.forEach(item => {
                itemMap[item.id] = { ...item, children: [] };
            });

            // Build tree
            spiritualData.forEach(item => {
                if (item.parentId && itemMap[item.parentId]) {
                    itemMap[item.parentId].children.push(itemMap[item.id]);
                } else {
                    tree.push(itemMap[item.id]);
                }
            });

            // Sort each level
            function sortLevel(items) {
                items.sort((a, b) => a.order - b.order);
                items.forEach(item => {
                    if (item.children.length > 0) {
                        sortLevel(item.children);
                    }
                });
            }

            sortLevel(tree);
            return tree;
        }

        // Select tree item
        function selectTreeItem(itemId) {
            const item = spiritualData.find(i => i.id === itemId);
            if (item) {
                // Fill form with item data
                document.getElementById('itemTitle').value = item.title;
                document.getElementById('parentSelect').value = item.parentId || '';
                document.getElementById('itemType').value = item.contentType;
                document.getElementById('contentText').value = item.content || '';
                document.getElementById('itemAuthor').value = item.metadata.author || '';
                document.getElementById('itemOrder').value = item.order;

                // Show content field if needed
                const contentGroup = document.getElementById('contentGroup');
                if (item.contentType === 'content') {
                    contentGroup.style.display = 'block';
                } else {
                    contentGroup.style.display = 'none';
                }

                showNotification('Item loaded for editing!', 'info');
            }
        }

        // Edit item
        function editItem(itemId) {
            selectTreeItem(itemId);
        }

        // Delete item
        function deleteItem(itemId) {
            const item = spiritualData.find(i => i.id === itemId);
            if (!item) return;

            if (confirm(`Delete "${item.title}"?\n\nThis will also delete all its children.`)) {
                // Find all children recursively
                function findAllChildren(parentId) {
                    const children = spiritualData.filter(i => i.parentId === parentId);
                    let allChildren = [...children];

                    children.forEach(child => {
                        allChildren = allChildren.concat(findAllChildren(child.id));
                    });

                    return allChildren;
                }

                const toDelete = [item, ...findAllChildren(itemId)];
                const deleteIds = toDelete.map(i => i.id);

                spiritualData = spiritualData.filter(i => !deleteIds.includes(i.id));

                // Update parent's children count
                if (item.parentId) {
                    const parent = spiritualData.find(i => i.id === item.parentId);
                    if (parent) {
                        parent.childrenCount = spiritualData.filter(i => i.parentId === item.parentId).length;
                    }
                }

                saveData();
                showNotification(`Deleted "${item.title}" and ${toDelete.length - 1} children`, 'success');
            }
        }

        // Search items
        function searchItems() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const items = document.querySelectorAll('.data-item');

            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // Export data for app
        function exportData() {
            if (spiritualData.length === 0) {
                showNotification('No data to export!', 'error');
                return;
            }

            const exportData = {
                spiritual_reading: spiritualData,
                metadata: {
                    version: '1.0',
                    totalItems: spiritualData.length,
                    exportedAt: Date.now(),
                    appName: 'ArhamApp'
                }
            };

            downloadJSON(exportData, `arham_spiritual_reading_${new Date().toISOString().split('T')[0]}.json`);
            showNotification('Data exported successfully!', 'success');
        }

        // Backup data
        function backupData() {
            if (spiritualData.length === 0) {
                showNotification('No data to backup!', 'error');
                return;
            }

            downloadJSON(spiritualData, `arham_backup_${new Date().toISOString().split('T')[0]}.json`);
            showNotification('Backup created successfully!', 'success');
        }

        // Clear all data
        function clearAllData() {
            if (confirm('Are you sure you want to delete ALL data?\n\nThis cannot be undone!')) {
                spiritualData = [];
                saveData();
                clearForm();
                showNotification('All data cleared!', 'success');
            }
        }

        // Download JSON file
        function downloadJSON(data, filename) {
            const blob = new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // Show notification
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                addItem();
            }
            if (e.ctrlKey && e.key === 'e') {
                e.preventDefault();
                exportData();
            }
        });
    </script>
</body>
</html>
