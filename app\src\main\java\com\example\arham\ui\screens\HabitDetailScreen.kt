package com.example.arham.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowBackIos
import androidx.compose.material.icons.filled.ArrowForwardIos
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import com.example.arham.data.models.*
import com.example.arham.ui.theme.eczarFamily
import java.time.LocalDate
import java.time.YearMonth
import java.time.format.DateTimeFormatter
import java.time.format.TextStyle
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HabitDetailScreen(
    navController: NavController,
    habitId: String,
    isDarkMode: Boolean
) {
    var selectedTab by remember { mutableStateOf(0) }
    var currentMonth by remember { mutableStateOf(YearMonth.now()) }
    
    // Sample data - in real app this would come from repository
    val habit = remember {
        Habit(
            id = habitId,
            name = if (habitId == "1") "Mala" else "Samayik",
            category = HabitCategory.MEDITATION,
            color = if (habitId == "1") Color(0xFF4A90E2) else Color(0xFFD2691E)
        )
    }
    
    val completions = remember {
        listOf(
            HabitCompletion(habitId, LocalDate.now().minusDays(6)),
            HabitCompletion(habitId, LocalDate.now().minusDays(5)),
            HabitCompletion(habitId, LocalDate.now().minusDays(4)),
            HabitCompletion(habitId, LocalDate.now().minusDays(3)),
            HabitCompletion(habitId, LocalDate.now().minusDays(2)),
            HabitCompletion(habitId, LocalDate.now().minusDays(1)),
            HabitCompletion(habitId, LocalDate.now())
        )
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
    ) {
        // Header
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(
                onClick = { navController.popBackStack() }
            ) {
                Icon(
                    Icons.Default.ArrowBack,
                    contentDescription = "Back",
                    tint = MaterialTheme.colorScheme.onBackground
                )
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // Habit icon and name
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .size(24.dp)
                        .background(
                            color = habit.color,
                            shape = RoundedCornerShape(6.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = habit.category.emoji,
                        fontSize = 12.sp
                    )
                }
                
                Spacer(modifier = Modifier.width(12.dp))
                
                Text(
                    text = habit.name,
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onBackground
                )
            }
        }
        
        // Tab Row
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {
            // Calendar Tab
            Box(
                modifier = Modifier
                    .background(
                        color = if (selectedTab == 0) MaterialTheme.colorScheme.surface else Color.Transparent,
                        shape = RoundedCornerShape(20.dp)
                    )
                    .clickable { selectedTab = 0 }
                    .padding(horizontal = 20.dp, vertical = 8.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "Calendar",
                    fontSize = 14.sp,
                    fontWeight = if (selectedTab == 0) FontWeight.Medium else FontWeight.Normal,
                    color = if (selectedTab == 0) 
                        MaterialTheme.colorScheme.onSurface else MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            // Analytics Tab
            Box(
                modifier = Modifier
                    .background(
                        color = if (selectedTab == 1) MaterialTheme.colorScheme.surface else Color.Transparent,
                        shape = RoundedCornerShape(20.dp)
                    )
                    .clickable { selectedTab = 1 }
                    .padding(horizontal = 20.dp, vertical = 8.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "Analytics",
                    fontSize = 14.sp,
                    fontWeight = if (selectedTab == 1) FontWeight.Medium else FontWeight.Normal,
                    color = if (selectedTab == 1) 
                        MaterialTheme.colorScheme.onSurface else MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Tab Content
        when (selectedTab) {
            0 -> CalendarTabContent(
                habit = habit,
                completions = completions,
                currentMonth = currentMonth,
                onMonthChange = { currentMonth = it }
            )
            1 -> AnalyticsTabContent(
                habit = habit,
                completions = completions
            )
        }
    }
}

@Composable
fun CalendarTabContent(
    habit: Habit,
    completions: List<HabitCompletion>,
    currentMonth: YearMonth,
    onMonthChange: (YearMonth) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp)
    ) {
        // Month navigation
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(
                onClick = { onMonthChange(currentMonth.minusMonths(1)) }
            ) {
                Icon(
                    Icons.Default.ArrowBackIos,
                    contentDescription = "Previous Month",
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            Text(
                text = "${currentMonth.month.getDisplayName(TextStyle.FULL, Locale.getDefault())}, ${currentMonth.year}",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onBackground
            )
            
            IconButton(
                onClick = { onMonthChange(currentMonth.plusMonths(1)) }
            ) {
                Icon(
                    Icons.Default.ArrowForwardIos,
                    contentDescription = "Next Month",
                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Calendar grid
        CalendarGrid(
            month = currentMonth,
            completions = completions,
            habitColor = habit.color
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Streak cards
        StreakCards(
            completions = completions,
            habitColor = habit.color
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Month heatmap
        MonthHeatmap(
            completions = completions,
            habitColor = habit.color
        )
    }
}

@Composable
fun CalendarGrid(
    month: YearMonth,
    completions: List<HabitCompletion>,
    habitColor: Color
) {
    val daysInMonth = month.lengthOfMonth()
    val firstDayOfWeek = month.atDay(1).dayOfWeek.value % 7

    Column {
        // Day headers
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            val dayHeaders = listOf("Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat")
            dayHeaders.forEach { day ->
                Text(
                    text = day,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.weight(1f),
                    textAlign = androidx.compose.ui.text.style.TextAlign.Center
                )
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Calendar days
        LazyVerticalGrid(
            columns = GridCells.Fixed(7),
            modifier = Modifier.height(240.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            // Empty cells for days before month starts
            items(firstDayOfWeek) {
                Box(modifier = Modifier.size(32.dp))
            }

            // Days of the month
            items(daysInMonth) { day ->
                val date = month.atDay(day + 1)
                val isCompleted = completions.any { it.date == date && it.isCompleted }
                val isToday = date == LocalDate.now()

                Box(
                    modifier = Modifier
                        .size(32.dp)
                        .background(
                            color = when {
                                isCompleted -> habitColor
                                isToday -> MaterialTheme.colorScheme.primary.copy(alpha = 0.3f)
                                else -> Color.Transparent
                            },
                            shape = CircleShape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = (day + 1).toString(),
                        fontSize = 14.sp,
                        fontWeight = if (isToday) FontWeight.Bold else FontWeight.Normal,
                        color = when {
                            isCompleted -> Color.White
                            isToday -> MaterialTheme.colorScheme.primary
                            else -> MaterialTheme.colorScheme.onBackground
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun StreakCards(
    completions: List<HabitCompletion>,
    habitColor: Color
) {
    // Calculate streaks
    val currentStreak = calculateCurrentStreak(completions)
    val longestStreak = calculateLongestStreak(completions)

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // Current Month Streak
        Card(
            modifier = Modifier.weight(1f),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFF4CAF50)
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "🔥",
                        fontSize = 16.sp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "$currentStreak days",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                }
                Text(
                    text = "Current\nMonth Streak",
                    fontSize = 12.sp,
                    color = Color.White.copy(alpha = 0.9f),
                    lineHeight = 16.sp
                )
            }
        }

        // Longest Streak
        Card(
            modifier = Modifier.weight(1f),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFFFF9800)
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "🔥",
                        fontSize = 16.sp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "$longestStreak days",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                }
                Text(
                    text = "Longest\nStreak",
                    fontSize = 12.sp,
                    color = Color.White.copy(alpha = 0.9f),
                    lineHeight = 16.sp
                )
                Text(
                    text = "Jul 2025",
                    fontSize = 10.sp,
                    color = Color.White.copy(alpha = 0.7f)
                )
            }
        }
    }
}

@Composable
fun MonthHeatmap(
    completions: List<HabitCompletion>,
    habitColor: Color
) {
    Column {
        Text(
            text = "Month Heatmap",
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onBackground
        )

        Spacer(modifier = Modifier.height(12.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // 31 Day Heatmap
            Card(
                modifier = Modifier.weight(1f),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface
                )
            ) {
                Column(
                    modifier = Modifier.padding(12.dp)
                ) {
                    Text(
                        text = "31 Day Heatmap",
                        fontSize = 12.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // Heatmap grid
                    repeat(5) { row ->
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(2.dp)
                        ) {
                            repeat(7) { col ->
                                val dayIndex = row * 7 + col
                                val isCompleted = dayIndex < 6 // Sample data

                                Box(
                                    modifier = Modifier
                                        .size(8.dp)
                                        .background(
                                            color = if (isCompleted) habitColor else MaterialTheme.colorScheme.outline.copy(alpha = 0.2f),
                                            shape = RoundedCornerShape(1.dp)
                                        )
                                )
                            }
                        }
                        if (row < 4) Spacer(modifier = Modifier.height(2.dp))
                    }
                }
            }

            // Times Done
            Card(
                modifier = Modifier.weight(1f),
                shape = RoundedCornerShape(12.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xFF9C27B0)
                )
            ) {
                Column(
                    modifier = Modifier.padding(12.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Times Done",
                            fontSize = 12.sp,
                            color = Color.White.copy(alpha = 0.9f)
                        )
                        Spacer(modifier = Modifier.weight(1f))
                        Icon(
                            imageVector = Icons.Default.ArrowForwardIos,
                            contentDescription = null,
                            tint = Color.White.copy(alpha = 0.7f),
                            modifier = Modifier.size(12.dp)
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "${completions.size} days",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )

                    Text(
                        text = "Since Last Month",
                        fontSize = 10.sp,
                        color = Color.White.copy(alpha = 0.7f)
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = "100 %",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )
                }
            }
        }
    }
}

@Composable
fun AnalyticsTabContent(
    habit: Habit,
    completions: List<HabitCompletion>
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp)
    ) {
        Text(
            text = "Analytics coming soon...",
            fontSize = 16.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(32.dp)
        )
    }
}

// Helper functions
private fun calculateCurrentStreak(completions: List<HabitCompletion>): Int {
    val sortedCompletions = completions.filter { it.isCompleted }.sortedByDescending { it.date }
    if (sortedCompletions.isEmpty()) return 0

    var streak = 0
    var currentDate = LocalDate.now()

    for (completion in sortedCompletions) {
        if (completion.date == currentDate || completion.date == currentDate.minusDays(1)) {
            streak++
            currentDate = completion.date.minusDays(1)
        } else {
            break
        }
    }

    return streak
}

private fun calculateLongestStreak(completions: List<HabitCompletion>): Int {
    val sortedDates = completions.filter { it.isCompleted }.map { it.date }.sorted()
    if (sortedDates.isEmpty()) return 0

    var longestStreak = 1
    var currentStreak = 1

    for (i in 1 until sortedDates.size) {
        if (sortedDates[i] == sortedDates[i - 1].plusDays(1)) {
            currentStreak++
            longestStreak = maxOf(longestStreak, currentStreak)
        } else {
            currentStreak = 1
        }
    }

    return longestStreak
}
