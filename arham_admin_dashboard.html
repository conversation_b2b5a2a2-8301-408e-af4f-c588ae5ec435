<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArhamApp Admin Dashboard - Personal CMS</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .admin-container {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 280px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 20px;
            overflow-y: auto;
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            border-radius: 15px;
            color: white;
        }

        .logo h1 {
            font-size: 1.8em;
            margin-bottom: 5px;
        }

        .logo p {
            opacity: 0.9;
            font-size: 0.9em;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 10px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            text-decoration: none;
            color: #333;
            border-radius: 10px;
            transition: all 0.3s;
            font-weight: 500;
        }

        .nav-link:hover, .nav-link.active {
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            color: white;
            transform: translateX(5px);
        }

        .nav-icon {
            margin-right: 12px;
            font-size: 1.2em;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .page-header {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .page-title {
            font-size: 2.2em;
            color: #333;
            margin-bottom: 10px;
        }

        .page-subtitle {
            color: #666;
            font-size: 1.1em;
        }

        /* Dashboard Cards */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .dashboard-card {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
        }

        .card-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
        }

        .card-title {
            font-size: 1.3em;
            color: #333;
            margin-bottom: 10px;
        }

        .card-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #4ECDC4;
            margin-bottom: 10px;
        }

        .card-description {
            color: #666;
            font-size: 0.9em;
        }

        /* Content Sections */
        .content-section {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            display: none;
        }

        .content-section.active {
            display: block;
        }

        .section-title {
            font-size: 1.8em;
            color: #333;
            margin-bottom: 20px;
            border-bottom: 3px solid #4ECDC4;
            padding-bottom: 10px;
        }

        /* Form Styles */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-input, .form-select, .form-textarea {
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 14px;
            transition: all 0.3s;
            background: white;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #4ECDC4;
            box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 120px;
        }

        /* Buttons */
        .btn {
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin: 5px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-primary { background: linear-gradient(45deg, #4ECDC4, #44A08D); }
        .btn-success { background: linear-gradient(45deg, #56ab2f, #a8e6cf); }
        .btn-danger { background: linear-gradient(45deg, #ff4757, #ff3838); }
        .btn-warning { background: linear-gradient(45deg, #ffa726, #ff9800); }
        .btn-info { background: linear-gradient(45deg, #667eea, #764ba2); }

        /* Data Table */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .data-table th {
            background: linear-gradient(45deg, #4ECDC4, #44A08D);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }

        .data-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #eee;
        }

        .data-table tr:hover {
            background: #f8f9fa;
        }

        /* Quick Actions */
        .quick-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 30px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .admin-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
            }

            .main-content {
                padding: 20px;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Notifications */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success { background: linear-gradient(45deg, #56ab2f, #a8e6cf); }
        .notification.error { background: linear-gradient(45deg, #ff4757, #ff3838); }
        .notification.info { background: linear-gradient(45deg, #667eea, #764ba2); }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="logo">
                <h1>🕉️ ArhamApp</h1>
                <p>Personal Admin Dashboard</p>
            </div>

            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#" class="nav-link active" onclick="showSection('dashboard')">
                        <span class="nav-icon">📊</span>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="showSection('add-content')">
                        <span class="nav-icon">➕</span>
                        Add Content
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="showSection('manage-content')">
                        <span class="nav-icon">📝</span>
                        Manage Content
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="showSection('categories')">
                        <span class="nav-icon">📂</span>
                        Categories
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="showSection('import-export')">
                        <span class="nav-icon">🔄</span>
                        Import/Export
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="showSection('settings')">
                        <span class="nav-icon">⚙️</span>
                        Settings
                    </a>
                </li>
            </ul>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Dashboard Section -->
            <div id="dashboard" class="content-section active">
                <div class="page-header">
                    <h1 class="page-title">📊 Dashboard</h1>
                    <p class="page-subtitle">ArhamApp Content Management Overview</p>
                </div>

                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">📖</div>
                        <div class="card-title">Total Content</div>
                        <div class="card-value" id="totalContent">0</div>
                        <div class="card-description">Spiritual content items</div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">📂</div>
                        <div class="card-title">Categories</div>
                        <div class="card-value" id="totalCategories">0</div>
                        <div class="card-description">Content categories</div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">👤</div>
                        <div class="card-title">Authors</div>
                        <div class="card-value" id="totalAuthors">0</div>
                        <div class="card-description">Content creators</div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">🕒</div>
                        <div class="card-title">Last Updated</div>
                        <div class="card-value" id="lastUpdated" style="font-size: 1.2em;">Never</div>
                        <div class="card-description">Recent activity</div>
                    </div>
                </div>

                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="showSection('add-content')">
                        ➕ Quick Add Content
                    </button>
                    <button class="btn btn-success" onclick="exportData()">
                        📱 Export for App
                    </button>
                    <button class="btn btn-info" onclick="loadSampleData()">
                        📝 Load Sample Data
                    </button>
                    <button class="btn btn-warning" onclick="backupData()">
                        💾 Backup Data
                    </button>
                </div>

                <!-- Recent Content -->
                <div class="content-section active">
                    <h2 class="section-title">📋 Recent Content</h2>
                    <div id="recentContent">
                        <p style="text-align: center; color: #666; padding: 40px;">
                            No content yet. Start by adding some spiritual content!
                        </p>
                    </div>
                </div>
            </div>

            <!-- Add Content Section -->
            <div id="add-content" class="content-section">
                <div class="page-header">
                    <h1 class="page-title">➕ Add New Content</h1>
                    <p class="page-subtitle">Add spiritual content to your app</p>
                </div>

                <form id="contentForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">📖 Title/Name *</label>
                            <input type="text" class="form-input" id="contentTitle" placeholder="जैसे: गायत्री मंत्र, हनुमान चालीसा" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">📂 Category *</label>
                            <select class="form-select" id="contentCategory" required>
                                <option value="">Select Category</option>
                                <option value="explorescreen">🏠 Explore Screen (दैनिक स्वाध्याय)</option>
                                <option value="section">📑 Section (गीत/ढाल संग्रह)</option>
                                <option value="category">📂 Category (भिक्षु स्तुति)</option>
                                <option value="author">👤 Author (आचार्य तुलसी)</option>
                                <option value="title">📖 Title (रु रु मैं संवारियो)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">👤 Author/Creator</label>
                            <input type="text" class="form-input" id="contentAuthor" placeholder="जैसे: आचार्य तुलसी, महावीर स्वामी">
                        </div>

                        <div class="form-group">
                            <label class="form-label">🌐 Language</label>
                            <select class="form-select" id="contentLanguage">
                                <option value="hindi">हिंदी</option>
                                <option value="sanskrit">संस्कृत</option>
                                <option value="english">English</option>
                                <option value="gujarati">गुजराती</option>
                                <option value="rajasthani">राजस्थानी</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">📋 Content Type</label>
                            <select class="form-select" id="contentType">
                                <option value="mantra">🕉️ मंत्र</option>
                                <option value="bhajan">🎵 भजन</option>
                                <option value="story">📚 कथा</option>
                                <option value="teaching">📖 शिक्षा</option>
                                <option value="prayer">🙏 प्रार्थना</option>
                                <option value="stuti">🌟 स्तुति</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">🏷️ Tags (comma separated)</label>
                            <input type="text" class="form-input" id="contentTags" placeholder="जैसे: मंत्र, प्रार्थना, भक्ति">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">📄 Content/Description *</label>
                        <textarea class="form-textarea" id="contentText" placeholder="यहाँ मंत्र, श्लोक, भजन या content लिखें..." required></textarea>
                    </div>

                    <div class="quick-actions">
                        <button type="button" class="btn btn-primary" onclick="addContent()">
                            ✅ Add Content
                        </button>
                        <button type="button" class="btn btn-warning" onclick="clearForm()">
                            🗑️ Clear Form
                        </button>
                        <button type="button" class="btn btn-info" onclick="previewContent()">
                            👀 Preview
                        </button>
                    </div>
                </form>
            </div>

            <!-- Manage Content Section -->
            <div id="manage-content" class="content-section">
                <div class="page-header">
                    <h1 class="page-title">📝 Manage Content</h1>
                    <p class="page-subtitle">Edit, delete, and organize your content</p>
                </div>

                <div class="quick-actions">
                    <input type="text" class="form-input" id="searchContent" placeholder="🔍 Search content..." style="width: 300px;" onkeyup="searchContent()">
                    <select class="form-select" id="filterCategory" onchange="filterContent()" style="width: 200px;">
                        <option value="">All Categories</option>
                        <option value="explorescreen">Explore Screen</option>
                        <option value="section">Section</option>
                        <option value="category">Category</option>
                        <option value="author">Author</option>
                        <option value="title">Title</option>
                    </select>
                    <button class="btn btn-danger" onclick="deleteSelected()">
                        🗑️ Delete Selected
                    </button>
                </div>

                <div id="contentTable">
                    <!-- Content table will be populated here -->
                </div>
            </div>

            <!-- Categories Section -->
            <div id="categories" class="content-section">
                <div class="page-header">
                    <h1 class="page-title">📂 Categories</h1>
                    <p class="page-subtitle">Manage content categories</p>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Category Name</label>
                        <input type="text" class="form-input" id="newCategoryName" placeholder="Enter category name">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Category Description</label>
                        <input type="text" class="form-input" id="newCategoryDesc" placeholder="Enter description">
                    </div>
                </div>

                <button class="btn btn-primary" onclick="addCategory()">
                    ➕ Add Category
                </button>

                <div id="categoriesList" style="margin-top: 30px;">
                    <!-- Categories will be listed here -->
                </div>
            </div>

            <!-- Import/Export Section -->
            <div id="import-export" class="content-section">
                <div class="page-header">
                    <h1 class="page-title">🔄 Import/Export</h1>
                    <p class="page-subtitle">Transfer data to/from your app</p>
                </div>

                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-icon">📤</div>
                        <div class="card-title">Export Data</div>
                        <div class="card-description">Download data for your app</div>
                        <div style="margin-top: 15px;">
                            <button class="btn btn-success" onclick="exportForApp()">
                                📱 Export for App
                            </button>
                            <button class="btn btn-info" onclick="exportBackup()">
                                💾 Export Backup
                            </button>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">📥</div>
                        <div class="card-title">Import Data</div>
                        <div class="card-description">Upload data from file</div>
                        <div style="margin-top: 15px;">
                            <input type="file" id="importFile" accept=".json" style="margin-bottom: 10px;">
                            <button class="btn btn-primary" onclick="importData()">
                                📥 Import Data
                            </button>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">🔗</div>
                        <div class="card-title">Direct Transfer</div>
                        <div class="card-description">Send directly to Firebase</div>
                        <div style="margin-top: 15px;">
                            <button class="btn btn-warning" onclick="setupFirebase()">
                                🔥 Setup Firebase
                            </button>
                            <button class="btn btn-success" onclick="uploadToFirebase()">
                                ☁️ Upload to Cloud
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Section -->
            <div id="settings" class="content-section">
                <div class="page-header">
                    <h1 class="page-title">⚙️ Settings</h1>
                    <p class="page-subtitle">Configure your admin dashboard</p>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">App Name</label>
                        <input type="text" class="form-input" id="appName" value="ArhamApp">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Default Language</label>
                        <select class="form-select" id="defaultLanguage">
                            <option value="hindi">हिंदी</option>
                            <option value="english">English</option>
                            <option value="sanskrit">संस्कृत</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Auto Backup</label>
                        <select class="form-select" id="autoBackup">
                            <option value="daily">Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                            <option value="disabled">Disabled</option>
                        </select>
                    </div>
                </div>

                <div class="quick-actions">
                    <button class="btn btn-primary" onclick="saveSettings()">
                        💾 Save Settings
                    </button>
                    <button class="btn btn-danger" onclick="resetSettings()">
                        🔄 Reset to Default
                    </button>
                    <button class="btn btn-warning" onclick="clearAllData()">
                        🗑️ Clear All Data
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification -->
    <div id="notification" class="notification"></div>

    <script>
        // Global data storage
        let arhamData = [];
        let categories = ['explorescreen', 'section', 'category', 'author', 'title'];
        let settings = {
            appName: 'ArhamApp',
            defaultLanguage: 'hindi',
            autoBackup: 'weekly'
        };

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            loadData();
            updateDashboard();
            loadSettings();
        });

        // Data management functions
        function loadData() {
            const saved = localStorage.getItem('arhamAdminData');
            if (saved) {
                arhamData = JSON.parse(saved);
            }

            const savedCategories = localStorage.getItem('arhamCategories');
            if (savedCategories) {
                categories = JSON.parse(savedCategories);
            }
        }

        function saveData() {
            localStorage.setItem('arhamAdminData', JSON.stringify(arhamData));
            localStorage.setItem('arhamCategories', JSON.stringify(categories));
            updateDashboard();
        }

        function loadSettings() {
            const saved = localStorage.getItem('arhamSettings');
            if (saved) {
                settings = JSON.parse(saved);
                document.getElementById('appName').value = settings.appName;
                document.getElementById('defaultLanguage').value = settings.defaultLanguage;
                document.getElementById('autoBackup').value = settings.autoBackup;
            }
        }

        function saveSettings() {
            settings.appName = document.getElementById('appName').value;
            settings.defaultLanguage = document.getElementById('defaultLanguage').value;
            settings.autoBackup = document.getElementById('autoBackup').value;

            localStorage.setItem('arhamSettings', JSON.stringify(settings));
            showNotification('Settings saved successfully!', 'success');
        }

        // Navigation functions
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // Remove active class from all nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // Show selected section
            document.getElementById(sectionId).classList.add('active');

            // Add active class to clicked nav link
            event.target.classList.add('active');

            // Update content based on section
            if (sectionId === 'manage-content') {
                updateContentTable();
            } else if (sectionId === 'categories') {
                updateCategoriesList();
            }
        }

        // Content management functions
        function addContent() {
            const title = document.getElementById('contentTitle').value.trim();
            const category = document.getElementById('contentCategory').value;
            const author = document.getElementById('contentAuthor').value.trim();
            const language = document.getElementById('contentLanguage').value;
            const type = document.getElementById('contentType').value;
            const tags = document.getElementById('contentTags').value.trim();
            const content = document.getElementById('contentText').value.trim();

            if (!title || !category || !content) {
                showNotification('Please fill in all required fields!', 'error');
                return;
            }

            const newItem = {
                id: 'arham_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                title: title,
                category: category,
                author: author,
                language: language,
                type: type,
                tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag),
                content: content,
                createdAt: Date.now(),
                updatedAt: Date.now()
            };

            arhamData.push(newItem);
            saveData();
            clearForm();
            showNotification('Content added successfully!', 'success');

            // Auto switch to manage content to see the added item
            setTimeout(() => {
                showSection('manage-content');
            }, 1000);
        }

        function clearForm() {
            document.getElementById('contentForm').reset();
            document.getElementById('contentTitle').focus();
        }

        function previewContent() {
            const title = document.getElementById('contentTitle').value.trim();
            const content = document.getElementById('contentText').value.trim();

            if (!title || !content) {
                showNotification('Please enter title and content to preview!', 'error');
                return;
            }

            const preview = `
                <div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); margin: 20px 0;">
                    <h3 style="color: #333; margin-bottom: 15px;">📖 ${title}</h3>
                    <div style="color: #666; line-height: 1.6;">${content}</div>
                </div>
            `;

            const previewWindow = window.open('', '_blank', 'width=600,height=400');
            previewWindow.document.write(`
                <html>
                    <head><title>Content Preview</title></head>
                    <body style="font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5;">
                        <h2>📱 App Preview</h2>
                        ${preview}
                    </body>
                </html>
            `);
        }

        // Dashboard functions
        function updateDashboard() {
            document.getElementById('totalContent').textContent = arhamData.length;

            const uniqueCategories = [...new Set(arhamData.map(item => item.category))];
            document.getElementById('totalCategories').textContent = uniqueCategories.length;

            const uniqueAuthors = [...new Set(arhamData.map(item => item.author).filter(author => author))];
            document.getElementById('totalAuthors').textContent = uniqueAuthors.length;

            const lastItem = arhamData[arhamData.length - 1];
            if (lastItem) {
                const time = new Date(lastItem.createdAt).toLocaleString('hi-IN');
                document.getElementById('lastUpdated').textContent = time;
            }

            updateRecentContent();
        }

        function updateRecentContent() {
            const recentContainer = document.getElementById('recentContent');

            if (arhamData.length === 0) {
                recentContainer.innerHTML = '<p style="text-align: center; color: #666; padding: 40px;">No content yet. Start by adding some spiritual content!</p>';
                return;
            }

            const recentItems = arhamData.slice(-5).reverse();
            let html = '<table class="data-table"><thead><tr><th>Title</th><th>Category</th><th>Author</th><th>Created</th><th>Actions</th></tr></thead><tbody>';

            recentItems.forEach(item => {
                html += `
                    <tr>
                        <td><strong>${item.title}</strong></td>
                        <td><span style="background: #4ECDC4; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">${item.category}</span></td>
                        <td>${item.author || 'Unknown'}</td>
                        <td>${new Date(item.createdAt).toLocaleDateString('hi-IN')}</td>
                        <td>
                            <button class="btn btn-info" style="padding: 5px 10px; font-size: 12px;" onclick="editContent('${item.id}')">✏️ Edit</button>
                            <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;" onclick="deleteContent('${item.id}')">🗑️ Delete</button>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            recentContainer.innerHTML = html;
        }

        // Content table functions
        function updateContentTable() {
            const tableContainer = document.getElementById('contentTable');

            if (arhamData.length === 0) {
                tableContainer.innerHTML = '<p style="text-align: center; color: #666; padding: 40px;">No content available. Add some content first!</p>';
                return;
            }

            let html = `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" onchange="toggleSelectAll(this)"></th>
                            <th>Title</th>
                            <th>Category</th>
                            <th>Author</th>
                            <th>Type</th>
                            <th>Language</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            arhamData.forEach(item => {
                html += `
                    <tr>
                        <td><input type="checkbox" class="content-checkbox" value="${item.id}"></td>
                        <td><strong>${item.title}</strong></td>
                        <td><span style="background: #4ECDC4; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">${item.category}</span></td>
                        <td>${item.author || 'Unknown'}</td>
                        <td>${item.type}</td>
                        <td>${item.language}</td>
                        <td>${new Date(item.createdAt).toLocaleDateString('hi-IN')}</td>
                        <td>
                            <button class="btn btn-info" style="padding: 5px 10px; font-size: 12px;" onclick="editContent('${item.id}')">✏️</button>
                            <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;" onclick="deleteContent('${item.id}')">🗑️</button>
                        </td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            tableContainer.innerHTML = html;
        }

        function searchContent() {
            const searchTerm = document.getElementById('searchContent').value.toLowerCase();
            const rows = document.querySelectorAll('#contentTable tbody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        function filterContent() {
            const filterCategory = document.getElementById('filterCategory').value;
            const rows = document.querySelectorAll('#contentTable tbody tr');

            rows.forEach(row => {
                if (!filterCategory) {
                    row.style.display = '';
                } else {
                    const categoryCell = row.cells[2].textContent.toLowerCase();
                    if (categoryCell.includes(filterCategory)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                }
            });
        }

        function toggleSelectAll(checkbox) {
            const checkboxes = document.querySelectorAll('.content-checkbox');
            checkboxes.forEach(cb => cb.checked = checkbox.checked);
        }

        function deleteSelected() {
            const selected = document.querySelectorAll('.content-checkbox:checked');
            if (selected.length === 0) {
                showNotification('Please select items to delete!', 'error');
                return;
            }

            if (confirm(`Delete ${selected.length} selected items?`)) {
                const idsToDelete = Array.from(selected).map(cb => cb.value);
                arhamData = arhamData.filter(item => !idsToDelete.includes(item.id));
                saveData();
                updateContentTable();
                showNotification(`${selected.length} items deleted successfully!`, 'success');
            }
        }

        // Individual content actions
        function editContent(id) {
            const item = arhamData.find(item => item.id === id);
            if (!item) return;

            // Fill form with existing data
            document.getElementById('contentTitle').value = item.title;
            document.getElementById('contentCategory').value = item.category;
            document.getElementById('contentAuthor').value = item.author || '';
            document.getElementById('contentLanguage').value = item.language;
            document.getElementById('contentType').value = item.type;
            document.getElementById('contentTags').value = item.tags.join(', ');
            document.getElementById('contentText').value = item.content;

            // Switch to add content section
            showSection('add-content');

            // Change button to update mode
            const addButton = document.querySelector('#add-content .btn-primary');
            addButton.textContent = '✅ Update Content';
            addButton.onclick = () => updateContent(id);

            showNotification('Content loaded for editing!', 'info');
        }

        function updateContent(id) {
            const title = document.getElementById('contentTitle').value.trim();
            const category = document.getElementById('contentCategory').value;
            const author = document.getElementById('contentAuthor').value.trim();
            const language = document.getElementById('contentLanguage').value;
            const type = document.getElementById('contentType').value;
            const tags = document.getElementById('contentTags').value.trim();
            const content = document.getElementById('contentText').value.trim();

            if (!title || !category || !content) {
                showNotification('Please fill in all required fields!', 'error');
                return;
            }

            const itemIndex = arhamData.findIndex(item => item.id === id);
            if (itemIndex !== -1) {
                arhamData[itemIndex] = {
                    ...arhamData[itemIndex],
                    title: title,
                    category: category,
                    author: author,
                    language: language,
                    type: type,
                    tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag),
                    content: content,
                    updatedAt: Date.now()
                };

                saveData();
                clearForm();

                // Reset button to add mode
                const addButton = document.querySelector('#add-content .btn-primary');
                addButton.textContent = '✅ Add Content';
                addButton.onclick = addContent;

                showNotification('Content updated successfully!', 'success');
                showSection('manage-content');
            }
        }

        function deleteContent(id) {
            const item = arhamData.find(item => item.id === id);
            if (!item) return;

            if (confirm(`Delete "${item.title}"?`)) {
                arhamData = arhamData.filter(item => item.id !== id);
                saveData();
                updateContentTable();
                showNotification('Content deleted successfully!', 'success');
            }
        }

        // Export/Import functions
        function exportForApp() {
            if (arhamData.length === 0) {
                showNotification('No data to export!', 'error');
                return;
            }

            const appFormat = {
                spiritual_content: arhamData,
                metadata: {
                    version: '1.0',
                    totalItems: arhamData.length,
                    exportedAt: Date.now(),
                    categories: [...new Set(arhamData.map(item => item.category))],
                    appName: settings.appName
                }
            };

            downloadJSON(appFormat, `${settings.appName}_data_${new Date().toISOString().split('T')[0]}.json`);
            showNotification('Data exported for app successfully!', 'success');
        }

        function exportBackup() {
            const backupData = {
                arhamData: arhamData,
                categories: categories,
                settings: settings,
                exportedAt: Date.now(),
                version: '1.0'
            };

            downloadJSON(backupData, `${settings.appName}_backup_${new Date().toISOString().split('T')[0]}.json`);
            showNotification('Backup created successfully!', 'success');
        }

        function downloadJSON(data, filename) {
            const blob = new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'});
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function importData() {
            const fileInput = document.getElementById('importFile');
            const file = fileInput.files[0];

            if (!file) {
                showNotification('Please select a file to import!', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const importedData = JSON.parse(e.target.result);

                    if (importedData.arhamData) {
                        // Backup format
                        arhamData = importedData.arhamData;
                        categories = importedData.categories || categories;
                        settings = importedData.settings || settings;
                    } else if (importedData.spiritual_content) {
                        // App format
                        arhamData = importedData.spiritual_content;
                    } else if (Array.isArray(importedData)) {
                        // Simple array format
                        arhamData = importedData;
                    } else {
                        throw new Error('Invalid file format');
                    }

                    saveData();
                    loadSettings();
                    showNotification('Data imported successfully!', 'success');

                } catch (error) {
                    showNotification('Error importing data: ' + error.message, 'error');
                }
            };
            reader.readAsText(file);
        }

        // Sample data and utility functions
        function loadSampleData() {
            const sampleData = [
                {
                    id: 'sample_1',
                    title: 'गायत्री मंत्र',
                    category: 'title',
                    author: 'वेद व्यास',
                    language: 'sanskrit',
                    type: 'mantra',
                    tags: ['मंत्र', 'प्रार्थना', 'वेद'],
                    content: 'ॐ भूर्भुवः स्वः तत्सवितुर्वरेण्यं भर्गो देवस्य धीमहि धियो यो नः प्रचोदयात्',
                    createdAt: Date.now(),
                    updatedAt: Date.now()
                },
                {
                    id: 'sample_2',
                    title: 'हनुमान चालीसा',
                    category: 'title',
                    author: 'तुलसीदास',
                    language: 'hindi',
                    type: 'bhajan',
                    tags: ['भजन', 'हनुमान', 'चालीसा'],
                    content: 'श्रीगुरु चरन सरोज रज निज मनु मुकुरु सुधारि। बरनउं रघुबर बिमल जसु जो दायकु फल चारि।।',
                    createdAt: Date.now() - 86400000,
                    updatedAt: Date.now() - 86400000
                },
                {
                    id: 'sample_3',
                    title: 'दैनिक स्वाध्याय',
                    category: 'explorescreen',
                    author: 'आचार्य तुलसी',
                    language: 'hindi',
                    type: 'teaching',
                    tags: ['स्वाध्याय', 'शिक्षा', 'दैनिक'],
                    content: 'प्रतिदिन स्वाध्याय करना आत्मा की शुद्धता के लिए आवश्यक है।',
                    createdAt: Date.now() - 172800000,
                    updatedAt: Date.now() - 172800000
                }
            ];

            arhamData = [...arhamData, ...sampleData];
            saveData();
            showNotification('Sample data loaded successfully!', 'success');
        }

        function backupData() {
            exportBackup();
        }

        function clearAllData() {
            if (confirm('Are you sure you want to delete ALL data? This cannot be undone!')) {
                arhamData = [];
                saveData();
                showNotification('All data cleared!', 'success');
            }
        }

        function resetSettings() {
            if (confirm('Reset all settings to default?')) {
                settings = {
                    appName: 'ArhamApp',
                    defaultLanguage: 'hindi',
                    autoBackup: 'weekly'
                };
                localStorage.setItem('arhamSettings', JSON.stringify(settings));
                loadSettings();
                showNotification('Settings reset to default!', 'success');
            }
        }

        // Categories management
        function addCategory() {
            const name = document.getElementById('newCategoryName').value.trim();
            const desc = document.getElementById('newCategoryDesc').value.trim();

            if (!name) {
                showNotification('Please enter category name!', 'error');
                return;
            }

            if (!categories.includes(name.toLowerCase())) {
                categories.push(name.toLowerCase());
                saveData();
                updateCategoriesList();
                document.getElementById('newCategoryName').value = '';
                document.getElementById('newCategoryDesc').value = '';
                showNotification('Category added successfully!', 'success');
            } else {
                showNotification('Category already exists!', 'error');
            }
        }

        function updateCategoriesList() {
            const container = document.getElementById('categoriesList');
            let html = '<div class="dashboard-grid">';

            categories.forEach(category => {
                const count = arhamData.filter(item => item.category === category).length;
                html += `
                    <div class="dashboard-card">
                        <div class="card-title">${category}</div>
                        <div class="card-value">${count}</div>
                        <div class="card-description">items</div>
                        <button class="btn btn-danger" style="margin-top: 10px;" onclick="deleteCategory('${category}')">
                            🗑️ Delete
                        </button>
                    </div>
                `;
            });

            html += '</div>';
            container.innerHTML = html;
        }

        function deleteCategory(categoryName) {
            const count = arhamData.filter(item => item.category === categoryName).length;
            if (count > 0) {
                if (!confirm(`This category has ${count} items. Delete anyway?`)) {
                    return;
                }
            }

            categories = categories.filter(cat => cat !== categoryName);
            saveData();
            updateCategoriesList();
            showNotification('Category deleted!', 'success');
        }

        // Firebase integration placeholders
        function setupFirebase() {
            showNotification('Firebase setup feature coming soon!', 'info');
        }

        function uploadToFirebase() {
            showNotification('Firebase upload feature coming soon!', 'info');
        }

        // Notification system
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type} show`;

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey) {
                switch(e.key) {
                    case 's':
                        e.preventDefault();
                        if (document.getElementById('add-content').classList.contains('active')) {
                            addContent();
                        }
                        break;
                    case 'n':
                        e.preventDefault();
                        showSection('add-content');
                        break;
                    case 'e':
                        e.preventDefault();
                        exportForApp();
                        break;
                }
            }
        });
    </script>
</body>
</html>