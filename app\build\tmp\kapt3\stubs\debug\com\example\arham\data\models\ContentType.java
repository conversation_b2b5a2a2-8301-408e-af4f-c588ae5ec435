package com.example.arham.data.models;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\n\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\n\u00a8\u0006\u000b"}, d2 = {"Lcom/example/arham/data/models/ContentType;", "", "(Ljava/lang/String;I)V", "<PERSON><PERSON><PERSON><PERSON>", "GEET", "STORY", "TEACHING", "<PERSON>AY<PERSON>", "MANT<PERSON>", "SUTRA", "BIOGRAPHY", "app_debug"})
public enum ContentType {
    /*public static final*/ BHAJAN /* = new BHAJAN() */,
    /*public static final*/ GEET /* = new GEET() */,
    /*public static final*/ STORY /* = new STORY() */,
    /*public static final*/ TEACHING /* = new TEACHING() */,
    /*public static final*/ PRAYER /* = new PRAYER() */,
    /*public static final*/ MANTRA /* = new MANTRA() */,
    /*public static final*/ SUTRA /* = new SUTRA() */,
    /*public static final*/ BIOGRAPHY /* = new BIOGRAPHY() */;
    
    ContentType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.arham.data.models.ContentType> getEntries() {
        return null;
    }
}