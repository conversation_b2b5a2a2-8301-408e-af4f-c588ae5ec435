package com.example.arham.domain.repository

import com.example.arham.domain.models.Habit
import com.example.arham.domain.models.HabitCompletion
import com.example.arham.domain.models.HabitWithCompletions
import kotlinx.coroutines.flow.Flow

/**
 * Domain layer repository interface for habits
 * Clean architecture - Repository pattern
 */
interface HabitRepository {
    
    // Habit CRUD operations
    suspend fun getAllHabits(): List<Habit>
    suspend fun getHabitById(id: String): Habit?
    suspend fun addHabit(habit: Habit): Result<Unit>
    suspend fun updateHabit(habit: Habit): Result<Unit>
    suspend fun deleteHabit(id: String): Result<Unit>
    
    // Habit completions
    suspend fun getHabitCompletions(habitId: String): List<HabitCompletion>
    suspend fun addHabitCompletion(completion: HabitCompletion): Result<Unit>
    suspend fun removeHabitCompletion(completionId: String): Result<Unit>
    suspend fun getTodayCompletion(habitId: String): HabitCompletion?
    
    // Combined data
    suspend fun getHabitsWithCompletions(): List<HabitWithCompletions>
    suspend fun getHabitWithCompletions(habitId: String): HabitWithCompletions?
    
    // Reactive streams
    fun getHabitsFlow(): Flow<List<Habit>>
    fun getHabitCompletionsFlow(habitId: String): Flow<List<HabitCompletion>>
    
    // Analytics
    suspend fun calculateStreak(habitId: String): Int
    suspend fun getHabitStats(habitId: String): Map<String, Any>
}
