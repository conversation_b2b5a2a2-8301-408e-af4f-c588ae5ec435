package com.example.arham.ui.theme

import androidx.compose.material3.Typography
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import com.example.arham.R

val amitaFamily = FontFamily(
    Font(R.font.amita_bold, FontWeight.Bold)
)

val kalamFamily = FontFamily(
    Font(R.font.kalam_light, FontWeight.Normal)
)

val eczarFamily = FontFamily(
    Font(R.font.eczar_regular, FontWeight.Normal),
    Font(R.font.eczar_medium, FontWeight.Medium)
)

// Set of Material typography styles to start with
val Typography = Typography(
    bodyLarge = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 16.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.5.sp
    ),
    titleLarge = TextStyle(
        fontFamily = amitaFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 48.sp,
        lineHeight = 56.sp,
        letterSpacing = 0.sp
    ),
    bodyMedium = TextStyle(
        fontFamily = kalamFamily,
        fontWeight = FontWeight.Normal,
        fontSize = 24.sp,
        lineHeight = 32.sp,
        letterSpacing = 0.5.sp
    ),
    headlineMedium = TextStyle(
        fontFamily = eczarFamily,
        fontWeight = FontWeight.Normal,
        fontSize = 36.sp, // Slightly smaller than 48.sp (home screen logo)
        lineHeight = 40.sp,
        letterSpacing = 0.sp
    ),
    headlineSmall = TextStyle(
        fontFamily = eczarFamily,
        fontWeight = FontWeight.Normal,
        fontSize = 20.sp, // Similar to card text on home screen
        lineHeight = 24.sp,
        letterSpacing = 0.sp
    )
)
