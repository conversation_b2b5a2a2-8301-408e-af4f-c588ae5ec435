package com.example.arham.ui.navigation

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.AccountCircle
import androidx.compose.material.icons.outlined.BookmarkBorder
import androidx.compose.material.icons.outlined.CalendarToday
import androidx.compose.material.icons.outlined.Explore
import androidx.compose.material.icons.outlined.Search
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.example.arham.ui.screens.AdhyatmaYatraScreen
import com.example.arham.ui.screens.DishaYantraScreen
import com.example.arham.ui.screens.TodayScreen
import com.example.arham.ui.screens.SadhanaTrackerScreen
import com.example.arham.ui.screens.HabitDetailScreen
import com.example.arham.ui.screens.AddHabitScreen
import com.example.arham.ui.screens.HabitOnboardingScreen
import com.example.arham.ui.screens.SearchScreen
import com.example.arham.ui.screens.BookmarkScreen
import com.example.arham.ui.screens.AuthScreen
import com.example.arham.ui.screens.ProfileScreen
import com.example.arham.ui.screens.BhajanEReaderScreen
import com.example.arham.ui.screens.UniversalContentScreen
import com.example.arham.ui.screens.DataUploadScreen



@Composable
fun AppNavigation(isDarkMode: Boolean, onToggleTheme: () -> Unit) {
    val navController = rememberNavController()
    var showSearchOverlay by remember { mutableStateOf(false) }



    Box(
        modifier = Modifier
            .fillMaxSize()
            .windowInsetsPadding(WindowInsets.systemBars) // Handle system bar insets
    ) {
        // Main app content with conditional blur
        Scaffold(
        containerColor = MaterialTheme.colorScheme.background,
        contentWindowInsets = WindowInsets(0), // Remove default window insets
        modifier = if (showSearchOverlay) {
            Modifier.blur(radius = 40.dp) // Perfect blur for glassmorphism effect
        } else {
            Modifier
        },
        bottomBar = {
            val currentRouteInBottomBar = navController.currentBackStackEntryAsState().value?.destination?.route
            if (currentRouteInBottomBar != "auth" && currentRouteInBottomBar?.startsWith("ereader/bhajan/") != true) {
                Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp)
                    .background(MaterialTheme.colorScheme.background), // Use theme-based transparent color
                horizontalArrangement = Arrangement.SpaceAround,
                verticalAlignment = Alignment.CenterVertically
            ) {
                BottomNavItem(
                    icon = Icons.Outlined.CalendarToday,
                    label = "Today",
                    selected = currentRouteInBottomBar == "today",
                    onClick = { navController.navigate("today") }
                )
                BottomNavItem(
                    icon = Icons.Outlined.Explore,
                    label = "Explore",
                    selected = currentRouteInBottomBar == "explore",
                    onClick = { navController.navigate("adhyatmayatra") }
                )
                BottomNavItem(
                    icon = Icons.Outlined.Search,
                    label = "Search",
                    selected = showSearchOverlay,
                    onClick = { showSearchOverlay = true } // Single tap to open
                )
                BottomNavItem(
                    icon = Icons.Outlined.BookmarkBorder,
                    label = "Bookmark",
                    selected = currentRouteInBottomBar == "bookmark",
                    onClick = { navController.navigate("bookmark") }
                )
                BottomNavItem(
                    icon = Icons.Outlined.AccountCircle,
                    label = "Profile",
                    selected = currentRouteInBottomBar == "profile",
                    onClick = { navController.navigate("profile") }
                )
            }
            }
        }
    ) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = "today",
            modifier = Modifier
                .fillMaxSize()
                .padding(bottom = innerPadding.calculateBottomPadding()) // Apply innerPadding here
        ) {
            composable("auth") { AuthScreen(navController, isDarkMode) }
            composable("home") {
                // Navigate to today screen after authentication
                LaunchedEffect(Unit) {
                    navController.navigate("today") {
                        popUpTo("auth") { inclusive = true }
                    }
                }
            }
            composable("today") { TodayScreen(navController, isDarkMode, onToggleTheme) }
            composable("sadhanatracker") { SadhanaTrackerScreen(navController, isDarkMode) }
            composable("habitdetail/{habitId}") { backStackEntry ->
                val habitId = backStackEntry.arguments?.getString("habitId") ?: "1"
                HabitDetailScreen(navController, habitId, isDarkMode)
            }
            composable("addhabit") { AddHabitScreen(navController, isDarkMode) }
            composable("onboarding") { HabitOnboardingScreen(navController, isDarkMode) }

            composable("adhyatmayatra") { AdhyatmaYatraScreen(navController, isDarkMode, onToggleTheme) }
            // Universal Content Screen routes
            composable("readscreen") { UniversalContentScreen(navController, "READ_SCREEN") }
            composable("geet") { UniversalContentScreen(navController, "GEET") }
            composable("mahaprajna") { UniversalContentScreen(navController, "MAHAPRAJNA") }
            composable("kalu") { UniversalContentScreen(navController, "KALU") }
            composable("tulsi") { UniversalContentScreen(navController, "TULSI") }
            composable("bhikshu") { UniversalContentScreen(navController, "BHIKSHU") }
            composable("mangal") { UniversalContentScreen(navController, "MANGAL") }

            // Adhyatma Yatra specific routes
            composable("dainik_swadhyay") { UniversalContentScreen(navController, "DAINIK_SWADHYAY") }
            composable("adhyayan_smaran") { UniversalContentScreen(navController, "ADHYAYAN_SMARAN") }

            // Generic content route with parameter
            composable("content/{category}") { backStackEntry ->
                val category = backStackEntry.arguments?.getString("category") ?: "GENERAL"
                UniversalContentScreen(navController, category)
            }

            composable("dishayantra") { DishaYantraScreen(isDarkMode, onToggleTheme) }

            composable("explore") {
                AdhyatmaYatraScreen(
                    navController = navController,
                    isDarkMode = isDarkMode,
                    onToggleTheme = onToggleTheme
                )
            }
            composable("bookmark") { BookmarkScreen(navController, isDarkMode) }
            composable("profile") { ProfileScreen(navController, isDarkMode) }
            composable("data_upload") { DataUploadScreen(navController, isDarkMode) }
            composable("ereader/bhajan/{title}") { backStackEntry ->
                val encodedTitle = backStackEntry.arguments?.getString("title") ?: ""
                val title = java.net.URLDecoder.decode(encodedTitle, "UTF-8")
                BhajanEReaderScreen(navController, title, isDarkMode)
            }
        }
        }

        // True overlay that appears on top of current content
        if (showSearchOverlay) {
            SearchScreen(
                onClose = { showSearchOverlay = false },
                isDarkMode = isDarkMode
            )
        }
    }
}

@Composable
fun BottomNavItem(icon: ImageVector, label: String, selected: Boolean, onClick: () -> Unit) {
    val scale = remember { Animatable(1f) }

    LaunchedEffect(selected) {
        if (selected) {
            scale.animateTo(1.2f, animationSpec = tween(100))
            scale.animateTo(1f, animationSpec = tween(100))
        }
    }

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.clickable(onClick = onClick)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            tint = if (selected) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onBackground,
            modifier = Modifier
                .size(28.dp) // Changed from 30.dp to 28.dp
                .graphicsLayer(
                    scaleX = scale.value,
                    scaleY = scale.value
                )
        )
    }
}
